-- Create quarterly_outlook table for storing quarterly market outlook data
CREATE TABLE quarterly_outlook (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    quarter TEXT NOT NULL CHECK (quarter IN ('Q1', 'Q2', 'Q3', 'Q4')),
    year INTEGER NOT NULL,
    market TEXT NOT NULL CHECK (market IN ('us', 'india')),
    summary TEXT NOT NULL,
    key_metrics JSONB DEFAULT '{}',
    detailed_analysis TEXT NOT NULL,
    risk_factors TEXT[] DEFAULT '{}',
    opportunities TEXT[] DEFAULT '{}',
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX idx_quarterly_outlook_status ON quarterly_outlook(status);
CREATE INDEX idx_quarterly_outlook_market ON quarterly_outlook(market);
CREATE INDEX idx_quarterly_outlook_year_quarter ON quarterly_outlook(year, quarter);
CREATE INDEX idx_quarterly_outlook_published_at ON quarterly_outlook(published_at);

-- Create RLS policies
ALTER TABLE quarterly_outlook ENABLE ROW LEVEL SECURITY;

-- Policy for reading published content (public access)
CREATE POLICY "Allow public read access to published quarterly outlook" ON quarterly_outlook
    FOR SELECT USING (status = 'published');

-- Policy for authenticated users to manage content (simplified for now)
CREATE POLICY "Allow authenticated users full access to quarterly outlook" ON quarterly_outlook
    FOR ALL USING (auth.role() = 'authenticated');

-- Policy for authors to manage their own content
CREATE POLICY "Allow authors to manage their own quarterly outlook" ON quarterly_outlook
    FOR ALL USING (author_id = auth.uid());

-- Create trigger for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_quarterly_outlook_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER quarterly_outlook_updated_at
    BEFORE UPDATE ON quarterly_outlook
    FOR EACH ROW
    EXECUTE FUNCTION update_quarterly_outlook_updated_at();

-- Insert sample data
INSERT INTO quarterly_outlook (
    title, quarter, year, market, summary, key_metrics, detailed_analysis, 
    risk_factors, opportunities, status, published_at
) VALUES 
(
    'Q4 2024 US Market Outlook',
    'Q4',
    2024,
    'us',
    'Strong economic fundamentals with cautious optimism amid global uncertainties.',
    '{
        "gdp_growth": 2.8,
        "inflation_rate": 3.2,
        "interest_rate": 5.25,
        "market_outlook": "bullish",
        "sector_highlights": ["Technology", "Healthcare", "Financial Services"]
    }',
    'The US market shows resilience with strong corporate earnings and consumer spending. Technology sector continues to lead with AI innovations, while healthcare benefits from demographic trends. Financial services sector remains robust with stable interest rate environment. However, geopolitical tensions and supply chain concerns require careful monitoring.',
    ARRAY['Geopolitical tensions', 'Inflation concerns', 'Supply chain disruptions', 'Interest rate volatility'],
    ARRAY['AI and Technology growth', 'Infrastructure investments', 'Green energy transition', 'Healthcare innovation'],
    'published',
    NOW()
),
(
    'Q1 2025 US Market Outlook',
    'Q1',
    2025,
    'us',
    'Continued growth momentum with focus on emerging technologies and sustainable investments.',
    '{
        "gdp_growth": 3.1,
        "inflation_rate": 2.9,
        "interest_rate": 5.0,
        "market_outlook": "bullish",
        "sector_highlights": ["Artificial Intelligence", "Renewable Energy", "Biotechnology"]
    }',
    'Q1 2025 presents opportunities in emerging sectors with strong fundamentals. Artificial Intelligence continues to drive innovation across industries, while renewable energy sector benefits from policy support and technological advances. Biotechnology shows promise with breakthrough treatments and personalized medicine.',
    ARRAY['Market volatility', 'Regulatory changes', 'Global economic slowdown', 'Technology disruption risks'],
    ARRAY['Tech innovation', 'ESG investments', 'Healthcare advancements', 'Digital transformation'],
    'published',
    NOW()
),
(
    'Q4 2024 Indian Market Outlook',
    'Q4',
    2024,
    'india',
    'Robust domestic demand driving growth with strong manufacturing and services sectors.',
    '{
        "gdp_growth": 6.8,
        "inflation_rate": 4.5,
        "interest_rate": 6.5,
        "market_outlook": "bullish",
        "sector_highlights": ["Manufacturing", "IT Services", "Financial Services"]
    }',
    'India''s economy continues to show strong fundamentals with domestic consumption driving growth. Manufacturing sector benefits from government initiatives and global supply chain diversification. IT services remain competitive globally, while financial services sector shows healthy credit growth and digital adoption.',
    ARRAY['Monsoon dependency', 'Global trade tensions', 'Commodity price volatility', 'Infrastructure bottlenecks'],
    ARRAY['Digital transformation', 'Infrastructure development', 'Manufacturing growth', 'Financial inclusion'],
    'published',
    NOW()
),
(
    'Q1 2025 Indian Market Outlook',
    'Q1',
    2025,
    'india',
    'Sustained growth trajectory with focus on digital economy and sustainable development.',
    '{
        "gdp_growth": 7.2,
        "inflation_rate": 4.2,
        "interest_rate": 6.25,
        "market_outlook": "bullish",
        "sector_highlights": ["Digital Economy", "Green Energy", "Healthcare"]
    }',
    'Q1 2025 outlook remains positive with strong policy support and investment flows. Digital economy initiatives continue to drive innovation and efficiency. Green energy sector shows significant potential with government targets and private sector participation. Healthcare sector benefits from increased awareness and infrastructure development.',
    ARRAY['External headwinds', 'Fiscal deficit concerns', 'Climate risks', 'Regulatory uncertainties'],
    ARRAY['Digital India initiatives', 'Renewable energy expansion', 'Healthcare innovation', 'Export opportunities'],
    'published',
    NOW()
);

-- Grant necessary permissions
GRANT SELECT ON quarterly_outlook TO anon;
GRANT ALL ON quarterly_outlook TO authenticated;
