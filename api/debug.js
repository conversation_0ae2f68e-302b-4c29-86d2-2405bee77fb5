export default function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // <PERSON>le preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  return res.status(200).json({
    message: 'Debug endpoint',
    request: {
      url: req.url,
      method: req.method,
      query: req.query,
      headers: req.headers,
      body: req.body
    },
    environment: {
      hasApiKey: !!(process.env.VITE_FINNHUB_API_KEY || process.env.FINNHUB_API_KEY),
      nodeEnv: process.env.NODE_ENV,
      vercelEnv: process.env.VERCEL_ENV
    }
  });
}
