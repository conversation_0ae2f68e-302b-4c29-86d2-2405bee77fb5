"""
Vercel serverless function for market indices
Optimized for Indian market indices
"""

from http.server import BaseHTTPRequestHandler
import json
import yfinance as yf
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests for market indices"""
        try:
            # Indian market indices symbols
            indian_indices = {
                '^NSEI': 'NIFTY 50',
                '^BSESN': 'BSE SENSEX', 
                '^NSEBANK': 'NIFTY BANK',
                '^NSEIT': 'NIFTY IT'
            }
            
            logger.info("Fetching Indian market indices")
            
            # Fetch indices data in parallel
            indices_data = self.fetch_indices_parallel(indian_indices)
            
            # Send response
            self.send_json_response(indices_data)
            
        except Exception as e:
            logger.error(f"Error in indices handler: {e}")
            self.send_error_response(500, str(e))
    
    def fetch_indices_parallel(self, indices_symbols: dict) -> list:
        """Fetch multiple indices in parallel"""
        results = []
        
        # Use ThreadPoolExecutor for parallel requests
        with ThreadPoolExecutor(max_workers=4) as executor:
            # Submit all tasks
            future_to_symbol = {
                executor.submit(self.fetch_single_index, symbol, name): symbol 
                for symbol, name in indices_symbols.items()
            }
            
            # Collect results as they complete
            for future in as_completed(future_to_symbol, timeout=8):  # 8 second timeout
                symbol = future_to_symbol[future]
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.error(f"Error fetching {symbol}: {e}")
                    # Continue with other indices
        
        return results
    
    def fetch_single_index(self, symbol: str, name: str) -> dict:
        """Fetch single index data"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Extract key data
            current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
            previous_close = info.get('previousClose', current_price)
            change = current_price - previous_close if current_price and previous_close else 0
            change_percent = (change / previous_close * 100) if previous_close else 0
            
            return {
                'symbol': symbol,
                'name': name,
                'price': float(current_price) if current_price else 0,
                'change': float(change),
                'changePercent': float(change_percent),
                'high': float(info.get('dayHigh', current_price)) if info.get('dayHigh') else float(current_price),
                'low': float(info.get('dayLow', current_price)) if info.get('dayLow') else float(current_price),
                'open': float(info.get('open', current_price)) if info.get('open') else float(current_price),
                'previousClose': float(previous_close) if previous_close else 0,
                'volume': int(info.get('volume', 0)) if info.get('volume') else 0,
                'currency': 'INR',
                'exchange': info.get('exchange', 'NSE'),
                'timestamp': int(time.time()),
                'marketState': info.get('marketState', 'REGULAR')
            }
            
        except Exception as e:
            logger.error(f"Error fetching {symbol}: {e}")
            return None
    
    def send_json_response(self, data):
        """Send JSON response with CORS headers"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        response_json = json.dumps(data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_error_response(self, status_code, message):
        """Send error response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = json.dumps({
            'error': message,
            'status': status_code,
            'timestamp': int(time.time())
        })
        self.wfile.write(error_response.encode('utf-8'))
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
