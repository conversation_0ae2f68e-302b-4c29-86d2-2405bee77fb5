const FINNHUB_BASE_URL = 'https://finnhub.io/api/v1';

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Get API key from environment variables
  const API_KEY = process.env.VITE_FINNHUB_API_KEY || process.env.FINNHUB_API_KEY;
  
  if (!API_KEY) {
    console.error('Finnhub API key not configured');
    return res.status(500).json({ 
      error: 'Finnhub API key not configured',
      message: 'Please set VITE_FINNHUB_API_KEY or FINNHUB_API_KEY environment variable'
    });
  }

  // The `path` parameter comes from the file name `[...path].js`
  // In Vercel, this will be an array of path segments
  let pathArray = req.query.path;

  // Debug logging
  console.log('Raw path from query:', pathArray);
  console.log('Request URL:', req.url);

  // Handle different path formats
  if (!pathArray) {
    // Fallback: extract from URL if path query param is missing
    const urlPath = req.url?.split('/api/finnhub/')[1]?.split('?')[0];
    if (urlPath) {
      pathArray = urlPath.split('/').filter(Boolean);
    }
  }

  // Ensure pathArray is an array
  if (!Array.isArray(pathArray)) {
    pathArray = pathArray ? [pathArray] : [];
  }

  const finnhubApiEndpoint = pathArray.filter(Boolean).join('/');

  console.log('Processed path array:', pathArray);
  console.log('Final endpoint:', finnhubApiEndpoint);

  if (!finnhubApiEndpoint) {
    return res.status(400).json({
      error: 'Invalid API endpoint',
      debug: {
        rawPath: req.query.path,
        processedPath: pathArray,
        url: req.url,
        query: req.query
      }
    });
  }

  // Reconstruct the query parameters from the original request
  const queryParams = { ...req.query };
  delete queryParams.path; // remove the path parameter itself

  const finnhubUrl = `${FINNHUB_BASE_URL}/${finnhubApiEndpoint}`;

  // Build query parameters manually to handle all types
  const params = new URLSearchParams();
  Object.entries(queryParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, String(value));
    }
  });
  params.append('token', API_KEY);

  try {
    console.log(`Proxying request to: ${finnhubUrl}?${params.toString()}`);
    
    const response = await fetch(`${finnhubUrl}?${params.toString()}`, {
      method: req.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Vercel-Serverless-Function',
      },
    });

    if (!response.ok) {
      throw new Error(`Finnhub API responded with status: ${response.status}`);
    }

    const data = await response.json();
    
    // Forward the response from Finnhub back to the client
    return res.status(200).json(data);
  } catch (error) {
    console.error('Error proxying to Finnhub:', error.message);
    return res.status(error.response?.status || 500).json({ 
      error: 'Proxy Error', 
      message: error.message,
      endpoint: finnhubApiEndpoint
    });
  }
}
