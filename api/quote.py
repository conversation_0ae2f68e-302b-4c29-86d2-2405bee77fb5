"""
Vercel serverless function for stock quotes
Optimized for Vercel's Python runtime
"""

from http.server import BaseHTTPRequestHandler
import json
import yfinance as yf
import time
from urllib.parse import parse_qs, urlparse
import os
import logging

# Configure logging for Vercel
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Handle GET requests for stock quotes"""
        try:
            # Parse URL and get symbol
            parsed_url = urlparse(self.path)
            path_parts = parsed_url.path.strip('/').split('/')
            
            if len(path_parts) < 2 or path_parts[0] != 'quote':
                self.send_error_response(400, "Invalid path. Use /api/quote/{symbol}")
                return
            
            symbol = path_parts[1].upper()
            logger.info(f"Fetching quote for {symbol}")
            
            # Fetch stock data
            quote_data = self.fetch_stock_quote(symbol)
            
            # Send successful response
            self.send_json_response(quote_data)
            
        except Exception as e:
            logger.error(f"Error in quote handler: {e}")
            self.send_error_response(500, str(e))
    
    def do_POST(self):
        """Handle POST requests for batch quotes"""
        try:
            # Read request body
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            request_data = json.loads(post_data.decode('utf-8'))
            
            symbols = request_data.get('symbols', [])
            if not symbols:
                self.send_error_response(400, "No symbols provided")
                return
            
            logger.info(f"Batch request for {len(symbols)} symbols")
            
            # Process batch request
            results = []
            for symbol in symbols[:10]:  # Limit to 10 symbols for Vercel timeout
                try:
                    quote_data = self.fetch_stock_quote(symbol)
                    results.append({
                        'symbol': symbol,
                        'data': quote_data,
                        'error': None
                    })
                except Exception as e:
                    results.append({
                        'symbol': symbol,
                        'data': None,
                        'error': str(e)
                    })
            
            self.send_json_response(results)
            
        except Exception as e:
            logger.error(f"Error in batch handler: {e}")
            self.send_error_response(500, str(e))
    
    def fetch_stock_quote(self, symbol: str) -> dict:
        """Fetch stock quote using yfinance"""
        try:
            ticker = yf.Ticker(symbol)
            
            # Get basic info with timeout
            info = ticker.info
            
            # Extract key data points
            current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
            previous_close = info.get('previousClose', current_price)
            change = current_price - previous_close if current_price and previous_close else 0
            change_percent = (change / previous_close * 100) if previous_close else 0
            
            return {
                'symbol': symbol,
                'name': info.get('shortName', symbol),
                'price': float(current_price) if current_price else 0,
                'change': float(change),
                'changePercent': float(change_percent),
                'high': float(info.get('dayHigh', current_price)) if info.get('dayHigh') else float(current_price),
                'low': float(info.get('dayLow', current_price)) if info.get('dayLow') else float(current_price),
                'open': float(info.get('open', current_price)) if info.get('open') else float(current_price),
                'previousClose': float(previous_close) if previous_close else 0,
                'volume': int(info.get('volume', 0)) if info.get('volume') else 0,
                'currency': info.get('currency', 'USD'),
                'exchange': info.get('exchange', ''),
                'timestamp': int(time.time()),
                'marketState': info.get('marketState', 'REGULAR')
            }
            
        except Exception as e:
            logger.error(f"Error fetching {symbol}: {e}")
            raise Exception(f"Failed to fetch data for {symbol}: {str(e)}")
    
    def send_json_response(self, data):
        """Send JSON response with CORS headers"""
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        response_json = json.dumps(data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_error_response(self, status_code, message):
        """Send error response with CORS headers"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        error_response = json.dumps({
            'error': message,
            'status': status_code,
            'timestamp': int(time.time())
        })
        self.wfile.write(error_response.encode('utf-8'))
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
