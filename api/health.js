export default function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // <PERSON>le preflight requests
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  const hasApiKey = !!(process.env.VITE_FINNHUB_API_KEY || process.env.FINNHUB_API_KEY);
  
  return res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development',
    hasApiKey,
    message: 'Vercel serverless functions are working correctly'
  });
}
