"""
Health check endpoint for Vercel API
"""

from http.server import BaseHTTPRequestHandler
import json
import time

class handler(BaseHTTPRequestHandler):
    def do_GET(self):
        """Health check endpoint"""
        try:
            health_data = {
                'status': 'healthy',
                'service': 'Finance Data API (Vercel)',
                'timestamp': int(time.time()),
                'version': '1.0.0',
                'environment': 'vercel-serverless'
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response_json = json.dumps(health_data, indent=2)
            self.wfile.write(response_json.encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            error_response = json.dumps({
                'status': 'error',
                'message': str(e),
                'timestamp': int(time.time())
            })
            self.wfile.write(error_response.encode('utf-8'))
