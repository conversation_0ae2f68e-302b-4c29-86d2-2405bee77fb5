# Deployment Guide for Syed Finance Compass

## 🚀 Vercel Deployment Setup

### 1. Environment Variables

Set these environment variables in your Vercel dashboard:

```bash
# Required: Finnhub API Key
VITE_FINNHUB_API_KEY=your_finnhub_api_key_here
FINNHUB_API_KEY=your_finnhub_api_key_here

# Required: Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 2. Vercel Configuration

The project includes a `vercel.json` file with the following configuration:

- **Serverless Functions**: API routes are handled by Vercel serverless functions
- **CORS Headers**: Properly configured for API access
- **Rewrites**: API routes are properly routed to serverless functions

### 3. API Endpoints

#### Health Check
- **URL**: `/api/health`
- **Purpose**: Verify serverless functions are working
- **Response**: System status and configuration info

#### Finnhub Proxy
- **URL**: `/api/finnhub/[...path]`
- **Purpose**: Proxy requests to Finnhub API with server-side API key
- **File**: `api/finnhub/[...path].js` (ES module JavaScript)
- **Examples**:
  - `/api/finnhub/quote?symbol=AAPL`
  - `/api/finnhub/search?q=apple`
  - `/api/finnhub/news?category=general`

### 4. Troubleshooting

#### Common Issues

1. **"exports is not defined" Error**
   - ✅ **Fixed**: Converted TypeScript serverless functions to JavaScript with ES modules
   - ✅ **Fixed**: Removed conflicting module system specifications
   - ✅ **Fixed**: Simplified Vercel configuration

2. **API Key Not Found**
   - Set both `VITE_FINNHUB_API_KEY` and `FINNHUB_API_KEY` in Vercel
   - Verify environment variables are set in production

3. **CORS Issues**
   - ✅ **Fixed**: Proper CORS headers in serverless functions
   - ✅ **Fixed**: Vercel configuration includes CORS headers

4. **Rate Limiting**
   - Finnhub free tier has rate limits
   - Consider upgrading to paid plan for production use

### 5. Testing Deployment

1. **Health Check**: Visit `/api/health` to verify serverless functions
2. **API Debug**: Visit `/api-debug` route for comprehensive API testing
3. **Stock Data**: Test with `/api/finnhub/quote?symbol=AAPL`

### 6. Performance Optimization

- **Caching**: React Query handles client-side caching
- **Serverless**: API requests are handled by fast Vercel edge functions
- **CDN**: Static assets served via Vercel's global CDN

### 7. Security

- **API Key Protection**: Server-side API key handling
- **CORS**: Properly configured cross-origin requests
- **Environment Variables**: Secure environment variable handling

## 📱 Local Development

### Prerequisites
```bash
npm install
```

### Environment Setup
```bash
cp .env.example .env.local
# Edit .env.local with your API keys
```

### Development Server
```bash
# Option 1: Frontend only (uses Vercel serverless functions)
npm run dev

# Option 2: With local proxy server
npm run dev:full
```

### Testing
- Visit `http://localhost:8080/api-debug` for API testing
- Visit `http://localhost:8080/api/health` for health check

## 🔧 Maintenance

### Updating Dependencies
```bash
npm update
npm audit fix
```

### Monitoring
- Check Vercel function logs for API errors
- Monitor Finnhub API usage and rate limits
- Review React Query cache performance

### Scaling
- Consider Finnhub paid plan for higher rate limits
- Implement server-side caching for frequently requested data
- Use Vercel Analytics for performance monitoring
