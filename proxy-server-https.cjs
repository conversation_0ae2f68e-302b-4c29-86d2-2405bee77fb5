const express = require('express');
const https = require('https');
const fs = require('fs');
const cors = require('cors');
const axios = require('axios');
require('dotenv').config({ path: '.env.local' });

const app = express();
const PORT = process.env.PROXY_PORT || 3001;
const HTTPS_PORT = process.env.HTTPS_PROXY_PORT || 3443;

// Enable CORS for all routes
app.use(cors({
  origin: [
    'https://localhost:5173',
    'https://localhost:3000',
    'https://127.0.0.1:5173',
    'https://localhost:8080',
    'https://localhost:8081',
    'https://localhost:8082',
    'https://localhost:8083',
    'https://localhost:8084',
    'https://localhost:8085',
    'https://localhost:8086', // Add the current frontend port
    'https://localhost:8087',
    'https://localhost:8088'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(express.json());

// Handle preflight requests for CORS
app.use((req, res, next) => {
  if (req.method === 'OPTIONS') {
    res.header('Access-Control-Allow-Origin', req.headers.origin);
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    res.header('Access-Control-Allow-Credentials', 'true');
    return res.sendStatus(200);
  }
  next();
});

// Finnhub API configuration
const FINNHUB_BASE_URL = 'https://finnhub.io/api/v1';
const API_KEY = process.env.VITE_FINNHUB_API_KEY;

// Yahoo Finance API configuration
const YAHOO_FINANCE_BASE_URL = 'https://query1.finance.yahoo.com';

if (!API_KEY) {
  console.error('❌ VITE_FINNHUB_API_KEY not found in environment variables');
  process.exit(1);
}

// Rate limiting
let requestQueue = [];
let isProcessing = false;
const RATE_LIMIT_DELAY = 1000; // 1 second between requests

const processQueue = () => {
  if (isProcessing || requestQueue.length === 0) return;
  
  isProcessing = true;
  const { resolve, request } = requestQueue.shift();
  
  request()
    .then(resolve)
    .catch(resolve)
    .finally(() => {
      setTimeout(() => {
        isProcessing = false;
        processQueue();
      }, RATE_LIMIT_DELAY);
    });
};

const rateLimitedRequest = (requestFn) => {
  return new Promise((resolve) => {
    requestQueue.push({ resolve, request: requestFn });
    processQueue();
  });
};

// Specific Finnhub proxy endpoints
app.get('/api/finnhub/quote', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying quote request for symbol: ${req.query.symbol}`);
    
    const response = await rateLimitedRequest(() => 
      axios.get(`${FINNHUB_BASE_URL}/quote`, {
        params: queryParams,
        timeout: 10000
      })
    );
    
    res.json(response.data);
  } catch (error) {
    console.error('❌ Quote proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

app.get('/api/finnhub/stock/profile2', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying profile request for symbol: ${req.query.symbol}`);
    
    const response = await rateLimitedRequest(() => 
      axios.get(`${FINNHUB_BASE_URL}/stock/profile2`, {
        params: queryParams,
        timeout: 10000
      })
    );
    
    res.json(response.data);
  } catch (error) {
    console.error('❌ Profile proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

app.get('/api/finnhub/stock/candle', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying candle request for symbol: ${req.query.symbol}`);
    
    const response = await rateLimitedRequest(() => 
      axios.get(`${FINNHUB_BASE_URL}/stock/candle`, {
        params: queryParams,
        timeout: 10000
      })
    );
    
    res.json(response.data);
  } catch (error) {
    console.error('❌ Candle proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

app.get('/api/finnhub/search', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying search request for query: ${req.query.q}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${FINNHUB_BASE_URL}/search`, {
        params: queryParams,
        timeout: 10000
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error('❌ Search proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

app.get('/api/finnhub/news', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying news request for category: ${req.query.category}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${FINNHUB_BASE_URL}/news`, {
        params: queryParams,
        timeout: 10000
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error('❌ News proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

app.get('/api/finnhub/company-news/:symbol', async (req, res) => {
  try {
    const { symbol } = req.params;
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying company news request for symbol: ${symbol}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${FINNHUB_BASE_URL}/company-news`, {
        params: { ...queryParams, symbol },
        timeout: 10000
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error(`❌ Company news proxy error for ${req.params.symbol}:`, error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

// Yahoo Finance proxy endpoints
app.get('/api/yahoo-finance/chart/:symbol', async (req, res) => {
  try {
    const { symbol } = req.params;
    const queryParams = req.query;
    console.log(`📡 Proxying Yahoo Finance chart request for symbol: ${symbol}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${YAHOO_FINANCE_BASE_URL}/v8/finance/chart/${symbol}`, {
        params: queryParams,
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json',
        }
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error(`❌ Yahoo Finance chart proxy error for ${req.params.symbol}:`, error.message);
    res.status(500).json({ error: 'Yahoo Finance Proxy Error', message: error.message });
  }
});

app.get('/api/yahoo-finance/search', async (req, res) => {
  try {
    const queryParams = req.query;
    console.log(`📡 Proxying Yahoo Finance search request for query: ${req.query.q}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${YAHOO_FINANCE_BASE_URL}/v1/finance/search`, {
        params: queryParams,
        timeout: 15000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'application/json',
        }
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error(`❌ Yahoo Finance search proxy error for query ${req.query.q}:`, error.message);
    res.status(500).json({ error: 'Yahoo Finance Proxy Error', message: error.message });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    apiKey: API_KEY ? 'Present' : 'Missing',
    protocol: 'HTTPS',
    port: HTTPS_PORT
  });
});

// Load SSL certificates
let httpsOptions;
try {
  httpsOptions = {
    key: fs.readFileSync('./ssl/key.pem'),
    cert: fs.readFileSync('./ssl/cert.pem')
  };
  console.log('✅ SSL certificates loaded successfully');
} catch (error) {
  console.error('❌ Failed to load SSL certificates:', error.message);
  console.log('💡 Make sure to run: openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"');
  process.exit(1);
}

// Start HTTPS server
https.createServer(httpsOptions, app).listen(HTTPS_PORT, () => {
  console.log(`🚀 Finnhub HTTPS Proxy Server running on https://localhost:${HTTPS_PORT}`);
  console.log(`🔑 API Key: ${API_KEY ? 'Loaded' : 'Missing'}`);
  console.log(`📊 Health check: https://localhost:${HTTPS_PORT}/health`);
  console.log(`🔒 SSL/TLS encryption enabled`);
});

module.exports = app;
