-- User Feedback System Database Schema
-- Run this script in your Supabase SQL Editor to create the feedback table

-- Create user_feedback table
CREATE TABLE user_feedback (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    subject TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('general', 'content', 'technical', 'suggestion', 'complaint', 'compliment')),
    message TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    user_agent TEXT,
    ip_address INET,
    status TEXT DEFAULT 'new' CHECK (status IN ('new', 'in_progress', 'resolved', 'closed')),
    admin_notes TEXT,
    resolved_by UUID REFERENCES auth.users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better query performance
CREATE INDEX idx_user_feedback_status ON user_feedback(status);
CREATE INDEX idx_user_feedback_category ON user_feedback(category);
CREATE INDEX idx_user_feedback_created_at ON user_feedback(created_at DESC);
CREATE INDEX idx_user_feedback_email ON user_feedback(email);

-- Create trigger for updating updated_at timestamp
CREATE TRIGGER update_user_feedback_updated_at
    BEFORE UPDATE ON user_feedback
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;

-- Policy: Anyone can insert feedback (public submission)
CREATE POLICY "Anyone can submit feedback" ON user_feedback
    FOR INSERT WITH CHECK (true);

-- Policy: Only admins can view all feedback
CREATE POLICY "Admins can view all feedback" ON user_feedback
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role = 'admin'
        )
    );

-- Policy: Only admins can update feedback (for status changes, admin notes)
CREATE POLICY "Admins can update feedback" ON user_feedback
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role = 'admin'
        )
    );

-- Policy: Only admins can delete feedback
CREATE POLICY "Admins can delete feedback" ON user_feedback
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role = 'admin'
        )
    );

-- Create a function to get feedback statistics
CREATE OR REPLACE FUNCTION get_feedback_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_feedback', COUNT(*),
        'new_feedback', COUNT(*) FILTER (WHERE status = 'new'),
        'in_progress', COUNT(*) FILTER (WHERE status = 'in_progress'),
        'resolved', COUNT(*) FILTER (WHERE status = 'resolved'),
        'closed', COUNT(*) FILTER (WHERE status = 'closed'),
        'average_rating', ROUND(AVG(rating), 2),
        'by_category', json_object_agg(category, category_count)
    ) INTO result
    FROM (
        SELECT 
            status,
            rating,
            category,
            COUNT(*) as category_count
        FROM user_feedback 
        GROUP BY category, status, rating
    ) stats;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admins will access this)
GRANT EXECUTE ON FUNCTION get_feedback_stats() TO authenticated;

-- Insert sample feedback data for testing (optional)
INSERT INTO user_feedback (name, email, subject, category, message, rating) VALUES
('John Doe', '<EMAIL>', 'Great website!', 'compliment', 'I love the clean design and comprehensive financial information. Keep up the great work!', 5),
('Jane Smith', '<EMAIL>', 'Suggestion for improvement', 'suggestion', 'It would be great to have more interactive charts and real-time notifications for market changes.', 4),
('Ahmed Hassan', '<EMAIL>', 'Technical issue', 'technical', 'The mobile version seems to load slowly on my device. Could you please look into this?', 3);

-- Create notification function for new feedback (for email notifications)
CREATE OR REPLACE FUNCTION notify_new_feedback()
RETURNS TRIGGER AS $$
BEGIN
    -- This function can be extended to send email notifications
    -- For now, it just logs the new feedback
    RAISE NOTICE 'New feedback submitted: % from %', NEW.subject, NEW.email;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for new feedback notifications
CREATE TRIGGER trigger_notify_new_feedback
    AFTER INSERT ON user_feedback
    FOR EACH ROW EXECUTE FUNCTION notify_new_feedback();

-- Feedback table setup complete!
-- Next steps:
-- 1. Update your TypeScript types in src/integrations/supabase/types.ts
-- 2. Create the feedback form component
-- 3. Set up email notifications (optional)
