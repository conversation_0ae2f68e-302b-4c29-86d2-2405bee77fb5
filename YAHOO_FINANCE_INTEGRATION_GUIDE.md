# Yahoo Finance API Integration Guide for Indian Stock Market Data

## Overview
This comprehensive guide provides step-by-step instructions for integrating Yahoo Finance API to fetch Indian stock market data (NSE/BSE) in the existing React TypeScript finance application. The implementation follows existing code patterns and maintains consistency with the current Finnhub integration.

## Table of Contents
1. [API Setup & Configuration](#1-api-setup--configuration)
2. [Technical Integration](#2-technical-integration)
3. [Indian Stock Market Specifics](#3-indian-stock-market-specifics)
4. [Component Integration](#4-component-integration)
5. [Data Display Requirements](#5-data-display-requirements)
6. [Testing & Validation](#6-testing--validation)

---

## 1. API Setup & Configuration

### 1.1 Yahoo Finance API Access

**Important Note**: Yahoo Finance doesn't provide an official public API. We'll use the unofficial Yahoo Finance API endpoints that are publicly accessible. This approach has limitations but is commonly used for educational and personal projects.

#### Available Endpoints:
- **Quote Data**: `https://query1.finance.yahoo.com/v8/finance/chart/{symbol}`
- **Search**: `https://query1.finance.yahoo.com/v1/finance/search?q={query}`

#### Rate Limiting Considerations:
- No official rate limits, but implement reasonable delays (1-2 seconds between requests)
- Use caching extensively to minimize API calls
- Implement exponential backoff for failed requests
- Consider using a proxy server to avoid CORS issues

### 1.2 Environment Configuration

Add Yahoo Finance configuration to your `.env.local`:

```env
# Yahoo Finance Configuration
VITE_YAHOO_FINANCE_BASE_URL=https://query1.finance.yahoo.com
VITE_YAHOO_FINANCE_ENABLED=true
VITE_YAHOO_FINANCE_RATE_LIMIT_MS=2000
```

---

## 2. Technical Integration

### 2.1 Create Yahoo Finance Client

Create `src/integrations/yahoo-finance/client.ts`:

```typescript
import axios from 'axios';

// Yahoo Finance configuration
const YAHOO_BASE_URL = import.meta.env.VITE_YAHOO_FINANCE_BASE_URL || 'https://query1.finance.yahoo.com';
const RATE_LIMIT_MS = parseInt(import.meta.env.VITE_YAHOO_FINANCE_RATE_LIMIT_MS || '2000');

// Rate limiting queue
let lastRequestTime = 0;
const requestQueue: Array<() => Promise<any>> = [];
let isProcessingQueue = false;

const processQueue = async () => {
  if (isProcessingQueue || requestQueue.length === 0) return;
  
  isProcessingQueue = true;
  
  while (requestQueue.length > 0) {
    const now = Date.now();
    const timeSinceLastRequest = now - lastRequestTime;
    
    if (timeSinceLastRequest < RATE_LIMIT_MS) {
      await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_MS - timeSinceLastRequest));
    }
    
    const request = requestQueue.shift();
    if (request) {
      try {
        await request();
      } catch (error) {
        console.error('Yahoo Finance request failed:', error);
      }
      lastRequestTime = Date.now();
    }
  }
  
  isProcessingQueue = false;
};

const rateLimitedRequest = <T>(requestFn: () => Promise<T>): Promise<T> => {
  return new Promise((resolve, reject) => {
    requestQueue.push(async () => {
      try {
        const result = await requestFn();
        resolve(result);
      } catch (error) {
        reject(error);
      }
    });
    processQueue();
  });
};

// Create axios instance
const yahooFinanceClient = axios.create({
  baseURL: YAHOO_BASE_URL,
  timeout: 15000,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    'Accept': 'application/json',
  },
});

// Response interceptor for error handling
yahooFinanceClient.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Yahoo Finance API Error:', {
      url: error.config?.url,
      status: error.response?.status,
      message: error.message,
    });
    
    if (error.response?.status === 429) {
      console.warn('Rate limit exceeded for Yahoo Finance API');
    }
    
    return Promise.reject(error);
  }
);

export default yahooFinanceClient;
```

### 2.2 Create TypeScript Interfaces

Create `src/integrations/yahoo-finance/types.ts`:

```typescript
// Yahoo Finance API Response Types

export interface YahooQuoteResponse {
  chart: {
    result: Array<{
      meta: {
        currency: string;
        symbol: string;
        exchangeName: string;
        instrumentType: string;
        firstTradeDate: number;
        regularMarketTime: number;
        gmtoffset: number;
        timezone: string;
        exchangeTimezoneName: string;
        regularMarketPrice: number;
        previousClose: number;
        previousClose: number;
        scale: number;
        priceHint: number;
        currentTradingPeriod: {
          pre: TradingPeriod;
          regular: TradingPeriod;
          post: TradingPeriod;
        };
        tradingPeriods: TradingPeriod[][];
        dataGranularity: string;
        range: string;
        validRanges: string[];
      };
      timestamp: number[];
      indicators: {
        quote: Array<{
          open: number[];
          high: number[];
          low: number[];
          close: number[];
          volume: number[];
        }>;
        adjclose?: Array<{
          adjclose: number[];
        }>;
      };
    }>;
    error: any;
  };
}

export interface TradingPeriod {
  timezone: string;
  start: number;
  end: number;
  gmtoffset: number;
}

export interface YahooSearchResponse {
  explains: any[];
  count: number;
  quotes: Array<{
    exchange: string;
    shortname: string;
    quoteType: string;
    symbol: string;
    index: string;
    score: number;
    typeDisp: string;
    longname: string;
    exchDisp: string;
    sector?: string;
    industry?: string;
  }>;
  news: any[];
  nav: any[];
  lists: any[];
  researchReports: any[];
  screenerFieldResults: any[];
  totalTime: number;
  timeTaken: number;
}

// Normalized types for our application
export interface IndianStockQuote {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  high: number;
  low: number;
  open: number;
  previousClose: number;
  volume: number;
  currency: string;
  exchange: string;
  timestamp: number;
  marketState: 'REGULAR' | 'CLOSED' | 'PRE' | 'POST';
}

export interface IndianMarketIndex {
  symbol: string;
  name: string;
  value: number;
  change: number;
  changePercent: number;
  isPositive: boolean;
  currency: string;
  lastUpdated: number;
}

// Popular Indian stocks configuration
export const POPULAR_INDIAN_STOCKS_YAHOO = [
  { symbol: 'RELIANCE.NS', name: 'Reliance Industries Ltd' },
  { symbol: 'TCS.NS', name: 'Tata Consultancy Services Ltd' },
  { symbol: 'HDFCBANK.NS', name: 'HDFC Bank Ltd' },
  { symbol: 'INFY.NS', name: 'Infosys Ltd' },
  { symbol: 'HINDUNILVR.NS', name: 'Hindustan Unilever Ltd' },
  { symbol: 'ICICIBANK.NS', name: 'ICICI Bank Ltd' },
  { symbol: 'BHARTIARTL.NS', name: 'Bharti Airtel Ltd' },
  { symbol: 'ITC.NS', name: 'ITC Ltd' },
  { symbol: 'KOTAKBANK.NS', name: 'Kotak Mahindra Bank Ltd' },
  { symbol: 'LT.NS', name: 'Larsen & Toubro Ltd' },
  { symbol: 'SBIN.NS', name: 'State Bank of India' },
  { symbol: 'ASIANPAINT.NS', name: 'Asian Paints Ltd' },
  { symbol: 'MARUTI.NS', name: 'Maruti Suzuki India Ltd' },
  { symbol: 'BAJFINANCE.NS', name: 'Bajaj Finance Ltd' },
  { symbol: 'HCLTECH.NS', name: 'HCL Technologies Ltd' },
];

export const POPULAR_BSE_STOCKS = [
  { symbol: 'RELIANCE.BO', name: 'Reliance Industries Ltd' },
  { symbol: 'TCS.BO', name: 'Tata Consultancy Services Ltd' },
  { symbol: 'HDFCBANK.BO', name: 'HDFC Bank Ltd' },
  { symbol: 'INFY.BO', name: 'Infosys Ltd' },
  { symbol: 'HINDUNILVR.BO', name: 'Hindustan Unilever Ltd' },
];

export const INDIAN_MARKET_INDICES = [
  { symbol: '^NSEI', name: 'NIFTY 50', exchange: 'NSE' },
  { symbol: '^BSESN', name: 'BSE SENSEX', exchange: 'BSE' },
  { symbol: '^NSEBANK', name: 'NIFTY BANK', exchange: 'NSE' },
  { symbol: '^NSEIT', name: 'NIFTY IT', exchange: 'NSE' },
];

// Market hours for Indian exchanges (IST)
export const INDIAN_MARKET_HOURS = {
  NSE: {
    open: { hour: 9, minute: 15 }, // 9:15 AM IST
    close: { hour: 15, minute: 30 }, // 3:30 PM IST
    timezone: 'Asia/Kolkata',
  },
  BSE: {
    open: { hour: 9, minute: 15 }, // 9:15 AM IST
    close: { hour: 15, minute: 30 }, // 3:30 PM IST
    timezone: 'Asia/Kolkata',
  },
};

// Currency formatting for Indian Rupee
export const formatINR = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Number formatting for Indian numbering system (lakhs, crores)
export const formatIndianNumber = (num: number): string => {
  if (num >= ********) { // 1 crore
    return `₹${(num / ********).toFixed(2)}Cr`;
  } else if (num >= 100000) { // 1 lakh
    return `₹${(num / 100000).toFixed(2)}L`;
  } else if (num >= 1000) { // 1 thousand
    return `₹${(num / 1000).toFixed(2)}K`;
  }
  return `₹${num.toFixed(2)}`;
};
```

### 2.3 Create API Functions

Create `src/integrations/yahoo-finance/api.ts`:

```typescript
import yahooFinanceClient from './client';
import { 
  YahooQuoteResponse, 
  YahooSearchResponse, 
  IndianStockQuote, 
  IndianMarketIndex,
  INDIAN_MARKET_HOURS 
} from './types';

// Helper function to determine market state
const getMarketState = (symbol: string, timestamp: number): 'REGULAR' | 'CLOSED' | 'PRE' | 'POST' => {
  const exchange = symbol.includes('.NS') ? 'NSE' : 'BSE';
  const marketHours = INDIAN_MARKET_HOURS[exchange];
  
  const date = new Date(timestamp * 1000);
  const istTime = new Date(date.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
  
  const currentHour = istTime.getHours();
  const currentMinute = istTime.getMinutes();
  const currentTime = currentHour * 60 + currentMinute;
  
  const openTime = marketHours.open.hour * 60 + marketHours.open.minute;
  const closeTime = marketHours.close.hour * 60 + marketHours.close.minute;
  
  // Check if it's a weekend
  const dayOfWeek = istTime.getDay();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return 'CLOSED';
  }
  
  if (currentTime < openTime) {
    return 'PRE';
  } else if (currentTime > closeTime) {
    return 'POST';
  } else {
    return 'REGULAR';
  }
};

// Get stock quote for Indian stocks
export const getIndianStockQuote = async (symbol: string): Promise<IndianStockQuote> => {
  try {
    const response = await yahooFinanceClient.get<YahooQuoteResponse>(
      `/v8/finance/chart/${symbol}`,
      {
        params: {
          region: 'IN',
          lang: 'en-IN',
          includePrePost: true,
          interval: '1m',
          range: '1d',
        },
      }
    );

    const result = response.data.chart.result[0];
    if (!result) {
      throw new Error(`No data found for symbol: ${symbol}`);
    }

    const meta = result.meta;
    const quote = result.indicators.quote[0];
    const latestIndex = quote.close.length - 1;

    const currentPrice = meta.regularMarketPrice || quote.close[latestIndex];
    const previousClose = meta.previousClose;
    const change = currentPrice - previousClose;
    const changePercent = (change / previousClose) * 100;

    return {
      symbol,
      name: meta.symbol,
      price: currentPrice,
      change,
      changePercent,
      high: quote.high[latestIndex] || meta.regularMarketPrice,
      low: quote.low[latestIndex] || meta.regularMarketPrice,
      open: quote.open[latestIndex] || meta.regularMarketPrice,
      previousClose,
      volume: quote.volume[latestIndex] || 0,
      currency: meta.currency,
      exchange: meta.exchangeName,
      timestamp: meta.regularMarketTime,
      marketState: getMarketState(symbol, meta.regularMarketTime),
    };
  } catch (error) {
    console.error(`Error fetching Indian stock quote for ${symbol}:`, error);
    throw error;
  }
};

// Get multiple Indian stock quotes
export const getBatchIndianStockQuotes = async (symbols: string[]): Promise<IndianStockQuote[]> => {
  try {
    const promises = symbols.map(symbol => getIndianStockQuote(symbol));
    const results = await Promise.allSettled(promises);

    return results
      .filter((result): result is PromiseFulfilledResult<IndianStockQuote> => 
        result.status === 'fulfilled'
      )
      .map(result => result.value);
  } catch (error) {
    console.error('Error fetching batch Indian stock quotes:', error);
    throw error;
  }
};

// Get Indian market indices
export const getIndianMarketIndices = async (): Promise<IndianMarketIndex[]> => {
  const indices = ['^NSEI', '^BSESN', '^NSEBANK', '^NSEIT'];
  
  try {
    const promises = indices.map(async (symbol) => {
      const quote = await getIndianStockQuote(symbol);
      return {
        symbol,
        name: getIndexName(symbol),
        value: quote.price,
        change: quote.change,
        changePercent: quote.changePercent,
        isPositive: quote.change >= 0,
        currency: quote.currency,
        lastUpdated: quote.timestamp,
      };
    });

    const results = await Promise.allSettled(promises);
    return results
      .filter((result): result is PromiseFulfilledResult<IndianMarketIndex> => 
        result.status === 'fulfilled'
      )
      .map(result => result.value);
  } catch (error) {
    console.error('Error fetching Indian market indices:', error);
    throw error;
  }
};

// Search Indian stocks
export const searchIndianStocks = async (query: string): Promise<YahooSearchResponse> => {
  try {
    const response = await yahooFinanceClient.get<YahooSearchResponse>(
      '/v1/finance/search',
      {
        params: {
          q: query,
          region: 'IN',
          lang: 'en-IN',
          quotesCount: 10,
          newsCount: 0,
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error(`Error searching Indian stocks for query: ${query}`, error);
    throw error;
  }
};

// Helper function to get index name
const getIndexName = (symbol: string): string => {
  const indexNames: Record<string, string> = {
    '^NSEI': 'NIFTY 50',
    '^BSESN': 'BSE SENSEX',
    '^NSEBANK': 'NIFTY BANK',
    '^NSEIT': 'NIFTY IT',
  };
  return indexNames[symbol] || symbol;
};


```

---

## 3. Indian Stock Market Specifics

### 3.1 Symbol Formatting

**NSE (National Stock Exchange):**
- Format: `SYMBOL.NS`
- Examples: `RELIANCE.NS`, `TCS.NS`, `INFY.NS`

**BSE (Bombay Stock Exchange):**
- Format: `SYMBOL.BO`
- Examples: `RELIANCE.BO`, `TCS.BO`, `INFY.BO`

**Market Indices:**
- NIFTY 50: `^NSEI`
- BSE SENSEX: `^BSESN`
- NIFTY BANK: `^NSEBANK`
- NIFTY IT: `^NSEIT`

### 3.2 Popular Indian Stocks for Testing

```typescript
export const TEST_INDIAN_STOCKS = [
  'RELIANCE.NS',    // Reliance Industries
  'TCS.NS',         // Tata Consultancy Services
  'HDFCBANK.NS',    // HDFC Bank
  'INFY.NS',        // Infosys
  'HINDUNILVR.NS',  // Hindustan Unilever
  'ICICIBANK.NS',   // ICICI Bank
  'BHARTIARTL.NS',  // Bharti Airtel
  'ITC.NS',         // ITC
  'KOTAKBANK.NS',   // Kotak Mahindra Bank
  'LT.NS',          // Larsen & Toubro
  'SBIN.NS',        // State Bank of India
  'ASIANPAINT.NS',  // Asian Paints
  'MARUTI.NS',      // Maruti Suzuki
  'BAJFINANCE.NS',  // Bajaj Finance
  'HCLTECH.NS',     // HCL Technologies
];
```

### 3.3 Currency and Market Hours

**Currency Handling:**
- Indian stocks are priced in INR (Indian Rupees)
- Use `₹` symbol for display
- Implement Indian numbering system (lakhs, crores)

**Market Hours (IST - Indian Standard Time):**
- **Trading Hours**: 9:15 AM - 3:30 PM IST
- **Pre-market**: 9:00 AM - 9:15 AM IST
- **Post-market**: 3:30 PM - 4:00 PM IST
- **Weekend**: Markets closed on Saturday and Sunday

---

## 4. Component Integration

### 4.1 Create React Hooks

Create `src/hooks/useIndianStockData.ts`:

```typescript
import { useQuery } from '@tanstack/react-query';
import {
  getIndianStockQuote,
  getBatchIndianStockQuotes,
  getIndianMarketIndices,
  searchIndianStocks
} from '@/integrations/yahoo-finance/api';
import { IndianStockQuote, IndianMarketIndex } from '@/integrations/yahoo-finance/types';

// Hook for single Indian stock quote
export const useIndianStockQuote = (symbol: string, enabled: boolean = true) => {
  return useQuery<IndianStockQuote>({
    queryKey: ['indian-stock-quote', symbol],
    queryFn: () => getIndianStockQuote(symbol),
    enabled: enabled && !!symbol,
    staleTime: 30000, // 30 seconds
    refetchInterval: 30000, // Auto-refresh every 30 seconds
    retry: 2,
    retryDelay: 2000, // 2 second delay between retries
  });
};

// Hook for multiple Indian stock quotes
export const useBatchIndianStockQuotes = (symbols: string[], enabled: boolean = true) => {
  return useQuery<IndianStockQuote[]>({
    queryKey: ['batch-indian-quotes', symbols.join(',')],
    queryFn: () => getBatchIndianStockQuotes(symbols),
    enabled: enabled && symbols.length > 0,
    staleTime: 30000, // 30 seconds
    refetchInterval: 30000, // Auto-refresh every 30 seconds
    retry: 1,
    retryDelay: 2000,
  });
};

// Hook for Indian market indices
export const useIndianMarketIndices = () => {
  return useQuery<IndianMarketIndex[]>({
    queryKey: ['indian-market-indices'],
    queryFn: getIndianMarketIndices,
    staleTime: 60000, // 1 minute
    refetchInterval: 60000, // Auto-refresh every minute
    retry: 2,
    retryDelay: 2000,
  });
};

// Hook for searching Indian stocks
export const useIndianStockSearch = (query: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['indian-stock-search', query],
    queryFn: () => searchIndianStocks(query),
    enabled: enabled && query.length > 2,
    staleTime: 300000, // 5 minutes
    retry: 1,
    retryDelay: 2000,
  });
};


```

### 4.2 Update MarketDashboard Component

Add Indian stocks section to `src/components/MarketDashboard.tsx`:

```typescript
// Add these imports
import { useIndianMarketIndices, useBatchIndianStockQuotes } from '@/hooks/useIndianStockData';
import { POPULAR_INDIAN_STOCKS_YAHOO, formatINR } from '@/integrations/yahoo-finance/types';

// Add to the component
const MarketDashboard: React.FC = () => {
  // ... existing code ...
  
  // Add Indian market data
  const { data: indianIndices, isLoading: indianIndicesLoading } = useIndianMarketIndices();
  const { data: indianStocks, isLoading: indianStocksLoading } = useBatchIndianStockQuotes(
    POPULAR_INDIAN_STOCKS_YAHOO.slice(0, 10).map(stock => stock.symbol)
  );

  // Add new tab for Indian markets
  const [activeTab, setActiveTab] = useState<'indices' | 'indian-indices' | 'indian-stocks' | 'gainers' | 'losers'>('indices');

  // ... rest of component implementation
};
```

### 4.3 Update HeroSection Component

Modify `src/components/HeroSection.tsx` to include Indian market indices:

```typescript
// Add import
import { useIndianMarketIndices } from '@/hooks/useIndianStockData';
import { formatINR } from '@/integrations/yahoo-finance/types';

// Update the useMarketData hook to include Indian indices
const useMarketData = () => {
  // Existing Finnhub queries
  const finnhubQueries = marketIndicesConfig.map(config =>
    useQuery({
      queryKey: ['marketIndex', config.symbol],
      queryFn: () => getStockQuote(config.symbol),
      staleTime: 5 * 60 * 1000,
      retry: 1,
      retryDelay: 1000,
    })
  );

  // Add Indian market indices
  const { data: indianIndices, isLoading: indianLoading, isError: indianError } = useIndianMarketIndices();

  // Combine data sources
  const combinedIndices = [
    ...marketIndicesConfig.map((config, index) => {
      const query = finnhubQueries[index];
      // ... existing logic
    }),
    ...(indianIndices || []).map(index => ({
      name: index.name,
      value: index.value,
      change: Math.abs(index.change),
      isPositive: index.isPositive,
      isLive: true,
      currency: 'INR',
    }))
  ];

  return {
    marketIndices: combinedIndices,
    isLoading: finnhubQueries.some(q => q.isLoading) || indianLoading,
    hasError: finnhubQueries.some(q => q.isError) || indianError,
    allSuccess: finnhubQueries.every(q => q.isSuccess) && !indianError,
  };
};
```

### 4.4 Create Indian Stock Components

Create `src/components/IndianStockCard.tsx`:

```typescript
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowUp, ArrowDown, Activity } from 'lucide-react';
import { IndianStockQuote, formatINR } from '@/integrations/yahoo-finance/types';

interface IndianStockCardProps {
  stock: IndianStockQuote;
  onClick?: () => void;
}

const IndianStockCard: React.FC<IndianStockCardProps> = ({ stock, onClick }) => {
  const isPositive = stock.change >= 0;

  return (
    <Card
      className="hover:shadow-md transition-shadow cursor-pointer"
      onClick={onClick}
    >
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <div>
            <h3 className="font-semibold text-sm text-gray-600">
              {stock.symbol.replace('.NS', '').replace('.BO', '')}
            </h3>
            <p className="text-xs text-gray-500">{stock.exchange}</p>
          </div>
          <div className="flex items-center gap-1">
            <Activity className="h-4 w-4 text-blue-500" />
            {stock.marketState === 'REGULAR' && (
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            )}
          </div>
        </div>

        <div className="space-y-1">
          <p className="text-2xl font-bold">
            {formatINR(stock.price)}
          </p>
          <div className={`flex items-center text-sm ${
            isPositive ? 'text-green-600' : 'text-red-600'
          }`}>
            {isPositive ? (
              <ArrowUp className="h-4 w-4 mr-1" />
            ) : (
              <ArrowDown className="h-4 w-4 mr-1" />
            )}
            <span>
              {formatINR(Math.abs(stock.change))} ({Math.abs(stock.changePercent).toFixed(2)}%)
            </span>
          </div>

          <div className="text-xs text-gray-500 mt-2">
            <div>High: {formatINR(stock.high)}</div>
            <div>Low: {formatINR(stock.low)}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default IndianStockCard;
```

---

## 5. Data Display Requirements

### 5.1 Real-time Price Updates

Implement real-time updates with proper caching:

```typescript
// In your component
const { data: stockData, isLoading } = useIndianStockQuote('RELIANCE.NS');

// Display with loading states
{isLoading ? (
  <div className="animate-pulse">
    <div className="h-8 bg-gray-200 rounded mb-2"></div>
    <div className="h-4 bg-gray-200 rounded"></div>
  </div>
) : (
  <div>
    <div className="text-2xl font-bold">{formatINR(stockData?.price || 0)}</div>
    <div className={`text-sm ${stockData?.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
      {stockData?.change >= 0 ? '+' : ''}{formatINR(stockData?.change || 0)}
      ({stockData?.changePercent.toFixed(2)}%)
    </div>
  </div>
)}
```



### 5.2 Market Status Indicators

Create `src/components/IndianMarketStatus.tsx`:

```typescript
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Clock, Activity } from 'lucide-react';
import { INDIAN_MARKET_HOURS } from '@/integrations/yahoo-finance/types';

const IndianMarketStatus: React.FC = () => {
  const [marketStatus, setMarketStatus] = React.useState<{
    nse: 'OPEN' | 'CLOSED' | 'PRE' | 'POST';
    bse: 'OPEN' | 'CLOSED' | 'PRE' | 'POST';
    nextOpen?: string;
  }>({ nse: 'CLOSED', bse: 'CLOSED' });

  React.useEffect(() => {
    const updateMarketStatus = () => {
      const now = new Date();
      const istTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));

      const currentHour = istTime.getHours();
      const currentMinute = istTime.getMinutes();
      const currentTime = currentHour * 60 + currentMinute;

      const openTime = 9 * 60 + 15; // 9:15 AM
      const closeTime = 15 * 60 + 30; // 3:30 PM

      // Check if it's a weekend
      const dayOfWeek = istTime.getDay();
      let status: 'OPEN' | 'CLOSED' | 'PRE' | 'POST' = 'CLOSED';

      if (dayOfWeek !== 0 && dayOfWeek !== 6) { // Not weekend
        if (currentTime < openTime) {
          status = 'PRE';
        } else if (currentTime >= openTime && currentTime <= closeTime) {
          status = 'OPEN';
        } else {
          status = 'POST';
        }
      }

      setMarketStatus({
        nse: status,
        bse: status,
        nextOpen: status === 'CLOSED' ? getNextOpenTime(istTime) : undefined,
      });
    };

    updateMarketStatus();
    const interval = setInterval(updateMarketStatus, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  const getNextOpenTime = (currentTime: Date): string => {
    const tomorrow = new Date(currentTime);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(9, 15, 0, 0);

    // Skip weekends
    while (tomorrow.getDay() === 0 || tomorrow.getDay() === 6) {
      tomorrow.setDate(tomorrow.getDate() + 1);
    }

    return tomorrow.toLocaleString('en-IN', {
      timeZone: 'Asia/Kolkata',
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'bg-green-500';
      case 'PRE': return 'bg-yellow-500';
      case 'POST': return 'bg-orange-500';
      default: return 'bg-red-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'OPEN': return 'Market Open';
      case 'PRE': return 'Pre-Market';
      case 'POST': return 'After Hours';
      default: return 'Market Closed';
    }
  };

  return (
    <div className="flex items-center gap-4 p-4 bg-card rounded-lg border">
      <div className="flex items-center gap-2">
        <Activity className="h-5 w-5" />
        <span className="font-medium">Indian Markets</span>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${getStatusColor(marketStatus.nse)}`} />
          <span className="text-sm">NSE: {getStatusText(marketStatus.nse)}</span>
        </div>

        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${getStatusColor(marketStatus.bse)}`} />
          <span className="text-sm">BSE: {getStatusText(marketStatus.bse)}</span>
        </div>
      </div>

      {marketStatus.nextOpen && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Clock className="h-4 w-4" />
          <span>Next open: {marketStatus.nextOpen}</span>
        </div>
      )}
    </div>
  );
};

export default IndianMarketStatus;
```

---

## 6. Testing & Validation

### 6.1 Sample API Calls

Create `src/utils/testYahooFinance.ts` for testing:

```typescript
import {
  getIndianStockQuote,
  getBatchIndianStockQuotes,
  getIndianMarketIndices,
  searchIndianStocks
} from '@/integrations/yahoo-finance/api';

// Test individual stock quote
export const testSingleStock = async () => {
  try {
    console.log('Testing single stock quote...');
    const quote = await getIndianStockQuote('RELIANCE.NS');
    console.log('RELIANCE.NS Quote:', quote);
    return quote;
  } catch (error) {
    console.error('Single stock test failed:', error);
    throw error;
  }
};

// Test batch quotes
export const testBatchQuotes = async () => {
  try {
    console.log('Testing batch quotes...');
    const symbols = ['RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS'];
    const quotes = await getBatchIndianStockQuotes(symbols);
    console.log('Batch Quotes:', quotes);
    return quotes;
  } catch (error) {
    console.error('Batch quotes test failed:', error);
    throw error;
  }
};

// Test market indices
export const testMarketIndices = async () => {
  try {
    console.log('Testing market indices...');
    const indices = await getIndianMarketIndices();
    console.log('Market Indices:', indices);
    return indices;
  } catch (error) {
    console.error('Market indices test failed:', error);
    throw error;
  }
};

// Test search functionality
export const testStockSearch = async () => {
  try {
    console.log('Testing stock search...');
    const results = await searchIndianStocks('Reliance');
    console.log('Search Results:', results);
    return results;
  } catch (error) {
    console.error('Stock search test failed:', error);
    throw error;
  }
};

// Run all tests
export const runAllTests = async () => {
  console.log('🧪 Starting Yahoo Finance API Tests...');

  try {
    await testSingleStock();
    console.log('✅ Single stock test passed');

    await testBatchQuotes();
    console.log('✅ Batch quotes test passed');

    await testMarketIndices();
    console.log('✅ Market indices test passed');

    await testStockSearch();
    console.log('✅ Stock search test passed');

    console.log('🎉 All tests passed!');
  } catch (error) {
    console.error('❌ Tests failed:', error);
  }
};
```

### 6.2 Error Scenarios and Fallback Data

Create fallback data in `src/integrations/yahoo-finance/fallback.ts`:

```typescript
import { IndianStockQuote, IndianMarketIndex } from './types';

export const fallbackIndianStocks: IndianStockQuote[] = [
  {
    symbol: 'RELIANCE.NS',
    name: 'Reliance Industries Ltd',
    price: 2456.75,
    change: 23.45,
    changePercent: 0.96,
    high: 2478.90,
    low: 2445.20,
    open: 2450.00,
    previousClose: 2433.30,
    volume: 1234567,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'TCS.NS',
    name: 'Tata Consultancy Services Ltd',
    price: 3567.80,
    change: -12.30,
    changePercent: -0.34,
    high: 3589.50,
    low: 3556.70,
    open: 3580.10,
    previousClose: 3580.10,
    volume: 987654,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  // Add more fallback stocks...
];

export const fallbackIndianIndices: IndianMarketIndex[] = [
  {
    symbol: '^NSEI',
    name: 'NIFTY 50',
    value: 22196.95,
    change: 82.37,
    changePercent: 0.37,
    isPositive: true,
    currency: 'INR',
    lastUpdated: Date.now() / 1000,
  },
  {
    symbol: '^BSESN',
    name: 'BSE SENSEX',
    value: 73088.33,
    change: 315.63,
    changePercent: 0.43,
    isPositive: true,
    currency: 'INR',
    lastUpdated: Date.now() / 1000,
  },
  // Add more fallback indices...
];
```

### 6.3 Performance Optimization

Implement caching and optimization strategies:

```typescript
// In your hooks, add proper error boundaries and fallbacks
export const useIndianStockQuoteWithFallback = (symbol: string) => {
  const query = useIndianStockQuote(symbol);

  return {
    ...query,
    data: query.data || fallbackIndianStocks.find(stock => stock.symbol === symbol),
    isUsingFallback: !query.data && !query.isLoading,
  };
};

// Implement request deduplication
const requestCache = new Map();

export const getCachedIndianStockQuote = async (symbol: string) => {
  const cacheKey = `quote-${symbol}`;
  const cached = requestCache.get(cacheKey);

  if (cached && Date.now() - cached.timestamp < 30000) { // 30 second cache
    return cached.data;
  }

  try {
    const data = await getIndianStockQuote(symbol);
    requestCache.set(cacheKey, { data, timestamp: Date.now() });
    return data;
  } catch (error) {
    // Return cached data if available, even if stale
    if (cached) {
      console.warn(`Using stale data for ${symbol}`);
      return cached.data;
    }
    throw error;
  }
};
```

---

## Implementation Checklist

### Phase 1: Basic Setup
- [ ] Create Yahoo Finance client with rate limiting
- [ ] Implement TypeScript interfaces
- [ ] Create basic API functions
- [ ] Set up React hooks
- [ ] Test with sample Indian stocks

### Phase 2: Component Integration
- [ ] Update MarketDashboard with Indian stocks
- [ ] Modify HeroSection for Indian indices
- [ ] Create IndianStockCard component

- [ ] Add IndianMarketStatus component

### Phase 3: Advanced Features
- [ ] Implement search functionality

- [ ] Create fallback mechanisms
- [ ] Optimize performance with caching
- [ ] Add error boundaries

### Phase 4: Testing & Deployment
- [ ] Test all API endpoints
- [ ] Validate error scenarios
- [ ] Performance testing
- [ ] Mobile responsiveness testing
- [ ] Deploy and monitor

---

## Best Practices

1. **Rate Limiting**: Always implement proper rate limiting to avoid being blocked
2. **Caching**: Use aggressive caching for non-real-time data
3. **Error Handling**: Provide meaningful fallbacks for API failures
4. **Performance**: Batch requests when possible
5. **User Experience**: Show loading states and error messages clearly
6. **Mobile First**: Ensure all components work well on mobile devices

This implementation guide provides a comprehensive approach to integrating Yahoo Finance API for Indian stock market data while maintaining consistency with your existing codebase patterns.
