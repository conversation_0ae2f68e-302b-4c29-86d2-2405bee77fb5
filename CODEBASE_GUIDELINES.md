# Codebase Rules & Guidelines for AI

## General Principles
- Follow the existing folder and file structure.
- Use TypeScript and React best practices.
- Prefer functional components and hooks.
- Use TanStack Query for data fetching and caching.
- Always handle loading, error, and fallback states in UI.
- Use environment variables for API keys and secrets (never hardcode).
- Use fallback data for critical features if APIs fail.
- Ensure all code is readable, maintainable, and well-commented.

## UI/UX
- Use Tailwind CSS for styling and utility classes.
- Ensure responsive design for all components.
- Provide skeletons or loading indicators for async data.
- Use clear, accessible color schemes and icons.
- Prefer modern, clean layouts and spacing.

## Data Layer
- Use custom hooks for all data fetching (e.g., `useStockData`, `useRealTimeQuotes`).
- Cache non-real-time data with long `staleTime` and `cacheTime`.
- Use debouncing for search inputs to avoid excessive API calls.
- Integrate fallback logic for market data and indices.

## API Integration
- Use Finnhub for US market data, Yahoo Finance for Indian indices.
- Always access API keys via `import.meta.env.VITE_...`.
- Handle API errors gracefully and show fallback/cached data.

## Component Guidelines
- Place UI components in `src/components/`.
- Place hooks in `src/hooks/`.
- Place integration logic in `src/integrations/`.
- Place pages in `src/pages/`.
- Use clear, descriptive names for all files and exports.

## Security & Environment
- Store secrets in `.env` (never commit to git).
- Provide `.env.example` for required environment variables.
- Add `.env` to `.gitignore`.

## Testing & Debugging
- Use test/debug components in `src/components/` as needed.
- Prefer mock data and fallback logic for local development.

## Accessibility & Polish
- Ensure all interactive elements are keyboard accessible.
- Use semantic HTML and ARIA attributes where appropriate.
- Provide dark/light mode support if possible.

---

_These guidelines ensure consistency, reliability, and maintainability for all AI-generated code in this project._
