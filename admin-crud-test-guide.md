# Admin Panel CRUD Operations Test Guide

## Issues Identified and Fixed

### 1. **Primary Issue: useContentItem Hook Filtering**
- **Problem**: The `useContentItem` hook was filtering by `status = 'published'`, preventing loading of draft content for editing
- **Fix**: Created `useAdminContentItem` hook that loads content regardless of status
- **Files Modified**: 
  - `src/hooks/useContent.ts` - Added new hook
  - `src/components/admin/ContentForm.tsx` - Updated to use new hook

### 2. **Secondary Issue: Missing Admin RLS Policies**
- **Problem**: Database RLS policies only allowed authors to manage their own content, no admin override
- **Fix**: Created admin-specific policies that allow admin/editor users to manage all content
- **Files Created**: `admin-policies.sql` - SQL script to add admin policies

## Testing Steps

### Prerequisites
1. Ensure you have admin access (user profile with role 'admin' or 'editor')
2. Apply the admin RLS policies by running `admin-policies.sql` in Supabase SQL Editor

### Test Edit Operations

#### Test 1: Edit Published Content
1. Navigate to `/admin`
2. Go to any content type tab (Insights, News, Education, Case Studies)
3. Find a published item and click the edit button
4. **Expected**: Form should load with existing content populated
5. Make changes and save
6. **Expected**: Changes should be saved successfully

#### Test 2: Edit Draft Content
1. Navigate to `/admin`
2. Go to any content type tab
3. Find a draft item and click the edit button
4. **Expected**: Form should load with existing content populated
5. Make changes and save
6. **Expected**: Changes should be saved successfully

#### Test 3: Edit Content from Different Authors
1. Navigate to `/admin`
2. Try to edit content created by different authors
3. **Expected**: Should work if you have admin/editor role

### Test Delete Operations

#### Test 4: Delete Published Content
1. Navigate to `/admin`
2. Go to any content type tab
3. Find a published item and click the delete button
4. Confirm deletion in the dialog
5. **Expected**: Item should be deleted successfully

#### Test 5: Delete Draft Content
1. Navigate to `/admin`
2. Go to any content type tab
3. Find a draft item and click the delete button
4. Confirm deletion in the dialog
5. **Expected**: Item should be deleted successfully

#### Test 6: Delete Content from Different Authors
1. Navigate to `/admin`
2. Try to delete content created by different authors
3. **Expected**: Should work if you have admin/editor role

### Test Create Operations

#### Test 7: Create New Content
1. Navigate to `/admin`
2. Click "Create New" for any content type
3. Fill out the form and save as draft
4. **Expected**: Content should be created successfully
5. Edit the same content and publish it
6. **Expected**: Content should be updated and published

### Error Scenarios to Test

#### Test 8: Non-Admin User Access
1. Login as a regular user (not admin/editor)
2. Try to access `/admin`
3. **Expected**: Should see "Access Denied" message

#### Test 9: Network Error Handling
1. Disconnect internet while performing CRUD operations
2. **Expected**: Should see appropriate error messages

## Common Issues and Solutions

### Issue: "Operation failed" toast appears
- **Cause**: RLS policies not applied or user doesn't have proper role
- **Solution**: Run `admin-policies.sql` and verify user has admin/editor role

### Issue: Form doesn't load existing content
- **Cause**: `useContentItem` hook still being used instead of `useAdminContentItem`
- **Solution**: Verify the import and usage in ContentForm.tsx

### Issue: Can only edit own content
- **Cause**: Admin RLS policies not applied
- **Solution**: Run `admin-policies.sql` in Supabase

### Issue: Authentication errors
- **Cause**: User not properly authenticated or role not set
- **Solution**: Check user_profiles table for proper role assignment

## Database Setup Required

Run the following SQL in your Supabase SQL Editor:

```sql
-- Execute the admin-policies.sql file content here
-- This adds the necessary RLS policies for admin users
```

## Files Modified Summary

1. **src/hooks/useContent.ts**
   - Added `useAdminContentItem` hook for admin content loading

2. **src/components/admin/ContentForm.tsx**
   - Updated import to use `useAdminContentItem`
   - Updated hook usage in component

3. **admin-policies.sql** (New file)
   - Contains RLS policies for admin access to all content

## Next Steps

1. Apply the admin RLS policies to your Supabase database
2. Test all CRUD operations as outlined above
3. Verify that non-admin users cannot access admin functions
4. Monitor for any additional errors in browser console

The fixes address the core issues preventing admin panel CRUD operations from working properly. The combination of the corrected hook usage and proper RLS policies should restore full admin functionality.
