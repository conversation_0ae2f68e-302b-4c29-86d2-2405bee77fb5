// Simple API test script
// Usage: node test-api.js https://your-app.vercel.app

const baseUrl = process.argv[2] || 'http://localhost:3000';

async function testEndpoint(url, description) {
  try {
    console.log(`\n🧪 Testing: ${description}`);
    console.log(`📍 URL: ${url}`);
    
    const response = await fetch(url);
    const data = await response.json();
    
    if (response.ok) {
      console.log(`✅ Status: ${response.status} OK`);
      console.log(`📄 Response:`, JSON.stringify(data, null, 2));
    } else {
      console.log(`❌ Status: ${response.status} ${response.statusText}`);
      console.log(`📄 Error:`, JSON.stringify(data, null, 2));
    }
  } catch (error) {
    console.log(`💥 Error: ${error.message}`);
  }
}

async function runTests() {
  console.log(`🚀 Testing API endpoints for: ${baseUrl}`);
  
  // Test health endpoint
  await testEndpoint(`${baseUrl}/api/health`, 'Health Check');
  
  // Test Finnhub endpoints
  await testEndpoint(`${baseUrl}/api/finnhub/quote?symbol=AAPL`, 'Stock Quote');
  await testEndpoint(`${baseUrl}/api/finnhub/search?q=apple`, 'Symbol Search');
  await testEndpoint(`${baseUrl}/api/finnhub/news?category=general`, 'Market News');
  
  console.log('\n🏁 Tests completed!');
}

runTests().catch(console.error);
