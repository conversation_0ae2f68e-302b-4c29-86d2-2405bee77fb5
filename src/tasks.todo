
  Phase 1: Foundation & Core Data Layer


  This phase focuses on setting up a secure and efficient data-fetching foundation.


   ☐  Task 1: Secure API Keys with Environment Variables
       * Goal: Remove all hardcoded API keys from the source code and manage them securely.
       * Steps:
           1. Create a new file in the project root named .env.
           2. Add your secret keys to this file, prefixed with VITE_:

                 VITE_SUPABASE_URL=your_supabase_url
                 VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
                VITE_FINNHUB_API_KEY=your_finnhub_api_key

           3. Add .env to your .gitignore file to ensure it's never committed.
           4. Create a .env.example file, listing the variables needed (without their values), so other developers
              know what keys to add.
           5. Update your code (e.g., src/integrations/supabase/client.ts and src/integrations/finnhub/client.ts) to
              use import.meta.env.VITE_... to access these keys.
 

   * Task 2: Implement Caching for Non-Real-time Data
       * Goal: Create a robust custom hook using TanStack Query to fetch and cache data that doesn't change
         frequently, like company profiles.
       * Steps:
           1. Open or create src/hooks/useStockData.ts.
           2. Create a function useCompanyProfile inside this file.
           3. Use @tanstack/react-query's useQuery hook.
           4. Set a long staleTime (e.g., 1 hour) and cacheTime (e.g., 90 minutes) to minimize API calls for this
              data.
           5. Handle isLoading and isError states within the hook's return value.

  ---


  Phase 2: Implementing Real-Time Functionality

  Now, let's build the real-time features using WebSockets.


   * Task 3: Create a WebSocket Hook for Real-Time Prices
       * Goal: Build a reusable hook to manage the Finnhub WebSocket connection.
       * Steps:
           1. Create a new file: src/hooks/useRealTimeQuotes.ts.
           2. Implement the useRealTimeQuotes hook as detailed in our previous conversation. It should:
               * Establish a single WebSocket connection.
               * Handle subscribe and unsubscribe messages.
               * Listen for incoming trade messages and update a state object.
               * Include cleanup logic to unsubscribe and close the connection.


   * Task 4: Build the Dynamic Market Ticker
       * Goal: Replace the static MarketTicker with a live one powered by your new WebSocket hook.
       * Steps:
           1. Open src/components/MarketTicker.tsx.
           2. Define a list of stock symbols you want to display (e.g., ['AAPL', 'RELIANCE.NS', ...]).
           3. Call your useRealTimeQuotes hook with this list.
           4. Map over the latestTrades state returned by the hook to render the ticker items.
           5. Add logic to display "Loading..." or a placeholder while the connection is established.
           6. Style the price changes (green/red) based on the data.

  ---

  Phase 3: Building Core Application Features

  With the data layer in place, you can now build the main UI features.


   * Task 5: Implement the Debounced Stock Search
       * Goal: Create a search bar that efficiently queries the Finnhub API without overwhelming it.
       * Steps:
           1. Create the src/hooks/useDebounce.ts hook.
           2. Create a new component: src/components/StockSearch.tsx.
           3. In this component, use useState for the input's value and useDebounce to get the debounced value.
           4. Use TanStack Query's useQuery to call the Finnhub symbolSearch endpoint, using the debounced search
              term as part of the queryKey.
           5. Render the search results and handle loading/error states.
           6. Integrate this StockSearch component into your home page or navbar.


   * Task 6: Create the Stock Detail Page
       * Goal: Build the comprehensive /stocks/:symbol page.
       * Steps:
           1. Open src/pages/StockDetail.tsx.
           2. Use the useParams hook from react-router-dom to get the stock symbol from the URL.
           3. Use your useCompanyProfile hook (from Task 2) to fetch and display the company's static data.
           4. Use your useRealTimeQuotes hook (from Task 3) with just the single stock symbol to get live price
              updates.
           5. Integrate the StockChart component, passing the symbol to it to fetch and display historical data.
           6. Flesh out the UI to show all the required information (day range, market cap, etc.), ensuring you have
              good loading skeletons for each section.


   * Task 7: Enhance the Home Page Dashboard
       * Goal: Assemble the home page with all the new components and data.
       * Steps:
           1. Open src/pages/Index.tsx or src/pages/Dashboard.tsx.
           2. Integrate the MarketTicker (Task 4) and StockSearch (Task 5).
           3. Create new components for "Top Gainers" and "Top Losers". These will require new functions in your
              useStockData hook that call the appropriate Finnhub endpoints.
           4. Add components for displaying major market indices (NIFTY, S&P 500), which will also need their own
              data-fetching logic.

  ---

  Phase 4: Finalization and Polish

  The final phase is about refining the user experience.


   * Task 8: Implement User Watchlist
       * Goal: Allow logged-in users to save and view a list of their favorite stocks.
       * Steps:
           1. Design the Supabase table schema for storing watchlists (e.g., a table linking user_id to stock_symbol).
           2. Create functions in src/integrations/supabase/client.ts to add, remove, and fetch watchlist items for a
              user.
           3. On the StockDetail page, add an "Add to Watchlist" button that calls these Supabase functions.
           4. Create a usePortfolio or useWatchlist hook to manage this state.
           5. Display the user's watchlist on their Dashboard page.



   * Task 9: Final UI/UX Polish and Review
       * Goal: Ensure the application is responsive, accessible, and handles all edge cases gracefully.
       * Steps:
           1. Thoroughly test the application on different screen sizes.
           2. Review every component and ensure loading and error states are handled elegantly everywhere.
           3. Implement dark/light mode support if desired.
           4. Add final touches like animations and transitions to improve the user experience.

     
           ☐ fix cors issue in vercel server
           