/**
 * High-performance microservice client for stock data
 * Replaces slow Yahoo Finance direct calls
 */

import axios from 'axios';

const MICROSERVICE_BASE_URL = import.meta.env.VITE_MICROSERVICE_URL || '/api';

// Create axios instance with optimized settings
const microserviceClient = axios.create({
  baseURL: MICROSERVICE_BASE_URL,
  timeout: 10000, // 10 second timeout (much faster than Yahoo direct)
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
microserviceClient.interceptors.request.use(
  (config) => {
    console.log(`🚀 Microservice Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Microservice Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
microserviceClient.interceptors.response.use(
  (response) => {
    console.log(`✅ Microservice Response: ${response.config.url} (${response.status})`);
    return response;
  },
  (error) => {
    console.error(`❌ Microservice Error: ${error.config?.url}`, {
      status: error.response?.status,
      message: error.response?.data?.detail || error.message,
    });
    return Promise.reject(error);
  }
);

// Types
export interface MicroserviceStockQuote {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  high: number;
  low: number;
  open: number;
  previousClose: number;
  volume: number;
  currency: string;
  exchange: string;
  timestamp: number;
  marketState: string;
}

export interface BatchQuoteResult {
  symbol: string;
  data: MicroserviceStockQuote | null;
  error: string | null;
}

// API Functions
export const getStockQuote = async (symbol: string): Promise<MicroserviceStockQuote> => {
  try {
    const response = await microserviceClient.get(`/quote/${symbol}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching quote for ${symbol}:`, error);
    throw error;
  }
};

export const getBatchStockQuotes = async (symbols: string[]): Promise<BatchQuoteResult[]> => {
  try {
    const response = await microserviceClient.post('/quote', { symbols });
    return response.data;
  } catch (error) {
    console.error('Error fetching batch quotes:', error);
    throw error;
  }
};

export const getIndianIndices = async (): Promise<MicroserviceStockQuote[]> => {
  try {
    const response = await microserviceClient.get('/indices');
    return response.data;
  } catch (error) {
    console.error('Error fetching Indian indices:', error);
    throw error;
  }
};

export const checkHealth = async () => {
  try {
    const response = await microserviceClient.get('/health');
    return response.data;
  } catch (error) {
    console.error('Health check failed:', error);
    throw error;
  }
};

// Utility function to convert microservice data to your existing format
export const convertToIndianStockQuote = (data: MicroserviceStockQuote) => {
  return {
    symbol: data.symbol,
    name: data.name,
    price: data.price,
    change: data.change,
    changePercent: data.changePercent,
    high: data.high,
    low: data.low,
    open: data.open,
    previousClose: data.previousClose,
    volume: data.volume,
    currency: data.currency,
    exchange: data.exchange,
    timestamp: data.timestamp,
    marketState: data.marketState,
  };
};

export default microserviceClient;
