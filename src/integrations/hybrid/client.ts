/**
 * Hybrid client that switches between Yahoo Finance and Microservice
 * Allows for gradual migration and A/B testing
 */

import { shouldUseMicroservice, logPerformance } from '@/utils/featureFlags';
import * as yahooClient from '@/integrations/yahoo-finance/api';
import * as microserviceClient from '@/integrations/microservice/client';
import { IndianStockQuote, BatchIndianQuoteResult } from '@/integrations/yahoo-finance/types';

// Wrapper function with performance tracking
const withPerformanceTracking = async <T>(
  operation: string,
  source: 'yahoo' | 'microservice',
  fn: () => Promise<T>
): Promise<T> => {
  const startTime = Date.now();
  
  try {
    const result = await fn();
    const duration = Date.now() - startTime;
    logPerformance(operation, duration, source, true);
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    logPerformance(operation, duration, source, false);
    throw error;
  }
};

// Hybrid functions that choose the best data source
export const getIndianStockQuote = async (symbol: string): Promise<IndianStockQuote> => {
  const useMicroservice = shouldUseMicroservice();
  
  if (useMicroservice) {
    try {
      return await withPerformanceTracking(
        'getIndianStockQuote',
        'microservice',
        async () => {
          const data = await microserviceClient.getStockQuote(symbol);
          return microserviceClient.convertToIndianStockQuote(data);
        }
      );
    } catch (error) {
      console.warn('🔄 Microservice failed, falling back to Yahoo Finance:', error);
      // Fallback to Yahoo Finance
    }
  }
  
  // Use Yahoo Finance (original implementation)
  return await withPerformanceTracking(
    'getIndianStockQuote',
    'yahoo',
    () => yahooClient.getIndianStockQuote(symbol)
  );
};

export const getBatchIndianStockQuotes = async (symbols: string[]): Promise<BatchIndianQuoteResult[]> => {
  const useMicroservice = shouldUseMicroservice();
  
  if (useMicroservice) {
    try {
      return await withPerformanceTracking(
        'getBatchIndianStockQuotes',
        'microservice',
        async () => {
          const results = await microserviceClient.getBatchStockQuotes(symbols);
          return results.map(result => ({
            symbol: result.symbol,
            data: result.data ? microserviceClient.convertToIndianStockQuote(result.data) : null,
            error: result.error,
          }));
        }
      );
    } catch (error) {
      console.warn('🔄 Microservice batch failed, falling back to Yahoo Finance:', error);
      // Fallback to Yahoo Finance
    }
  }
  
  // Use Yahoo Finance (original implementation)
  return await withPerformanceTracking(
    'getBatchIndianStockQuotes',
    'yahoo',
    () => yahooClient.getBatchIndianStockQuotes(symbols)
  );
};

export const getIndianMarketIndices = async () => {
  const useMicroservice = shouldUseMicroservice();
  
  if (useMicroservice) {
    try {
      return await withPerformanceTracking(
        'getIndianMarketIndices',
        'microservice',
        async () => {
          const data = await microserviceClient.getIndianIndices();
          return data.map(microserviceClient.convertToIndianStockQuote);
        }
      );
    } catch (error) {
      console.warn('🔄 Microservice indices failed, falling back to Yahoo Finance:', error);
      // Fallback to Yahoo Finance
    }
  }
  
  // Use Yahoo Finance (original implementation)
  return await withPerformanceTracking(
    'getIndianMarketIndices',
    'yahoo',
    () => yahooClient.getIndianMarketIndices()
  );
};

// Re-export other functions that don't need hybrid logic
export const searchIndianStocks = yahooClient.searchIndianStocks;
export const areIndianMarketsOpen = yahooClient.areIndianMarketsOpen;
export const getNextMarketOpenTime = yahooClient.getNextMarketOpenTime;

// Health check function to test microservice availability
export const checkMicroserviceHealth = async (): Promise<boolean> => {
  try {
    await microserviceClient.checkHealth();
    return true;
  } catch (error) {
    console.warn('Microservice health check failed:', error);
    return false;
  }
};

// Development helper to force switch data sources
if (import.meta.env.DEV) {
  (window as any).hybridClient = {
    checkMicroserviceHealth,
    forceYahoo: () => {
      localStorage.setItem('force-yahoo', 'true');
      location.reload();
    },
    forceMicroservice: () => {
      localStorage.setItem('force-microservice', 'true');
      location.reload();
    },
    clearForce: () => {
      localStorage.removeItem('force-yahoo');
      localStorage.removeItem('force-microservice');
      location.reload();
    },
  };
}
