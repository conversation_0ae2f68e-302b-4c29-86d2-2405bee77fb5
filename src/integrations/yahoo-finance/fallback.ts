import { IndianStockQuote, IndianMarketIndex } from './types';

// Fallback data for Indian stocks when API is unavailable
export const fallbackIndianStocks: IndianStockQuote[] = [
  {
    symbol: 'RELIANCE.NS',
    name: 'Reliance Industries Ltd',
    price: 2456.75,
    change: 23.45,
    changePercent: 0.96,
    high: 2478.90,
    low: 2445.20,
    open: 2450.00,
    previousClose: 2433.30,
    volume: 1234567,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'TCS.NS',
    name: 'Tata Consultancy Services Ltd',
    price: 3567.80,
    change: -12.30,
    changePercent: -0.34,
    high: 3589.50,
    low: 3556.70,
    open: 3580.10,
    previousClose: 3580.10,
    volume: 987654,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'HDFCBANK.NS',
    name: 'HDFC Bank Ltd',
    price: 1678.45,
    change: 8.75,
    changePercent: 0.52,
    high: 1685.20,
    low: 1665.30,
    open: 1670.00,
    previousClose: 1669.70,
    volume: 2345678,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'INFY.NS',
    name: 'Infosys Ltd',
    price: 1456.90,
    change: -5.60,
    changePercent: -0.38,
    high: 1468.50,
    low: 1452.30,
    open: 1462.50,
    previousClose: 1462.50,
    volume: 1876543,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'HINDUNILVR.NS',
    name: 'Hindustan Unilever Ltd',
    price: 2234.60,
    change: 15.80,
    changePercent: 0.71,
    high: 2245.30,
    low: 2225.40,
    open: 2230.00,
    previousClose: 2218.80,
    volume: 654321,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'ICICIBANK.NS',
    name: 'ICICI Bank Ltd',
    price: 1089.75,
    change: 12.45,
    changePercent: 1.16,
    high: 1095.60,
    low: 1078.20,
    open: 1082.30,
    previousClose: 1077.30,
    volume: 3456789,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'BHARTIARTL.NS',
    name: 'Bharti Airtel Ltd',
    price: 1234.50,
    change: -8.25,
    changePercent: -0.66,
    high: 1248.75,
    low: 1230.40,
    open: 1242.75,
    previousClose: 1242.75,
    volume: 2109876,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'ITC.NS',
    name: 'ITC Ltd',
    price: 456.80,
    change: 3.20,
    changePercent: 0.71,
    high: 459.50,
    low: 452.30,
    open: 454.60,
    previousClose: 453.60,
    volume: 5432109,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'KOTAKBANK.NS',
    name: 'Kotak Mahindra Bank Ltd',
    price: 1789.30,
    change: 18.70,
    changePercent: 1.06,
    high: 1795.80,
    low: 1775.50,
    open: 1780.60,
    previousClose: 1770.60,
    volume: 1765432,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
  {
    symbol: 'LT.NS',
    name: 'Larsen & Toubro Ltd',
    price: 3456.75,
    change: -22.50,
    changePercent: -0.65,
    high: 3485.20,
    low: 3445.30,
    open: 3479.25,
    previousClose: 3479.25,
    volume: 876543,
    currency: 'INR',
    exchange: 'NSE',
    timestamp: Date.now() / 1000,
    marketState: 'CLOSED',
  },
];

// Fallback data for Indian market indices
export const fallbackIndianIndices: IndianMarketIndex[] = [
  {
    symbol: '^NSEI',
    name: 'NIFTY 50',
    value: 22196.95,
    change: 82.37,
    changePercent: 0.37,
    isPositive: true,
    currency: 'INR',
    lastUpdated: Date.now() / 1000,
  },
  {
    symbol: '^BSESN',
    name: 'BSE SENSEX',
    value: 73088.33,
    change: 315.63,
    changePercent: 0.43,
    isPositive: true,
    currency: 'INR',
    lastUpdated: Date.now() / 1000,
  },
  {
    symbol: '^NSEBANK',
    name: 'NIFTY BANK',
    value: 48567.80,
    change: -125.45,
    changePercent: -0.26,
    isPositive: false,
    currency: 'INR',
    lastUpdated: Date.now() / 1000,
  },
  {
    symbol: '^NSEIT',
    name: 'NIFTY IT',
    value: 34789.25,
    change: 156.75,
    changePercent: 0.45,
    isPositive: true,
    currency: 'INR',
    lastUpdated: Date.now() / 1000,
  },
];

// Helper function to get fallback data for a specific stock
export const getFallbackStockData = (symbol: string): IndianStockQuote | null => {
  return fallbackIndianStocks.find(stock => stock.symbol === symbol) || null;
};

// Helper function to get fallback data for a specific index
export const getFallbackIndexData = (symbol: string): IndianMarketIndex | null => {
  return fallbackIndianIndices.find(index => index.symbol === symbol) || null;
};

// Helper function to get multiple fallback stocks
export const getFallbackStocksData = (symbols: string[]): IndianStockQuote[] => {
  return symbols
    .map(symbol => getFallbackStockData(symbol))
    .filter((stock): stock is IndianStockQuote => stock !== null);
};

// Generate realistic random variations for fallback data (for demo purposes)
export const generateRandomVariation = (baseValue: number, maxVariationPercent: number = 2): number => {
  const variation = (Math.random() - 0.5) * 2 * maxVariationPercent / 100;
  return baseValue * (1 + variation);
};

// Update fallback data with random variations (for demo purposes)
export const getUpdatedFallbackData = (): {
  stocks: IndianStockQuote[];
  indices: IndianMarketIndex[];
} => {
  const now = Date.now() / 1000;
  
  const updatedStocks = fallbackIndianStocks.map(stock => ({
    ...stock,
    price: generateRandomVariation(stock.price, 1),
    change: generateRandomVariation(stock.change, 50),
    changePercent: generateRandomVariation(stock.changePercent, 50),
    high: generateRandomVariation(stock.high, 0.5),
    low: generateRandomVariation(stock.low, 0.5),
    volume: Math.floor(generateRandomVariation(stock.volume, 20)),
    timestamp: now,
  }));

  const updatedIndices = fallbackIndianIndices.map(index => ({
    ...index,
    value: generateRandomVariation(index.value, 0.5),
    change: generateRandomVariation(index.change, 50),
    changePercent: generateRandomVariation(index.changePercent, 50),
    lastUpdated: now,
  }));

  return {
    stocks: updatedStocks,
    indices: updatedIndices,
  };
};
