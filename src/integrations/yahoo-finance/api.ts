import yahooFinanceClient, { rateLimitedRequest } from './client';
import {
  YahooQuoteResponse,
  YahooSearchResponse,
  IndianStockQuote,
  IndianMarketIndex,
  INDIAN_MARKET_HOURS,
  BatchIndianQuoteResult
} from './types';

// Helper function to determine market state
const getMarketState = (symbol: string, timestamp: number): 'REGULAR' | 'CLOSED' | 'PRE' | 'POST' => {
  const exchange = symbol.includes('.NS') ? 'NSE' : 'BSE';
  const marketHours = INDIAN_MARKET_HOURS[exchange];
  
  const date = new Date(timestamp * 1000);
  const istTime = new Date(date.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
  
  const currentHour = istTime.getHours();
  const currentMinute = istTime.getMinutes();
  const currentTime = currentHour * 60 + currentMinute;
  
  const openTime = marketHours.open.hour * 60 + marketHours.open.minute;
  const closeTime = marketHours.close.hour * 60 + marketHours.close.minute;
  
  // Check if it's a weekend
  const dayOfWeek = istTime.getDay();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return 'CLOSED';
  }
  
  if (currentTime < openTime) {
    return 'PRE';
  } else if (currentTime > closeTime) {
    return 'POST';
  } else {
    return 'REGULAR';
  }
};

// Get stock quote for Indian stocks
export const getIndianStockQuote = async (symbol: string): Promise<IndianStockQuote> => {
  return rateLimitedRequest(async () => {
    try {
      const response = await yahooFinanceClient.get<YahooQuoteResponse>(
        `/chart/${symbol}`,
        {
          params: {
            // Use yfinance standard parameters for real-time quote
            interval: '1m',
            period: '1d',

            // Regional settings for Indian market
            region: 'IN',
            lang: 'en-IN',

            // Include pre/post market data for better coverage
            includePrePost: true,

            // Additional parameters for data quality
            events: 'div,splits',
            includeAdjustedClose: true,
            corsDomain: 'finance.yahoo.com',
          },
        }
      );

      const result = response.data.chart.result[0];
      if (!result) {
        throw new Error(`No data found for symbol: ${symbol}`);
      }

      const meta = result.meta;
      const quote = result.indicators.quote[0];
      const latestIndex = quote.close.length - 1;

      // Get the most recent valid price
      let currentPrice = meta.regularMarketPrice;
      let latestClose = quote.close[latestIndex];
      
      // Use the latest close price if regularMarketPrice is not available
      if (!currentPrice && latestClose) {
        currentPrice = latestClose;
      }

      const previousClose = meta.previousClose || meta.chartPreviousClose;
      const change = currentPrice - previousClose;
      const changePercent = (change / previousClose) * 100;

      return {
        symbol,
        name: meta.symbol,
        price: currentPrice,
        change,
        changePercent,
        high: quote.high[latestIndex] || currentPrice,
        low: quote.low[latestIndex] || currentPrice,
        open: quote.open[latestIndex] || currentPrice,
        previousClose,
        volume: quote.volume[latestIndex] || 0,
        currency: meta.currency,
        exchange: meta.exchangeName,
        timestamp: meta.regularMarketTime,
        marketState: getMarketState(symbol, meta.regularMarketTime),
      };
    } catch (error) {
      console.error(`Error fetching Indian stock quote for ${symbol}:`, error);
      throw error;
    }
  });
};

// Get multiple Indian stock quotes with error handling
export const getBatchIndianStockQuotes = async (symbols: string[]): Promise<BatchIndianQuoteResult[]> => {
  try {
    const promises = symbols.map(async (symbol) => {
      try {
        const data = await getIndianStockQuote(symbol);
        return { symbol, data, error: null };
      } catch (error) {
        console.warn(`Failed to fetch quote for ${symbol}:`, error);
        return { symbol, data: null, error };
      }
    });

    const results = await Promise.allSettled(promises);
    
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          symbol: symbols[index],
          data: null,
          error: result.reason,
        };
      }
    });
  } catch (error) {
    console.error('Error fetching batch Indian stock quotes:', error);
    throw error;
  }
};

// Get Indian market indices
export const getIndianMarketIndices = async (): Promise<IndianMarketIndex[]> => {
  const indices = ['^NSEI', '^BSESN', '^NSEBANK', '^NSEIT'];
  
  try {
    const promises = indices.map(async (symbol) => {
      try {
        const quote = await getIndianStockQuote(symbol);
        return {
          symbol,
          name: getIndexName(symbol),
          value: quote.price,
          change: quote.change,
          changePercent: quote.changePercent,
          isPositive: quote.change >= 0,
          currency: quote.currency,
          lastUpdated: quote.timestamp,
        };
      } catch (error) {
        console.warn(`Failed to fetch index ${symbol}:`, error);
        return null;
      }
    });

    const results = await Promise.allSettled(promises);
    return results
      .filter((result): result is PromiseFulfilledResult<IndianMarketIndex> => 
        result.status === 'fulfilled' && result.value !== null
      )
      .map(result => result.value);
  } catch (error) {
    console.error('Error fetching Indian market indices:', error);
    throw error;
  }
};

// Search Indian stocks
export const searchIndianStocks = async (query: string): Promise<YahooSearchResponse> => {
  return rateLimitedRequest(async () => {
    try {
      const response = await yahooFinanceClient.get<YahooSearchResponse>(
        '/search',
        {
          params: {
            q: query,
            region: 'IN',
            lang: 'en-IN',
            quotesCount: 10,
            newsCount: 0,
          },
        }
      );

      return response.data;
    } catch (error) {
      console.error(`Error searching Indian stocks for query: ${query}`, error);
      throw error;
    }
  });
};



// Helper function to get index name
const getIndexName = (symbol: string): string => {
  const indexNames: Record<string, string> = {
    '^NSEI': 'NIFTY 50',
    '^BSESN': 'BSE SENSEX',
    '^NSEBANK': 'NIFTY BANK',
    '^NSEIT': 'NIFTY IT',
  };
  return indexNames[symbol] || symbol;
};

// Utility function to check if markets are open
export const areIndianMarketsOpen = (): boolean => {
  const now = new Date();
  const istTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
  
  const currentHour = istTime.getHours();
  const currentMinute = istTime.getMinutes();
  const currentTime = currentHour * 60 + currentMinute;
  
  const openTime = 9 * 60 + 15; // 9:15 AM
  const closeTime = 15 * 60 + 30; // 3:30 PM
  
  // Check if it's a weekend
  const dayOfWeek = istTime.getDay();
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return false;
  }
  
  return currentTime >= openTime && currentTime <= closeTime;
};

// Get next market open time
export const getNextMarketOpenTime = (): Date => {
  const now = new Date();
  const istTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Kolkata" }));
  
  const nextOpen = new Date(istTime);
  nextOpen.setHours(9, 15, 0, 0);
  
  // If market opening time has passed today, move to next day
  if (istTime.getHours() > 15 || (istTime.getHours() === 15 && istTime.getMinutes() >= 30)) {
    nextOpen.setDate(nextOpen.getDate() + 1);
  }
  
  // Skip weekends
  while (nextOpen.getDay() === 0 || nextOpen.getDay() === 6) {
    nextOpen.setDate(nextOpen.getDate() + 1);
  }
  
  return nextOpen;
};
