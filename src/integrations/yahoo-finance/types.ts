// Yahoo Finance API Response Types

export interface YahooQuoteResponse {
  chart: {
    result: Array<{
      meta: {
        currency: string;
        symbol: string;
        exchangeName: string;
        instrumentType: string;
        firstTradeDate: number;
        regularMarketTime: number;
        gmtoffset: number;
        timezone: string;
        exchangeTimezoneName: string;
        regularMarketPrice: number;
        chartPreviousClose: number;
        previousClose: number;
        scale: number;
        priceHint: number;
        currentTradingPeriod: {
          pre: TradingPeriod;
          regular: TradingPeriod;
          post: TradingPeriod;
        };
        tradingPeriods: TradingPeriod[][];
        dataGranularity: string;
        range: string;
        validRanges: string[];
      };
      timestamp: number[];
      indicators: {
        quote: Array<{
          open: number[];
          high: number[];
          low: number[];
          close: number[];
          volume: number[];
        }>;
        adjclose?: Array<{
          adjclose: number[];
        }>;
      };
    }>;
    error: any;
  };
}

export interface TradingPeriod {
  timezone: string;
  start: number;
  end: number;
  gmtoffset: number;
}

export interface YahooSearchResponse {
  explains: any[];
  count: number;
  quotes: Array<{
    exchange: string;
    shortname: string;
    quoteType: string;
    symbol: string;
    index: string;
    score: number;
    typeDisp: string;
    longname: string;
    exchDisp: string;
    sector?: string;
    industry?: string;
  }>;
  news: any[];
  nav: any[];
  lists: any[];
  researchReports: any[];
  screenerFieldResults: any[];
  totalTime: number;
  timeTaken: number;
}

// Normalized types for our application
export interface IndianStockQuote {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  high: number;
  low: number;
  open: number;
  previousClose: number;
  volume: number;
  currency: string;
  exchange: string;
  timestamp: number;
  marketState: 'REGULAR' | 'CLOSED' | 'PRE' | 'POST';
}

export interface IndianMarketIndex {
  symbol: string;
  name: string;
  value: number;
  change: number;
  changePercent: number;
  isPositive: boolean;
  currency: string;
  lastUpdated: number;
}

export interface BatchIndianQuoteResult {
  symbol: string;
  data: IndianStockQuote | null;
  error: any;
}

// Popular Indian stocks configuration
export const POPULAR_INDIAN_STOCKS_YAHOO = [
  { symbol: 'RELIANCE.NS', name: 'Reliance Industries Ltd' },
  { symbol: 'TCS.NS', name: 'Tata Consultancy Services Ltd' },
  { symbol: 'HDFCBANK.NS', name: 'HDFC Bank Ltd' },
  { symbol: 'INFY.NS', name: 'Infosys Ltd' },
  { symbol: 'HINDUNILVR.NS', name: 'Hindustan Unilever Ltd' },
  { symbol: 'ICICIBANK.NS', name: 'ICICI Bank Ltd' },
  { symbol: 'BHARTIARTL.NS', name: 'Bharti Airtel Ltd' },
  { symbol: 'ITC.NS', name: 'ITC Ltd' },
  { symbol: 'KOTAKBANK.NS', name: 'Kotak Mahindra Bank Ltd' },
  { symbol: 'LT.NS', name: 'Larsen & Toubro Ltd' },
  { symbol: 'SBIN.NS', name: 'State Bank of India' },
  { symbol: 'ASIANPAINT.NS', name: 'Asian Paints Ltd' },
  { symbol: 'MARUTI.NS', name: 'Maruti Suzuki India Ltd' },
  { symbol: 'BAJFINANCE.NS', name: 'Bajaj Finance Ltd' },
  { symbol: 'HCLTECH.NS', name: 'HCL Technologies Ltd' },
];

export const POPULAR_BSE_STOCKS = [
  { symbol: 'RELIANCE.BO', name: 'Reliance Industries Ltd' },
  { symbol: 'TCS.BO', name: 'Tata Consultancy Services Ltd' },
  { symbol: 'HDFCBANK.BO', name: 'HDFC Bank Ltd' },
  { symbol: 'INFY.BO', name: 'Infosys Ltd' },
  { symbol: 'HINDUNILVR.BO', name: 'Hindustan Unilever Ltd' },
];

export const INDIAN_MARKET_INDICES = [
  { symbol: '^NSEI', name: 'NIFTY 50', exchange: 'NSE' },
  { symbol: '^BSESN', name: 'BSE SENSEX', exchange: 'BSE' },
  { symbol: '^NSEBANK', name: 'NIFTY BANK', exchange: 'NSE' },
  { symbol: '^NSEIT', name: 'NIFTY IT', exchange: 'NSE' },
];

// Market hours for Indian exchanges (IST)
export const INDIAN_MARKET_HOURS = {
  NSE: {
    open: { hour: 9, minute: 15 }, // 9:15 AM IST
    close: { hour: 15, minute: 30 }, // 3:30 PM IST
    timezone: 'Asia/Kolkata',
  },
  BSE: {
    open: { hour: 9, minute: 15 }, // 9:15 AM IST
    close: { hour: 15, minute: 30 }, // 3:30 PM IST
    timezone: 'Asia/Kolkata',
  },
};

// Currency formatting for Indian Rupee
export const formatINR = (amount: number): string => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// Number formatting for Indian numbering system (lakhs, crores)
export const formatIndianNumber = (num: number): string => {
  if (num >= ********) { // 1 crore
    return `₹${(num / ********).toFixed(2)}Cr`;
  } else if (num >= 100000) { // 1 lakh
    return `₹${(num / 100000).toFixed(2)}L`;
  } else if (num >= 1000) { // 1 thousand
    return `₹${(num / 1000).toFixed(2)}K`;
  }
  return `₹${num.toFixed(2)}`;
};

// Helper function to get stock name from symbol
export const getStockName = (symbol: string): string => {
  const stock = POPULAR_INDIAN_STOCKS_YAHOO.find(s => s.symbol === symbol) ||
                POPULAR_BSE_STOCKS.find(s => s.symbol === symbol);
  return stock?.name || symbol;
};

// Helper function to determine if symbol is Indian
export const isIndianStock = (symbol: string): boolean => {
  return symbol.endsWith('.NS') || symbol.endsWith('.BO') || symbol.startsWith('^NSE') || symbol.startsWith('^BSE');
};



// Test symbols for validation
export const TEST_INDIAN_STOCKS = [
  'RELIANCE.NS',    // Reliance Industries
  'TCS.NS',         // Tata Consultancy Services
  'HDFCBANK.NS',    // HDFC Bank
  'INFY.NS',        // Infosys
  'HINDUNILVR.NS',  // Hindustan Unilever
  'ICICIBANK.NS',   // ICICI Bank
  'BHARTIARTL.NS',  // Bharti Airtel
  'ITC.NS',         // ITC
  'KOTAKBANK.NS',   // Kotak Mahindra Bank
  'LT.NS',          // Larsen & Toubro
];

export const TEST_INDIAN_INDICES = [
  '^NSEI',          // NIFTY 50
  '^BSESN',         // BSE SENSEX
  '^NSEBANK',       // NIFTY BANK
  '^NSEIT',         // NIFTY IT
];
