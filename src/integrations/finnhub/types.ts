// Finnhub API Response Types

export interface StockQuote {
  c: number;  // Current price
  d: number;  // Change
  dp: number; // Percent change
  h: number;  // High price of the day
  l: number;  // Low price of the day
  o: number;  // Open price of the day
  pc: number; // Previous close price
  t: number;  // Timestamp
}

export interface CompanyProfile {
  country: string;
  currency: string;
  exchange: string;
  finnhubIndustry: string;
  ipo: string;
  logo: string;
  marketCapitalization: number;
  name: string;
  phone: string;
  shareOutstanding: number;
  ticker: string;
  weburl: string;
}



export interface MarketNews {
  category: string;
  datetime: number;
  headline: string;
  id: number;
  image: string;
  related: string;
  source: string;
  summary: string;
  url: string;
}

export interface SymbolSearch {
  count: number;
  result: Array<{
    description: string;
    displaySymbol: string;
    symbol: string;
    type: string;
  }>;
}

export interface BatchQuoteResult {
  symbol: string;
  data: StockQuote | null;
  error: any;
}

// Custom types for our application
export interface StockData {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  high: number;
  low: number;
  open: number;
  previousClose: number;
  timestamp: number;
  currency?: string;
  exchange?: string;
}

export interface MarketIndex {
  symbol: string;
  name: string;
  value: number;
  change: number;
  changePercent: number;
  isPositive: boolean;
}

export interface StockListItem {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume?: number;
  marketCap?: number;
}



export interface MarketSummary {
  totalStocks: number;
  advancers: number;
  decliners: number;
  unchanged: number;
  topGainers: StockListItem[];
  topLosers: StockListItem[];
}

// Indian Market Specific Types
export interface IndianStock {
  symbol: string;
  name: string;
  sector: string;
  exchange: 'NSE' | 'BSE';
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap: number;
}

export interface IndianMarketIndex {
  name: string;
  symbol: string;
  value: number;
  change: number;
  changePercent: number;
  high: number;
  low: number;
  open: number;
  previousClose: number;
}

// Watchlist Types
export interface WatchlistItem {
  id: string;
  symbol: string;
  name: string;
  addedAt: string;
  alertPrice?: number;
  notes?: string;
}

export interface Watchlist {
  id: string;
  name: string;
  description?: string;
  items: WatchlistItem[];
  createdAt: string;
  updatedAt: string;
}



// API Error Types
export interface FinnhubError {
  error: string;
  message?: string;
  status?: number;
}

// Market Status Types
export interface MarketStatus {
  exchange: string;
  holiday: string | null;
  isOpen: boolean;
  session: string;
  timezone: string;
  t: number;
}

// Popular Stock Lists
export const POPULAR_INDIAN_STOCKS = [
  'RELIANCE.NS',
  'TCS.NS',
  'HDFCBANK.NS',
  'INFY.NS',
  'HINDUNILVR.NS',
  'ICICIBANK.NS',
  'KOTAKBANK.NS',
  'BHARTIARTL.NS',
  'ITC.NS',
  'SBIN.NS',
  'LT.NS',
  'AXISBANK.NS',
  'MARUTI.NS',
  'ASIANPAINT.NS',
  'BAJFINANCE.NS'
];

export const POPULAR_US_STOCKS = [
  'AAPL',
  'GOOGL',
  'MSFT',
  'AMZN',
  'TSLA',
  'META',
  'NVDA',
  'NFLX',
  'AMD',
  'BABA',
  'CRM',
  'ORCL',
  'ADBE',
  'PYPL',
  'INTC'
];

export const MAJOR_INDICES = [
  { symbol: '^NSEI', name: 'NIFTY 50', exchange: 'NSE' },
  { symbol: '^BSESN', name: 'BSE SENSEX', exchange: 'BSE' },
  { symbol: '^GSPC', name: 'S&P 500', exchange: 'NYSE' },
  { symbol: '^IXIC', name: 'NASDAQ', exchange: 'NASDAQ' },
  { symbol: '^DJI', name: 'DOW JONES', exchange: 'NYSE' },
  { symbol: '^VIX', name: 'VIX', exchange: 'CBOE' }
];

// Indian Market Sectors
export const INDIAN_SECTORS = [
  'Banking',
  'Information Technology',
  'Pharmaceuticals',
  'Automobiles',
  'Oil & Gas',
  'Metals & Mining',
  'FMCG',
  'Telecommunications',
  'Power',
  'Real Estate',
  'Textiles',
  'Chemicals'
];
