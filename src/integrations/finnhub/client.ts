
import axios from 'axios';

// Use proxy server for local development and Vercel serverless function for production
const isProduction = import.meta.env.PROD;
const isDevelopment = import.meta.env.DEV;

// Determine the correct base URL based on environment
const getBaseUrl = () => {
  if (isProduction) {
    // In production, use the Vercel serverless function
    return '/api/finnhub';
  } else if (isDevelopment) {
    // In development, try proxy server first, fallback to direct API
    return import.meta.env.VITE_PROXY_URL || 'http://localhost:3001/api/finnhub';
  }
  return '/api/finnhub';
};

const PROXY_BASE_URL = getBaseUrl();
const API_KEY = import.meta.env.VITE_FINNHUB_API_KEY;

// Enhanced logging for debugging
console.log('Finnhub Client Configuration:', {
  isProduction,
  isDevelopment,
  baseUrl: PROXY_BASE_URL,
  hasApiKey: !!API_KEY,
  environment: import.meta.env.MODE
});

if (!API_KEY && isDevelopment) {
  console.warn('⚠️ Finnhub API key not found. Please set VITE_FINNHUB_API_KEY in your .env.local file.');
}

const finnhubClient = axios.create({
  baseURL: PROXY_BASE_URL,
  timeout: 30000, // 30 second timeout (increased from 15s)
  headers: {
    'Content-Type': 'application/json',
  },
});

// Enhanced error interceptor for debugging
finnhubClient.interceptors.response.use(
  (response) => {
    console.log(`✅ API Success: ${response.config?.url}`, {
      status: response.status,
      dataSize: JSON.stringify(response.data).length
    });
    return response;
  },
  (error) => {
    const errorDetails = {
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      responseData: error.response?.data
    };

    console.error(`❌ API Error: ${error.config?.url}`, errorDetails);

    // Provide more helpful error messages
    if (error.response?.status === 403) {
      console.error('🔑 API Key issue: Check if VITE_FINNHUB_API_KEY is set correctly');
    } else if (error.response?.status === 429) {
      console.error('⏰ Rate limit exceeded: Too many requests to Finnhub API');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Connection refused: Check if proxy server is running (npm run proxy)');
    }

    return Promise.reject(error);
  }
);

// Rate limiting is handled by the proxy server

// Enhanced API functions
export const getStockQuote = async (symbol: string) => {
  try {
    const response = await finnhubClient.get('/quote', {
      params: { symbol },
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching stock quote for ${symbol}:`, error);
    throw error;
  }
};

export const getCompanyProfile = async (symbol: string) => {
  try {
    const response = await finnhubClient.get('/stock/profile2', {
      params: { symbol },
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching company profile for ${symbol}:`, error);
    throw error;
  }
};

export const getStockCandles = async (
  symbol: string,
  resolution: string,
  from: number,
  to: number
) => {
  try {
    const response = await finnhubClient.get('/stock/candle', {
      params: { symbol, resolution, from, to },
    });
    return response.data;
  } catch (error) {
    console.error(`Error fetching stock candles for ${symbol}:`, error);
    throw error;
  }
};

export const getMarketNews = async (category: string = 'general') => {
  try {
    const response = await finnhubClient.get('/news', {
      params: { category },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching market news:', error);
    throw error;
  }
};

export const searchSymbols = async (query: string) => {
  try {
    const response = await finnhubClient.get('/search', {
      params: { q: query },
    });
    return response.data;
  } catch (error) {
    console.error(`Error searching symbols for ${query}:`, error);
    throw error;
  }
};

// Batch quote fetching for multiple symbols
export const getBatchQuotes = async (symbols: string[]) => {
  try {
    const promises = symbols.map(symbol => getStockQuote(symbol));
    const results = await Promise.allSettled(promises);

    return results.map((result, index) => ({
      symbol: symbols[index],
      data: result.status === 'fulfilled' ? result.value : null,
      error: result.status === 'rejected' ? result.reason : null,
    }));
  } catch (error) {
    console.error('Error fetching batch quotes:', error);
    throw error;
  }
};

// Legacy function for backward compatibility
export const getStockData = getStockQuote;

export default finnhubClient;
