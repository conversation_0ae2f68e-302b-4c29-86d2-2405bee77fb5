

import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import SplashScreen from "@/components/SplashScreen";
import { useSplashScreen } from "@/hooks/useSplashScreen";

import { CachingDemo } from "./components/CachingDemo";
import ApiDebugger from "./components/ApiDebugger";
import PerformanceDashboard from "./components/PerformanceDashboard";

import Index from "./pages/Index";
import Dashboard from "./pages/Dashboard";
import MarketDashboardPage from "./pages/MarketDashboard";
import Insights from "./pages/Insights";
import News from "./pages/News";
import Education from "./pages/Education";
import CaseStudies from "./pages/CaseStudies";
import About from "./pages/About";
import Feedback from "./pages/Feedback";
import Admin from "./pages/Admin";
import NotFound from "./pages/NotFound";

// Detail pages
import InsightDetail from "./pages/InsightDetail";
import NewsDetail from "./pages/NewsDetail";
import EducationDetail from "./pages/EducationDetail";
import CaseStudyDetail from "./pages/CaseStudyDetail";
import StockDetail from "./pages/StockDetail";

// Admin pages
import CreateInsight from "./pages/admin/CreateInsight";
import EditInsight from "./pages/admin/EditInsight";
import CreateNews from "./pages/admin/CreateNews";
import EditNews from "./pages/admin/EditNews";
import CreateEducation from "./pages/admin/CreateEducation";
import EditEducation from "./pages/admin/EditEducation";
import CreateCaseStudy from "./pages/admin/CreateCaseStudy";
import EditCaseStudy from "./pages/admin/EditCaseStudy";
import CreateQuarterlyOutlook from "./pages/admin/CreateQuarterlyOutlook";
import EditQuarterlyOutlook from "./pages/admin/EditQuarterlyOutlook";
import CreatePage from "./pages/admin/CreatePage";
import EditPage from "./pages/admin/EditPage";
import Layout from "./components/Layout";
import USQuarterlyMarkets from "./pages/USQuarterlyMarkets";
import IndianQuarterlyMarkets from "./pages/IndianQuarterlyMarkets";
import YahooFinanceTest from "./components/YahooFinanceTest";
import YahooFinanceIndianTest from "./components/YahooFinanceIndianTest";
import APIMigrationTest from "./components/APIMigrationTest";
import PageDebugger from "./components/PageDebugger";
import QuarterlyOutlookAdminDebug from "./components/QuarterlyOutlookAdminDebug";
import SplashScreenDemo from "./components/SplashScreenDemo";

import TickerSpeedTest from "./components/TickerSpeedTest";





const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
      // Enable request deduplication
      refetchOnMount: false,
      refetchOnReconnect: 'always',
      // Retry configuration for better reliability
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 2 times for other errors
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
      retryDelay: 2000,
    },
  },
});

const App = () => {
  const { showSplash, handleSplashComplete, isAppReady } = useSplashScreen({
    duration: 3000
  });

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Toaster />
        <Sonner />

        {/* Show splash screen on first load */}
        {showSplash && (
          <SplashScreen onComplete={handleSplashComplete} duration={3000} />
        )}

        {/* Main app content - only render when app is ready */}
        {isAppReady && (
          <BrowserRouter>
            <Layout>
              <Routes>
              <Route path="/" element={<Index />} />
              {/* <Route path="/dashboard" element={
                // <ProtectedRoute>
                //   <Dashboard />
                // </ProtectedRoute>
              } /> */}
              <Route path="/market-dashboard" element={<MarketDashboardPage />} />

     
          {/* Content Pages */}
          <Route path="/insights" element={<Insights />} />
          <Route path="/insights/:id" element={<InsightDetail />} />
          <Route path="/news" element={<News />} />
          <Route path="/news/:id" element={<NewsDetail />} />
          <Route path="/education" element={<Education />} />
          <Route path="/education/:id" element={<EducationDetail />} />
          <Route path="/case-studies" element={<CaseStudies />} />
          <Route path="/case-studies/:id" element={<CaseStudyDetail />} />
          <Route path="/about" element={<About />} />
          {/* <Route path="/feedback" element={<Feedback />} /> */}

          {/* Quarterly Market Routes */}
          <Route path="/markets/us-quarterly" element={<USQuarterlyMarkets />} />
          <Route path="/markets/indian-quarterly" element={<IndianQuarterlyMarkets />} />

          {/* Stock Detail Page */}
          <Route path="/stocks/:symbol" element={<StockDetail />} />

          {/* Admin Routes */}
          <Route path="/admin" element={
            <ProtectedRoute requireAdmin>
              <Admin />
            </ProtectedRoute>
          } />
          <Route path="/admin/insights/new" element={
            <ProtectedRoute requireAdmin>
              <CreateInsight />
            </ProtectedRoute>
          } />
          <Route path="/admin/insights/:id/edit" element={
            <ProtectedRoute requireAdmin>
              <EditInsight />
            </ProtectedRoute>
          } />
          <Route path="/admin/news/new" element={
            <ProtectedRoute requireAdmin>
              <CreateNews />
            </ProtectedRoute>
          } />
          <Route path="/admin/news/:id/edit" element={
            <ProtectedRoute requireAdmin>
              <EditNews />
            </ProtectedRoute>
          } />
          <Route path="/admin/education/new" element={
            <ProtectedRoute requireAdmin>
              <CreateEducation />
            </ProtectedRoute>
          } />
          <Route path="/admin/education/:id/edit" element={
            <ProtectedRoute requireAdmin>
              <EditEducation />
            </ProtectedRoute>
          } />
          <Route path="/admin/case-studies/new" element={
            <ProtectedRoute requireAdmin>
              <CreateCaseStudy />
            </ProtectedRoute>
          } />
          <Route path="/admin/case-studies/:id/edit" element={
            <ProtectedRoute requireAdmin>
              <EditCaseStudy />
            </ProtectedRoute>
          } />
          <Route path="/admin/quarterly-outlook/new" element={
            <ProtectedRoute requireAdmin>
              <CreateQuarterlyOutlook />
            </ProtectedRoute>
          } />
          <Route path="/admin/quarterly-outlook/edit/:id" element={
            <ProtectedRoute requireAdmin>
              <EditQuarterlyOutlook />
            </ProtectedRoute>
          } />
          <Route path="/admin/pages/new" element={
            <ProtectedRoute requireAdmin>
              <CreatePage />
            </ProtectedRoute>
          } />
          <Route path="/admin/pages/:id/edit" element={
            <ProtectedRoute requireAdmin>
              <EditPage />
            </ProtectedRoute>
          } />
          <Route path="/test/yahoo-finance" element={<YahooFinanceTest />} />
          <Route path="/test/yahoo-finance-indian" element={<YahooFinanceIndianTest />} />

          <Route path="/test/ticker-speed" element={<TickerSpeedTest />} />
          <Route path="/test/performance" element={<PerformanceDashboard />} />

          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
              </Routes>
            </Layout>
          </BrowserRouter>
        )}
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
