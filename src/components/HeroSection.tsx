
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ArrowUp, ArrowDown, Wifi, WifiOff } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getStockQuote } from '@/integrations/finnhub/client';
import { useIndianMarketIndicesWithFallback } from '@/hooks/useIndianStockData';


import MarketSummaryCards from '@/components/MarketSummaryCards';
import QuarterlyOutlookSection from '@/components/QuarterlyOutlookSection';
import QuarterlyOutlookDebug from '@/components/QuarterlyOutlookDebug';
import ScrollArrow from '@/components/ScrollArrow';
import CompanyInfoModal from '@/components/CompanyInfoModal';



// Fallback data for when API is unavailable
const fallbackMarketIndices = [
  { name: "NIFTY 50", value: 22196.95, change: 0.37, isPositive: true },
  { name: "BSE SENSEX", value: 73088.33, change: 0.43, isPositive: true },
  { name: "S&P 500", value: 5069.76, change: 0.41, isPositive: true },
  { name: "NASDAQ", value: 15927.90, change: 0.85, isPositive: true },
  { name: "DOW", value: 38239.98, change: -0.12, isPositive: false },
];

// Separate Indian and US indices configuration
const indianIndicesConfig = [
  { name: "NIFTY 50", symbol: "^NSEI", fallback: fallbackMarketIndices[0] },
  { name: "BSE SENSEX", symbol: "^BSESN", fallback: fallbackMarketIndices[1] },
];

const usIndicesConfig = [
  { name: "S&P 500", symbol: "^GSPC", fallback: fallbackMarketIndices[2] },
  { name: "NASDAQ", symbol: "^IXIC", fallback: fallbackMarketIndices[3] },
  { name: "DOW", symbol: "^DJI", fallback: fallbackMarketIndices[4] },
];

// Custom hook for fetching market data
const useMarketData = () => {
  // Finnhub queries for US markets only
  const finnhubQueries = usIndicesConfig.map(config =>
    useQuery({
      queryKey: ['marketIndex', config.symbol],
      queryFn: () => getStockQuote(config.symbol),
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      retryDelay: 1000,
    })
  );

  // Yahoo Finance queries for Indian markets
  const { data: indianIndices, isUsingFallback: indianFallback } = useIndianMarketIndicesWithFallback();

  // Process Finnhub data for US indices
  const finnhubIndices = usIndicesConfig.map((config, index) => {
    const query = finnhubQueries[index];

    if (query.isSuccess && query.data && typeof query.data === 'object' && 'c' in query.data) {
      const data = query.data as { c: number; pc: number; d?: number; dp?: number };
      const currentPrice = data.c;
      const previousClose = data.pc;
      const change = previousClose ? ((currentPrice - previousClose) / previousClose) * 100 : 0;

      return {
        name: config.name,
        value: currentPrice,
        change: Number(change.toFixed(2)),
        isPositive: change >= 0,
        isLive: true,
        currency: 'USD', // US indices use USD
      };
    }

    // Return fallback data if API fails
    return {
      ...config.fallback,
      isLive: false,
      currency: 'USD', // US indices use USD
    };
  });

  // Process Indian indices from Yahoo Finance
  const processedIndianIndices = indianIndices?.map(index => ({
    name: index.name,
    value: index.value,
    change: index.changePercent,
    isPositive: index.isPositive,
    isLive: !indianFallback,
    currency: 'INR',
  })) || [];

  // Combine all indices, prioritizing Yahoo Finance data for Indian indices
  const combinedIndices = [
    ...processedIndianIndices,
    ...finnhubIndices.filter(index =>
      !index.name.includes('NIFTY') && !index.name.includes('BSE')
    ),
  ];

  const isLoading = finnhubQueries.some(q => q.isLoading);
  const hasError = finnhubQueries.some(q => q.isError);
  const allSuccess = finnhubQueries.every(q => q.isSuccess) && !indianFallback;

  return {
    marketIndices: combinedIndices,
    isLoading,
    hasError,
    allSuccess,
  };
};

const HeroSection: React.FC = () => {
  const { marketIndices, isLoading, hasError, allSuccess } = useMarketData();
  const [isCompanyInfoOpen, setIsCompanyInfoOpen] = useState(false);

  const handleScrollArrowClick = () => {
    setIsCompanyInfoOpen(true);
  };

  return (
    <>
      <div className="relative min-h-screen">
        <div className="container py-8">
          {/* Quarterly Outlook Section */}
          <QuarterlyOutlookSection />

      <div className="flex flex-col gap-8">
        {/* <div className="text-center mb-4">
          <h1 className="text-4xl font-bold tracking-tight mb-2">Bridging Markets, Empowering Investors</h1>
          <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
            Access accurate, Shariah-compliant financial education and data-driven insights
            to navigate Indian and U.S. markets with confidence.
          </p>
        </div> */}
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex justify-between">
                <span>Market Performance</span>
                <span className="text-sm font-medium text-muted-foreground">Last 6 Months</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={chartData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#333" />
                    <XAxis dataKey="name" stroke="#888" />
                    <YAxis yAxisId="left" stroke="#888" />
                    <YAxis yAxisId="right" orientation="right" stroke="#888" />
                    <RechartsTooltip
                      contentStyle={{
                        backgroundColor: "hsl(222 47% 11%)",
                        borderColor: "hsl(217 32% 17%)",
                        color: "#fff",
                      }}
                    />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="nifty"
                      stroke="#3b82f6"
                      name="NIFTY 50"
                      activeDot={{ r: 8 }}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="sp500"
                      stroke="#10b981"
                      name="S&P 500"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card> */}

         
        </div>
      </div>

      {/* Market Summary Cards */}
      <div className="mt-12">
        <MarketSummaryCards />
      </div>
        </div>

        {/* Scroll Arrow Indicator */}
        {/* <ScrollArrow onClick={handleScrollArrowClick} /> */}
      </div>

      {/* Company Info Modal */}
      <CompanyInfoModal
        isOpen={isCompanyInfoOpen}
        onClose={() => setIsCompanyInfoOpen(false)}
      />
    </>
  );
};

export default HeroSection;
