import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Target, 
  Eye, 
  Award, 
  Calendar, 
  TrendingUp, 
  Shield, 
  Globe, 
  Users,
  CheckCircle
} from 'lucide-react';

interface CompanyInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const CompanyInfoModal: React.FC<CompanyInfoModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-background border-b p-6 flex justify-between items-center">
          <h2 className="text-2xl font-bold">About <PERSON>'s Investmentss</h2>
          <button
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="p-6 space-y-8">
          {/* Mission & Vision */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  Our Mission
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  To democratize financial education and provide accessible, accurate, and Shariah-compliant 
                  investment guidance that empowers individuals to make informed decisions in both Indian and 
                  U.S. stock markets.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5 text-green-600" />
                  Our Vision
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground leading-relaxed">
                  To become the leading platform for cross-border financial education, bridging the gap 
                  between Indian and U.S. markets while maintaining the highest standards of ethical 
                  and Shariah-compliant investing.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Key Services */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                Key Services
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">Market Analysis</h4>
                    <p className="text-sm text-muted-foreground">Real-time insights and quarterly market outlooks</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">Educational Content</h4>
                    <p className="text-sm text-muted-foreground">Comprehensive learning resources and tutorials</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">Case Studies</h4>
                    <p className="text-sm text-muted-foreground">Real-world investment scenarios and outcomes</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">News & Updates</h4>
                    <p className="text-sm text-muted-foreground">Latest financial news and market developments</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">Portfolio Insights</h4>
                    <p className="text-sm text-muted-foreground">Data-driven investment recommendations</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold">Shariah Compliance</h4>
                    <p className="text-sm text-muted-foreground">Ethical investing guidance and screening</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Company Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-orange-600" />
                  Founded
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">2020</p>
                <p className="text-sm text-muted-foreground">4+ years of market expertise</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-blue-600" />
                  Markets Covered
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">2</p>
                <p className="text-sm text-muted-foreground">Indian & U.S. Stock Markets</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-green-600" />
                  Community
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-2xl font-bold">10K+</p>
                <p className="text-sm text-muted-foreground">Active learners and investors</p>
              </CardContent>
            </Card>
          </div>

          {/* What Makes Us Unique */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-yellow-600" />
                What Makes Syed's Investmentss Unique
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <Shield className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold">Shariah-Compliant Focus</h4>
                      <p className="text-sm text-muted-foreground">
                        Specialized in ethical investing principles with comprehensive Shariah screening
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Globe className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold">Cross-Border Expertise</h4>
                      <p className="text-sm text-muted-foreground">
                        Unique insights into both Indian and U.S. markets with cultural understanding
                      </p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start gap-3">
                    <TrendingUp className="h-5 w-5 text-purple-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold">Data-Driven Approach</h4>
                      <p className="text-sm text-muted-foreground">
                        Real-time market data integration with comprehensive analysis tools
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Users className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <h4 className="font-semibold">Educational First</h4>
                      <p className="text-sm text-muted-foreground">
                        Commitment to financial literacy and empowering informed decision-making
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Values */}
          <Card>
            <CardHeader>
              <CardTitle>Our Core Values</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary" className="px-3 py-1">Transparency</Badge>
                <Badge variant="secondary" className="px-3 py-1">Integrity</Badge>
                <Badge variant="secondary" className="px-3 py-1">Education</Badge>
                <Badge variant="secondary" className="px-3 py-1">Innovation</Badge>
                <Badge variant="secondary" className="px-3 py-1">Accessibility</Badge>
                <Badge variant="secondary" className="px-3 py-1">Ethical Investing</Badge>
                <Badge variant="secondary" className="px-3 py-1">Cultural Sensitivity</Badge>
                <Badge variant="secondary" className="px-3 py-1">Data Accuracy</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CompanyInfoModal;
