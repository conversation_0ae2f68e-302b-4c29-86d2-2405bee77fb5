import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowUp, ArrowDown, Search, TrendingUp, TrendingDown, Activity, BarChart3, ExternalLink, Wifi, WifiOff } from 'lucide-react';
import { useMarketIndices, usePopularStocks, useSymbolSearch } from '@/hooks/useStockData';
import { SymbolSearch } from '@/integrations/finnhub/types';
import { useQuery } from '@tanstack/react-query';
import { getStockQuote } from '@/integrations/finnhub/client';
import {
  useIndianMarketIndicesWithFallback,
  useBatchIndianStockQuotesWithFallback,
  useIndianStockMovers,
  useIndianMarketStatus
} from '@/hooks/useIndianStockData';
import { POPULAR_INDIAN_STOCKS_YAHOO, formatINR } from '@/integrations/yahoo-finance/types';
import StockChart from '@/components/StockChart';

// Fallback data for when API is unavailable
const fallbackMarketIndices = [
  { name: "NIFTY 50", value: 22196.95, change: 0.37, isPositive: true },
  { name: "BSE SENSEX", value: 73088.33, change: 0.43, isPositive: true },
  { name: "S&P 500", value: 5069.76, change: 0.41, isPositive: true },
  { name: "NASDAQ", value: 15927.90, change: 0.85, isPositive: true },
  { name: "DOW", value: 38239.98, change: -0.12, isPositive: false },
];

// Separate Indian and US indices configuration
const indianIndicesConfig = [
  { name: "NIFTY 50", symbol: "^NSEI", fallback: fallbackMarketIndices[0] },
  { name: "BSE SENSEX", symbol: "^BSESN", fallback: fallbackMarketIndices[1] },
];

const usIndicesConfig = [
  { name: "S&P 500", symbol: "^GSPC", fallback: fallbackMarketIndices[2] },
  { name: "NASDAQ", symbol: "^IXIC", fallback: fallbackMarketIndices[3] },
  { name: "DOW", symbol: "^DJI", fallback: fallbackMarketIndices[4] },
];

// Custom hook for fetching market data
const useMarketData = () => {
  // Finnhub queries for US markets only
  const finnhubQueries = usIndicesConfig.map(config =>
    useQuery({
      queryKey: ['marketIndex', config.symbol],
      queryFn: () => getStockQuote(config.symbol),
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      retryDelay: 1000,
    })
  );

  // Yahoo Finance queries for Indian markets
  const { data: indianIndices, isUsingFallback: indianFallback } = useIndianMarketIndicesWithFallback();

  // Process Finnhub data for US indices
  const finnhubIndices = usIndicesConfig.map((config, index) => {
    const query = finnhubQueries[index];

    if (query.isSuccess && query.data && typeof query.data === 'object' && 'c' in query.data) {
      const data = query.data as { c: number; pc: number; d?: number; dp?: number };
      const currentPrice = data.c;
      const previousClose = data.pc;
      const change = previousClose ? ((currentPrice - previousClose) / previousClose) * 100 : 0;

      return {
        name: config.name,
        value: currentPrice,
        change: Number(change.toFixed(2)),
        isPositive: change >= 0,
        isLive: true,
        currency: 'USD', // US indices use USD
      };
    }

    // Return fallback data if API fails
    return {
      ...config.fallback,
      isLive: false,
      currency: 'USD', // US indices use USD
    };
  });

  // Process Indian indices from Yahoo Finance
  const processedIndianIndices = indianIndices?.map(index => ({
    name: index.name,
    value: index.value,
    change: index.changePercent,
    isPositive: index.isPositive,
    isLive: !indianFallback,
    currency: 'INR',
  })) || [];

  // Combine all indices, prioritizing Yahoo Finance data for Indian indices
  const combinedIndices = [
    ...processedIndianIndices,
    ...finnhubIndices.filter(index =>
      !index.name.includes('NIFTY') && !index.name.includes('BSE')
    ),
  ];

  const isLoading = finnhubQueries.some(q => q.isLoading);
  const hasError = finnhubQueries.some(q => q.isError);
  const allSuccess = finnhubQueries.every(q => q.isSuccess) && !indianFallback;

  return {
    marketIndices: combinedIndices,
    isLoading,
    hasError,
    allSuccess,
  };
};

const MarketDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<'indices' | 'indian-stocks' | 'gainers' | 'losers' | 'charts'>('indices');
  const [featuredStock, setFeaturedStock] = useState('RELIANCE.NS');

  // Get market indices data using the new hook
  const { marketIndices, isLoading: marketIndicesLoading, hasError: marketIndicesError, allSuccess } = useMarketData();

  // Get market indices data (Finnhub) - keeping for other tabs
  const indicesData = useMarketIndices();

  // Get Indian market data (Yahoo Finance)
  const { data: indianIndices, isUsingFallback: indianIndicesFallback } = useIndianMarketIndicesWithFallback();
  const { data: indianStocksData } = useBatchIndianStockQuotesWithFallback(
    POPULAR_INDIAN_STOCKS_YAHOO.slice(0, 10).map(stock => stock.symbol)
  );
  const { data: marketStatus } = useIndianMarketStatus();
  const { gainers: indianGainers, losers: indianLosers } = useIndianStockMovers();

  // Get popular stocks for gainers/losers analysis - use proper API routing
  const { data: stocksData } = usePopularStocks();
  
  // Search functionality
  const { data: searchResults } = useSymbolSearch(searchQuery, searchQuery.length > 2);

  // Process stocks data for gainers and losers
  const processedStocks = stocksData?.filter(stock => stock.data && !stock.error)
    .map(stock => ({
      symbol: stock.symbol,
      name: stock.symbol.replace('.NS', '').replace('.BO', ''),
      price: stock.data!.c,
      change: stock.data!.d,
      changePercent: stock.data!.dp,
    })) || [];

  const topGainers = processedStocks
    .filter(stock => stock.changePercent > 0)
    .sort((a, b) => b.changePercent - a.changePercent)
    .slice(0, 5);

  const topLosers = processedStocks
    .filter(stock => stock.changePercent < 0)
    .sort((a, b) => a.changePercent - b.changePercent)
    .slice(0, 5);

  const formatPrice = (price: number, symbol: string): string => {
    if (symbol.endsWith('.NS') || symbol.endsWith('.BO')) {
      return `₹${price.toLocaleString('en-IN', { maximumFractionDigits: 2 })}`;
    }
    return `$${price.toLocaleString('en-US', { maximumFractionDigits: 2 })}`;
  };

  // Render Indian market indices (Yahoo Finance)
  const renderIndianIndices = () => (
    <div className="space-y-4">
      {/* Market Status Banner */}
      {marketStatus && (
        <Card className=" from-blue-50 to-indigo-50 border-blue-900">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-3 h-3 rounded-full ${
                  marketStatus.isOpen ? 'bg-green-500 animate-pulse' : 'bg-red-500'
                }`} />
                <span className="font-medium">
                  Indian Markets: {marketStatus.isOpen ? 'OPEN' : 'CLOSED'}
                </span>
              </div>
              {indianIndicesFallback && (
                <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">
                  Using fallback data
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Indian Market Indices */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {indianIndices?.map((index, idx) => (
          <Card key={idx} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <h3 className="font-semibold text-sm text-gray-600">{index.name}</h3>
                <Activity className="h-4 w-4 text-blue-500" />
              </div>
              <div className="space-y-1">
                <p className="text-2xl font-bold">
                  {index.value.toLocaleString('en-IN')}
                </p>
                <div className={`flex items-center text-sm ${
                  index.isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                  {index.isPositive ? (
                    <ArrowUp className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDown className="h-4 w-4 mr-1" />
                  )}
                  <span>
                    {Math.abs(index.change).toFixed(2)} ({Math.abs(index.changePercent).toFixed(2)}%)
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  // Render Indian stocks section
  const renderIndianStocks = () => (
    <div className="space-y-6">
      {/* Indian Stock Gainers and Losers */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-lg">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <span className="ml-2">Indian Top Gainers</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {indianGainers.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No gainers data available</p>
              ) : (
                indianGainers.map((stock) => (
                  <div
                    key={stock.symbol}
                    className="flex justify-between items-center py-2 border-b last:border-0  cursor-pointer rounded px-2 -mx-2 transition-colors"
                    onClick={() => navigate(`/stocks/${stock.symbol}`)}
                  >
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-sm">{stock.symbol.replace('.NS', '').replace('.BO', '')}</p>
                        <ExternalLink className="h-3 w-3 text-gray-400" />
                      </div>
                      <p className="text-xs text-gray-500">{stock.symbol}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-sm">{formatINR(stock.price)}</p>
                      <p className="text-xs flex items-center justify-end text-green-600">
                        <ArrowUp className="h-3 w-3 mr-1" />
                        {stock.changePercent.toFixed(2)}%
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center text-lg">
              <TrendingDown className="h-5 w-5 text-red-600" />
              <span className="ml-2">Indian Top Losers</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {indianLosers.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No losers data available</p>
              ) : (
                indianLosers.map((stock) => (
                  <div
                    key={stock.symbol}
                    className="flex justify-between items-center py-2 border-b last:border-0  cursor-pointer rounded px-2 -mx-2 transition-colors"
                    onClick={() => navigate(`/stocks/${stock.symbol}`)}
                  >
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium text-sm">{stock.symbol.replace('.NS', '').replace('.BO', '')}</p>
                        <ExternalLink className="h-3 w-3 text-gray-400" />
                      </div>
                      <p className="text-xs text-gray-500">{stock.symbol}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-sm">{formatINR(stock.price)}</p>
                      <p className="text-xs flex items-center justify-end text-red-600">
                        <ArrowDown className="h-3 w-3 mr-1" />
                        {Math.abs(stock.changePercent).toFixed(2)}%
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Popular Indian Stocks Grid */}
      <Card>
        <CardHeader>
          <CardTitle>Popular Indian Stocks</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {indianStocksData?.slice(0, 9).map((item) => (
              <div
                key={item.symbol}
                className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => navigate(`/stocks/${item.symbol}`)}
              >
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-semibold text-sm">{item.symbol.replace('.NS', '').replace('.BO', '')}</h3>
                    <p className="text-xs text-gray-500">{item.symbol}</p>
                  </div>
                  {item.isUsingFallback && (
                    <span className="text-xs bg-orange-100 text-orange-700 px-1 py-0.5 rounded">
                      Fallback
                    </span>
                  )}
                </div>
                {item.data && (
                  <div className="space-y-1">
                    <p className="text-lg font-bold">{formatINR(item.data.price)}</p>
                    <div className={`flex items-center text-sm ${
                      item.data.change >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {item.data.change >= 0 ? (
                        <ArrowUp className="h-3 w-3 mr-1" />
                      ) : (
                        <ArrowDown className="h-3 w-3 mr-1" />
                      )}
                      <span>
                        {Math.abs(item.data.changePercent).toFixed(2)}%
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderIndices = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Market Indices</span>
          <div className="flex items-center gap-2">
            {marketIndicesLoading && (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
            )}
            {allSuccess ? (
              <div title="Live data">
                <Wifi className="h-4 w-4 text-green-500" />
              </div>
            ) : (
              <div title="Using fallback data">
                <WifiOff className="h-4 w-4 text-orange-500" />
              </div>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {marketIndices.map((index: any, i: number) => (
            <div key={i} className="flex justify-between items-center pb-2 border-b border-border last:border-0">
              <div>
                <h3 className="font-medium flex items-center gap-2">
                  {index.name}
                  {index.isLive && (
                    <div title="Live data">
                      <span className="inline-block w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                    </div>
                  )}
                </h3>
              </div>
              <div className="text-right">
                <p className="font-semibold">
                  {typeof index.value === 'number' ? index.value.toLocaleString(undefined, {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                  }) : index.value}
                </p>
                <p className={`flex items-center justify-end ${index.isPositive ? "text-market-up" : "text-market-down"}`}>
                  {index.isPositive ? (
                    <ArrowUp className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDown className="h-4 w-4 mr-1" />
                  )}
                  {Math.abs(index.change)}%
                </p>
              </div>
            </div>
          ))}
        </div>
        {marketIndicesError && !allSuccess && (
          <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-950 border border-orange-200 dark:border-orange-800 rounded-lg">
            <p className="text-sm text-orange-700 dark:text-orange-300">
              ⚠️ Some data may not be current. Using cached/fallback values.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );

  const renderStockList = (stocks: typeof topGainers, title: string, icon: React.ReactNode) => (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center text-lg">
          {icon}
          <span className="ml-2">{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {stocks.length === 0 ? (
            <p className="text-gray-500 text-center py-4">No data available</p>
          ) : (
            stocks.map((stock) => (
              <div
                key={stock.symbol}
                className="flex justify-between items-center py-2 border-b last:border-0  cursor-pointer rounded px-2 -mx-2 transition-colors"
                onClick={() => navigate(`/stocks/${stock.symbol}`)}
              >
                <div>
                  <div className="flex items-center space-x-2">
                    <p className="font-medium text-sm">{stock.name}</p>
                    <ExternalLink className="h-3 w-3 text-gray-400" />
                  </div>
                  <p className="text-xs text-gray-500">{stock.symbol}</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-sm">{formatPrice(stock.price, stock.symbol)}</p>
                  <p className={`text-xs flex items-center justify-end ${
                    stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stock.changePercent >= 0 ? (
                      <ArrowUp className="h-3 w-3 mr-1" />
                    ) : (
                      <ArrowDown className="h-3 w-3 mr-1" />
                    )}
                    {Math.abs(stock.changePercent).toFixed(2)}%
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Enhanced Search Bar */}
      <Card className="overflow-hidden">
        <CardContent className="p-6">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-muted-foreground transition-colors duration-200" />
            </div>
            <Input
              placeholder="Search stocks, indices, or companies..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 pr-4 h-12 text-base bg-background/50 border-2 border-border/50 rounded-xl
                         focus:border-primary/50 focus:bg-background transition-all duration-200
                         placeholder:text-muted-foreground/70 shadow-sm hover:shadow-md focus:shadow-lg"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute inset-y-0 right-0 pr-4 flex items-center text-muted-foreground
                           hover:text-foreground transition-colors duration-200"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>

          {/* Enhanced Search Results */}
          {searchResults && (searchResults as SymbolSearch)?.result && searchQuery.length > 2 && (
            <div className="mt-4 max-h-80 overflow-y-auto border border-border/50 rounded-xl bg-card/95 backdrop-blur-sm shadow-xl">
              <div className="p-2">
                <p className="text-xs font-medium text-muted-foreground px-3 py-2 uppercase tracking-wide">
                  Search Results
                </p>
                {(searchResults as SymbolSearch).result.slice(0, 8).map((result, index) => (
                  <div
                    key={index}
                    className="group relative p-3 mx-1 rounded-lg cursor-pointer transition-all duration-200
                               hover:bg-accent/80 hover:shadow-md active:scale-[0.98] border border-transparent
                               hover:border-border/30"
                    onClick={() => {
                      setSearchQuery('');
                      navigate(`/stocks/${result.symbol}`);
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center
                                          group-hover:bg-primary/20 transition-colors duration-200">
                            <span className="text-xs font-bold text-primary">
                              {result.displaySymbol.charAt(0)}
                            </span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="font-semibold text-sm text-foreground group-hover:text-primary
                                         transition-colors duration-200 truncate">
                              {result.displaySymbol}
                            </p>
                            <p className="text-xs text-muted-foreground truncate mt-0.5">
                              {result.description}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <svg className="h-4 w-4 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Loading State */}
          {searchQuery.length > 2 && !searchResults && (
            <div className="mt-4 p-4 text-center">
              <div className="inline-flex items-center gap-2 text-sm text-muted-foreground">
                <div className="w-4 h-4 border-2 border-primary/30 border-t-primary rounded-full animate-spin"></div>
                Searching...
              </div>
            </div>
          )}

          {/* No Results State */}
          {searchResults && (searchResults as SymbolSearch)?.result?.length === 0 && searchQuery.length > 2 && (
            <div className="mt-4 p-6 text-center border border-border/50 rounded-xl bg-muted/30">
              <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-muted flex items-center justify-center">
                <Search className="h-5 w-5 text-muted-foreground" />
              </div>
              <p className="text-sm font-medium text-foreground mb-1">No results found</p>
              <p className="text-xs text-muted-foreground">
                Try searching for a different stock symbol or company name
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tab Navigation */}
      <div className="flex space-x-1 p-1 rounded-lg overflow-x-auto">
        <Button
          variant={activeTab === 'indices' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('indices')}
          className="flex-shrink-0"
        >
          Market Indices
        </Button>
        <Button
          variant={activeTab === 'indian-stocks' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('indian-stocks')}
          className="flex-shrink-0"
        >
          🇮🇳 Indian Markets
        </Button>
        <Button
          variant={activeTab === 'gainers' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('gainers')}
          className="flex-shrink-0"
        >
          Top Gainers
        </Button>
        <Button
          variant={activeTab === 'losers' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('losers')}
          className="flex-shrink-0"
        >
          Top Losers
        </Button>
        <Button
          variant={activeTab === 'charts' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setActiveTab('charts')}
          className="flex-shrink-0"
        >
          <BarChart3 className="h-4 w-4 mr-1" />
          Charts
        </Button>
      </div>

      {/* Content based on active tab */}
      {activeTab === 'indices' && renderIndices()}

      {activeTab === 'indian-stocks' && (
        <div className="space-y-6">
          {renderIndianIndices()}
          {renderIndianStocks()}
        </div>
      )}

      {activeTab === 'gainers' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {renderStockList(topGainers, 'Top Gainers', <TrendingUp className="h-5 w-5 text-green-600" />)}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Market Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{topGainers.length}</p>
                  <p className="text-sm text-gray-500">Gainers</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">{topLosers.length}</p>
                  <p className="text-sm text-gray-500">Losers</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      
      {activeTab === 'losers' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {renderStockList(topLosers, 'Top Losers', <TrendingDown className="h-5 w-5 text-red-600" />)}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Market Sentiment</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm">Bullish Stocks</span>
                  <span className="text-sm font-medium text-green-600">{topGainers.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Bearish Stocks</span>
                  <span className="text-sm font-medium text-red-600">{topLosers.length}</span>
                </div>
                <div className="pt-2 border-t">
                  <p className="text-xs text-gray-500">
                    Market sentiment based on top performing stocks
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'charts' && (
        <div className="space-y-6">
          {/* Featured Stock Selector */}
          <Card>
            <CardHeader>
              <CardTitle>Featured Stock Chart</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-4">
                {['RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'AAPL', 'GOOGL', 'MSFT', 'TSLA'].map((stock) => (
                  <Button
                    key={stock}
                    variant={featuredStock === stock ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFeaturedStock(stock)}
                  >
                    {stock.replace('.NS', '')}
                  </Button>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Stock Chart */}
          <StockChart
            symbol={featuredStock}
            name={featuredStock.replace('.NS', '')}
            height={500}
            showControls={true}
          />

          {/* Additional Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <StockChart
              symbol="NIFTY50.NS"
              name="NIFTY 50"
              height={300}
              showControls={false}
            />
            <StockChart
              symbol="SPY"
              name="S&P 500"
              height={300}
              showControls={false}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketDashboard;
