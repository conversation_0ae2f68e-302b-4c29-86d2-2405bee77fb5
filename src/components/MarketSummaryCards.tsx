import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowUp, ArrowDown, TrendingUp, Activity, BarChart3, ExternalLink, Wifi, WifiOff } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { getStockQuote } from '@/integrations/finnhub/client';
import { useIndianMarketIndicesWithFallback } from '@/hooks/useIndianStockData';

// Fallback data for when API is unavailable
const fallbackMarketIndices = [
  { name: "NIFTY 50", value: 22196.95, change: 0.37, isPositive: true },
  { name: "BSE SENSEX", value: 73088.33, change: 0.43, isPositive: true },
  { name: "S&P 500", value: 5069.76, change: 0.41, isPositive: true },
  { name: "NASDAQ", value: 15927.90, change: 0.85, isPositive: true },
];

// Configuration for US indices
const usIndicesConfig = [
  { name: "S&P 500", symbol: "^GSPC", fallback: fallbackMarketIndices[2] },
  { name: "NASDAQ", symbol: "^IXIC", fallback: fallbackMarketIndices[3] },
];

// Custom hook for fetching key market data for summary
const useMarketSummaryData = () => {
  // Finnhub queries for US markets
  const finnhubQueries = usIndicesConfig.map(config =>
    useQuery({
      queryKey: ['marketSummary', config.symbol],
      queryFn: () => getStockQuote(config.symbol),
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
      retryDelay: 1000,
    })
  );

  // Yahoo Finance queries for Indian markets
  const { data: indianIndices, isUsingFallback: indianFallback } = useIndianMarketIndicesWithFallback();

  // Process Finnhub data for US indices
  const finnhubIndices = usIndicesConfig.map((config, index) => {
    const query = finnhubQueries[index];

    if (query.isSuccess && query.data && typeof query.data === 'object' && 'c' in query.data) {
      const data = query.data as { c: number; pc: number; d?: number; dp?: number };
      const currentPrice = data.c;
      const previousClose = data.pc;
      const change = previousClose ? ((currentPrice - previousClose) / previousClose) * 100 : 0;

      return {
        name: config.name,
        value: currentPrice,
        change: Number(change.toFixed(2)),
        isPositive: change >= 0,
        isLive: true,
        currency: 'USD',
      };
    }

    return {
      ...config.fallback,
      isLive: false,
      currency: 'USD',
    };
  });

  // Process Indian indices from Yahoo Finance (take first 2)
  const processedIndianIndices = indianIndices?.slice(0, 2).map(index => ({
    name: index.name,
    value: index.value,
    change: index.changePercent,
    isPositive: index.isPositive,
    isLive: !indianFallback,
    currency: 'INR',
  })) || [];

  // Combine key indices for summary
  const summaryIndices = [
    ...processedIndianIndices,
    ...finnhubIndices,
  ];

  const isLoading = finnhubQueries.some(q => q.isLoading);
  const hasError = finnhubQueries.some(q => q.isError);
  const allSuccess = finnhubQueries.every(q => q.isSuccess) && !indianFallback;

  return {
    summaryIndices,
    isLoading,
    hasError,
    allSuccess,
  };
};

const MarketSummaryCards: React.FC = () => {
  const navigate = useNavigate();
  const { summaryIndices, isLoading, hasError, allSuccess } = useMarketSummaryData();

  const handleViewDashboard = () => {
    navigate('/market-dashboard');
  };

  const formatValue = (value: number, currency: string) => {
    if (currency === 'INR') {
      return new Intl.NumberFormat('en-IN', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(value);
    } else {
      return new Intl.NumberFormat('en-US', {
        style: 'decimal',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(value);
    }
  };

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-4">Market Overview</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Get a quick snapshot of key market indices and access our comprehensive real-time dashboard.
        </p>
      </div>

      {/* Market Summary Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {summaryIndices.map((index, idx) => (
          <Card key={idx} className="relative overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {index.name}
                </CardTitle>
                <div className="flex items-center">
                  {index.isLive ? (
                    <Wifi className="h-3 w-3 text-green-500" />
                  ) : (
                    <WifiOff className="h-3 w-3 text-orange-500" />
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  {formatValue(index.value, index.currency)}
                </div>
                <div className={`flex items-center text-sm ${
                  index.isPositive ? 'text-green-600' : 'text-red-600'
                }`}>
                  {index.isPositive ? (
                    <ArrowUp className="h-4 w-4 mr-1" />
                  ) : (
                    <ArrowDown className="h-4 w-4 mr-1" />
                  )}
                  {Math.abs(index.change)}%
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Call-to-Action Card */}
      <Card className="from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20  dark:border-blue-800">
        <CardContent className="p-8">
          <div className="text-center space-y-4">
            <div className="flex justify-center">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">
                <BarChart3 className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-2">Explore Full Market Dashboard</h3>
              <p className="text-muted-foreground mb-4">
                Access comprehensive real-time data, interactive charts, stock search, and detailed market analysis.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button onClick={handleViewDashboard} size="lg" className="flex items-center">
                <Activity className="h-4 w-4 mr-2" />
                View Live Dashboard
                <ExternalLink className="h-4 w-4 ml-2" />
              </Button>
              <Button variant="outline" size="lg" className="flex items-center">
                <TrendingUp className="h-4 w-4 mr-2" />
                Market Analysis
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Status Indicator */}
      <div className="text-center">
        <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs ${
          allSuccess 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
            : hasError 
            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
            : 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400'
        }`}>
          {allSuccess ? (
            <>
              <Wifi className="h-3 w-3 mr-1" />
              Live Market Data
            </>
          ) : hasError ? (
            <>
              <WifiOff className="h-3 w-3 mr-1" />
              Using Cached Data
            </>
          ) : (
            <>
              <Activity className="h-3 w-3 mr-1" />
              Loading Market Data
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketSummaryCards;
