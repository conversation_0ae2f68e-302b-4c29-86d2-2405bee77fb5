import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Play, CheckCircle, XCircle, TrendingUp, TrendingDown } from 'lucide-react';
import { 
  useIndianStockQuoteWithFallback, 
  useIndianMarketIndicesWithFallback,
  useBatchIndianStockQuotesWithFallback,
  useIndianMarketStatus 
} from '@/hooks/useIndianStockData';
import { formatINR } from '@/integrations/yahoo-finance/types';
import { runAllTests, quickTest } from '@/utils/testYahooFinance';

const YahooFinanceTest: React.FC = () => {
  const [testResults, setTestResults] = useState<any>(null);
  const [isRunningTests, setIsRunningTests] = useState(false);

  // Test hooks
  const { data: relianceQuote, isLoading: relianceLoading, isUsingFallback: relianceFallback } = 
    useIndianStockQuoteWithFallback('RELIANCE.NS');
  
  const { data: marketIndices, isLoading: indicesLoading, isUsingFallback: indicesFallback } = 
    useIndianMarketIndicesWithFallback();
  
  const { data: batchQuotes, isLoading: batchLoading } = 
    useBatchIndianStockQuotesWithFallback(['TCS.NS', 'HDFCBANK.NS', 'INFY.NS']);
  
  const { data: marketStatus } = useIndianMarketStatus();

  const runTests = async () => {
    setIsRunningTests(true);
    try {
      const results = await runAllTests();
      setTestResults(results);
    } catch (error) {
      console.error('Test failed:', error);
      setTestResults({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setIsRunningTests(false);
    }
  };

  const runQuickTest = async () => {
    setIsRunningTests(true);
    try {
      const success = await quickTest();
      setTestResults({ quickTest: success });
    } catch (error) {
      console.error('Quick test failed:', error);
      setTestResults({ quickTest: false, error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setIsRunningTests(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Yahoo Finance API Integration Test</h1>
        <p className="text-muted-foreground">Testing Indian stock market data integration</p>
      </div>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button onClick={runQuickTest} disabled={isRunningTests}>
              {isRunningTests ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              Quick Test
            </Button>
            <Button onClick={runTests} disabled={isRunningTests} variant="outline">
              {isRunningTests ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              Full Test Suite
            </Button>
          </div>
          
          {testResults && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Test Results:</h3>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Market Status */}
      <Card>
        <CardHeader>
          <CardTitle>Market Status</CardTitle>
        </CardHeader>
        <CardContent>
          {marketStatus ? (
            <div className="flex items-center gap-4">
              <Badge variant={marketStatus.isOpen ? 'default' : 'secondary'}>
                {marketStatus.isOpen ? 'MARKET OPEN' : 'MARKET CLOSED'}
              </Badge>
              <span className="text-sm text-muted-foreground">
                Last updated: {new Date(marketStatus.timestamp).toLocaleTimeString()}
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading market status...</span>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Single Stock Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Single Stock Quote Test
            {relianceFallback ? (
              <Badge variant="outline" className="text-orange-600">Using Fallback</Badge>
            ) : (
              <Badge variant="outline" className="text-green-600">Live Data</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {relianceLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading RELIANCE.NS...</span>
            </div>
          ) : relianceQuote ? (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">{relianceQuote.symbol}</h3>
                <Badge variant={relianceQuote.marketState === 'REGULAR' ? 'default' : 'secondary'}>
                  {relianceQuote.marketState}
                </Badge>
              </div>
              <div className="text-2xl font-bold">{formatINR(relianceQuote.price)}</div>
              <div className={`flex items-center gap-1 ${
                relianceQuote.change >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {relianceQuote.change >= 0 ? (
                  <TrendingUp className="h-4 w-4" />
                ) : (
                  <TrendingDown className="h-4 w-4" />
                )}
                <span>
                  {formatINR(Math.abs(relianceQuote.change))} ({Math.abs(relianceQuote.changePercent).toFixed(2)}%)
                </span>
              </div>
              <div className="text-sm text-muted-foreground">
                Exchange: {relianceQuote.exchange} | Volume: {relianceQuote.volume.toLocaleString()}
              </div>
            </div>
          ) : (
            <div className="text-red-600">Failed to load stock data</div>
          )}
        </CardContent>
      </Card>

      {/* Market Indices Test */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Market Indices Test
            {indicesFallback ? (
              <Badge variant="outline" className="text-orange-600">Using Fallback</Badge>
            ) : (
              <Badge variant="outline" className="text-green-600">Live Data</Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {indicesLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading market indices...</span>
            </div>
          ) : marketIndices && marketIndices.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {marketIndices.map((index) => (
                <div key={index.symbol} className="p-4 border rounded-lg">
                  <h3 className="font-semibold">{index.name}</h3>
                  <div className="text-xl font-bold">{index.value.toLocaleString()}</div>
                  <div className={`flex items-center gap-1 text-sm ${
                    index.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {index.isPositive ? (
                      <TrendingUp className="h-3 w-3" />
                    ) : (
                      <TrendingDown className="h-3 w-3" />
                    )}
                    <span>
                      {Math.abs(index.change).toFixed(2)} ({Math.abs(index.changePercent).toFixed(2)}%)
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-red-600">Failed to load market indices</div>
          )}
        </CardContent>
      </Card>

      {/* Batch Quotes Test */}
      <Card>
        <CardHeader>
          <CardTitle>Batch Quotes Test</CardTitle>
        </CardHeader>
        <CardContent>
          {batchLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Loading batch quotes...</span>
            </div>
          ) : batchQuotes && batchQuotes.length > 0 ? (
            <div className="space-y-3">
              {batchQuotes.map((item) => (
                <div key={item.symbol} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <span className="font-medium">{item.symbol}</span>
                    {item.isUsingFallback ? (
                      <Badge variant="outline" className="text-orange-600">Fallback</Badge>
                    ) : (
                      <Badge variant="outline" className="text-green-600">Live</Badge>
                    )}
                  </div>
                  {item.data ? (
                    <div className="text-right">
                      <div className="font-semibold">{formatINR(item.data.price)}</div>
                      <div className={`text-sm ${
                        item.data.change >= 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {item.data.changePercent.toFixed(2)}%
                      </div>
                    </div>
                  ) : (
                    <div className="text-red-600 text-sm">Failed</div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-red-600">Failed to load batch quotes</div>
          )}
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm text-muted-foreground">
            1. Click "Quick Test" to test a single API call
          </p>
          <p className="text-sm text-muted-foreground">
            2. Click "Full Test Suite" to run comprehensive tests
          </p>
          <p className="text-sm text-muted-foreground">
            3. Check browser console for detailed logs
          </p>
          <p className="text-sm text-muted-foreground">
            4. Orange badges indicate fallback data is being used
          </p>
          <p className="text-sm text-muted-foreground">
            5. Green badges indicate live data from Yahoo Finance API
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default YahooFinanceTest;
