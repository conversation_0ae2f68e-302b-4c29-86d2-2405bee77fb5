
import React from 'react';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { usePublishedContent } from '@/hooks/useContent';

// Dynamic data will be fetched from Supabase

const EducationCard: React.FC<{ resource: any }> = ({ resource }) => {
  const getLevelColor = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'beginner': return 'bg-green-500/20 text-green-500';
      case 'intermediate': return 'bg-blue-500/20 text-blue-500';
      case 'advanced': return 'bg-purple-500/20 text-purple-500';
      default: return 'bg-gray-500/20 text-gray-500';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'article': return '📄';
      case 'video': return '🎬';
      case 'guide': return '📚';
      default: return '📄';
    }
  };

  const getDuration = (resource: any) => {
    if (resource.reading_time) {
      return `${resource.reading_time} min read`;
    }
    if (resource.video_duration) {
      return `${resource.video_duration} min video`;
    }
    return resource.duration || '5 min read';
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Badge variant="outline" className={getLevelColor(resource.difficulty_level)}>
            {resource.difficulty_level?.charAt(0).toUpperCase() + resource.difficulty_level?.slice(1) || 'Beginner'}
          </Badge>
          <span className="text-xs text-muted-foreground">{getDuration(resource)}</span>
        </div>
        <CardTitle className="text-xl mt-2 flex items-center gap-2">
          {getTypeIcon(resource.content_type)} {resource.title}
        </CardTitle>
        <CardDescription>
          {resource.excerpt || resource.content?.substring(0, 150) + '...'}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        {resource.tags && (
          <div className="flex flex-wrap gap-2">
            {resource.tags.slice(0, 3).map((tag: string, index: number) => (
              <Badge key={index} variant="secondary" className="text-xs">{tag}</Badge>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-2">
        <Link to={`/education/${resource.id}`} className="text-primary text-sm hover:underline">
          Start Learning →
        </Link>
      </CardFooter>
    </Card>
  );
};

const EducationSection: React.FC = () => {
  const { data: allEducation, isLoading, error } = usePublishedContent('education', { limit: 6 });

  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(6)].map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-3 w-20" />
            </div>
            <Skeleton className="h-6 w-full mt-2" />
            <Skeleton className="h-4 w-full mt-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-20" />
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-4 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  if (error) {
    return (
      <div className="container py-12 bg-secondary/30 rounded-lg">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Educational Resources</h2>
          <p className="text-muted-foreground">Unable to load educational resources at the moment. Please try again later.</p>
        </div>
      </div>
    );
  }

  const resources = allEducation || [];

  return (
    <div className="container py-12 bg-secondary/30 rounded-lg">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold">Educational Resources</h2>
        <p className="text-muted-foreground mt-2">Enhance your financial literacy with our curated educational content</p>
      </div>

      {isLoading ? (
        <LoadingSkeleton />
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {resources.map((resource) => (
            <EducationCard key={resource.id} resource={resource} />
          ))}
        </div>
      )}

      <div className="mt-10 text-center">
        <Link to="/education" className="text-primary hover:underline">
          View All Educational Resources →
        </Link>
      </div>
    </div>
  );
};

export default EducationSection;
