import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
  BarChart,
  Bar
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  transformYahooFinanceData, 
  createSampleChartData,
  validateYahooFinanceResponse,
  type TransformedChartData,
  type YahooFinanceResponse 
} from '@/utils/yahooFinanceDataTransformer';

const YahooFinanceChartTest: React.FC = () => {
  const [chartData, setChartData] = useState<TransformedChartData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [chartType, setChartType] = useState<'line' | 'area' | 'candlestick'>('line');

  // Load test data from response.txt
  const loadTestData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real implementation, this would be an API call
      // For now, we'll simulate loading the response.txt data
      const response = await fetch('/src/response.txt');
      const responseText = await response.text();
      const yahooData: YahooFinanceResponse = JSON.parse(responseText);
      
      if (!validateYahooFinanceResponse(yahooData)) {
        throw new Error('Invalid Yahoo Finance response format');
      }
      
      const transformed = transformYahooFinanceData(yahooData);
      if (!transformed) {
        throw new Error('Failed to transform Yahoo Finance data');
      }
      
      setChartData(transformed);
    } catch (err) {
      console.error('Error loading test data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
      
      // Fallback to sample data
      setChartData(createSampleChartData());
    } finally {
      setLoading(false);
    }
  };

  // Load sample data
  const loadSampleData = () => {
    setLoading(true);
    setError(null);
    
    setTimeout(() => {
      setChartData(createSampleChartData());
      setLoading(false);
    }, 500);
  };

  useEffect(() => {
    loadSampleData(); // Load sample data by default
  }, []);

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.time}</p>
          <p className="text-sm text-muted-foreground">{data.date}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">Open: <span className="font-medium">₹{data.open}</span></p>
            <p className="text-sm">High: <span className="font-medium text-green-600">₹{data.high}</span></p>
            <p className="text-sm">Low: <span className="font-medium text-red-600">₹{data.low}</span></p>
            <p className="text-sm">Close: <span className="font-medium">₹{data.close}</span></p>
            <p className="text-sm">Volume: <span className="font-medium">{data.volume.toLocaleString()}</span></p>
            <p className="text-sm">
              Change: <span className={`font-medium ${data.change >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {data.change >= 0 ? '+' : ''}₹{data.change} ({data.changePercent}%)
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    if (!chartData?.data.length) return null;

    const data = chartData.data;

    switch (chartType) {
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 12 }}
                interval="preserveStartEnd"
              />
              <YAxis 
                domain={['dataMin - 5', 'dataMax + 5']}
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Area 
                type="monotone" 
                dataKey="close" 
                stroke="#3b82f6" 
                fill="#3b82f6" 
                fillOpacity={0.1}
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'candlestick':
        // Simplified candlestick using bars (for demo purposes)
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 12 }}
                interval="preserveStartEnd"
              />
              <YAxis 
                domain={['dataMin - 5', 'dataMax + 5']}
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar dataKey="high" fill="#10b981" />
              <Bar dataKey="low" fill="#ef4444" />
            </BarChart>
          </ResponsiveContainer>
        );

      default: // line chart
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="time" 
                tick={{ fontSize: 12 }}
                interval="preserveStartEnd"
              />
              <YAxis 
                domain={['dataMin - 5', 'dataMax + 5']}
                tick={{ fontSize: 12 }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line 
                type="monotone" 
                dataKey="close" 
                stroke="#3b82f6" 
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 4 }}
              />
            </LineChart>
          </ResponsiveContainer>
        );
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Yahoo Finance Chart Test</h1>
        <p className="text-muted-foreground">
          Testing Yahoo Finance API data transformation and chart rendering
        </p>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Chart Controls</CardTitle>
          <CardDescription>Load data and customize chart display</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={loadTestData} disabled={loading}>
              {loading ? 'Loading...' : 'Load Test Data (response.txt)'}
            </Button>
            <Button onClick={loadSampleData} variant="outline" disabled={loading}>
              Load Sample Data
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant={chartType === 'line' ? 'default' : 'outline'}
              onClick={() => setChartType('line')}
            >
              Line Chart
            </Button>
            <Button 
              variant={chartType === 'area' ? 'default' : 'outline'}
              onClick={() => setChartType('area')}
            >
              Area Chart
            </Button>
            <Button 
              variant={chartType === 'candlestick' ? 'default' : 'outline'}
              onClick={() => setChartType('candlestick')}
            >
              Candlestick
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200">
          <CardContent className="pt-6">
            <div className="text-red-600">
              <strong>Error:</strong> {error}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chart Display */}
      {chartData && (
        <>
          {/* Stock Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {chartData.meta.companyName}
                <Badge variant="secondary">{chartData.meta.symbol}</Badge>
              </CardTitle>
              <CardDescription>
                Current Price: ₹{chartData.meta.currentPrice} | 
                Volume: {chartData.meta.volume.toLocaleString()} | 
                Data Points: {chartData.summary.totalPoints}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Day High</p>
                  <p className="font-medium text-green-600">₹{chartData.meta.dayHigh}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Day Low</p>
                  <p className="font-medium text-red-600">₹{chartData.meta.dayLow}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">52W High</p>
                  <p className="font-medium">₹{chartData.meta.fiftyTwoWeekHigh}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">52W Low</p>
                  <p className="font-medium">₹{chartData.meta.fiftyTwoWeekLow}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Price Chart ({chartType})</CardTitle>
              <CardDescription>{chartData.summary.timeRange}</CardDescription>
            </CardHeader>
            <CardContent>
              {renderChart()}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default YahooFinanceChartTest;
