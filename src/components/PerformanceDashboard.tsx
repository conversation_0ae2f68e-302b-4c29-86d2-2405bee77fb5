/**
 * Performance Dashboard - Shows API performance improvements
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, TrendingUp, TrendingDown, Clock, CheckCircle, XCircle } from 'lucide-react';
import { performanceMonitor } from '@/utils/performanceMonitor';

const PerformanceDashboard: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const refreshStats = () => {
    setIsRefreshing(true);
    const newStats = performanceMonitor.getStats();
    setStats(newStats);
    setTimeout(() => setIsRefreshing(false), 500);
  };

  useEffect(() => {
    refreshStats();
    
    // Auto-refresh every 10 seconds
    const interval = setInterval(refreshStats, 10000);
    return () => clearInterval(interval);
  }, []);

  if (!stats) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading performance data...</div>
        </CardContent>
      </Card>
    );
  }

  const getSpeedBadge = (duration: number) => {
    if (duration < 1000) return <Badge className="bg-green-500">⚡ Fast</Badge>;
    if (duration < 3000) return <Badge className="bg-yellow-500">🟡 Good</Badge>;
    if (duration < 5000) return <Badge className="bg-orange-500">🟠 Slow</Badge>;
    return <Badge variant="destructive">🔴 Very Slow</Badge>;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="flex items-center gap-2">
              📊 API Performance Dashboard
              <Badge variant="outline">Live</Badge>
            </CardTitle>
            <Button
              onClick={refreshStats}
              disabled={isRefreshing}
              size="sm"
              variant="outline"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Success Rate */}
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                {stats.successRate > 90 ? (
                  <CheckCircle className="h-8 w-8 text-green-500" />
                ) : (
                  <XCircle className="h-8 w-8 text-red-500" />
                )}
              </div>
              <div className="text-2xl font-bold">{stats.successRate.toFixed(1)}%</div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>

            {/* Average Duration */}
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <Clock className="h-8 w-8 text-blue-500" />
              </div>
              <div className="text-2xl font-bold">{stats.averageDuration}ms</div>
              <div className="text-sm text-gray-600">Avg Response</div>
            </div>

            {/* Fast Operations */}
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
              <div className="text-2xl font-bold">{stats.fastOperations}</div>
              <div className="text-sm text-gray-600">Fast (&lt;2s)</div>
            </div>

            {/* Slow Operations */}
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center mb-2">
                <TrendingDown className="h-8 w-8 text-orange-500" />
              </div>
              <div className="text-2xl font-bold">{stats.slowOperations}</div>
              <div className="text-sm text-gray-600">Slow (&gt;5s)</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Operations */}
      <Card>
        <CardHeader>
          <CardTitle>Recent API Calls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {stats.recentMetrics.length === 0 ? (
              <div className="text-center text-gray-500 py-4">
                No recent API calls. Try navigating to the dashboard or stock pages.
              </div>
            ) : (
              stats.recentMetrics.map((metric: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {metric.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <div>
                      <div className="font-medium">{metric.operation}</div>
                      <div className="text-sm text-gray-600">
                        {new Date(metric.timestamp).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getSpeedBadge(metric.duration)}
                    <span className="text-sm font-mono">{metric.duration}ms</span>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Performance Tips */}
      <Card>
        <CardHeader>
          <CardTitle>🚀 Performance Optimizations Applied</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium text-green-600">✅ Optimizations Active</h4>
              <ul className="text-sm space-y-1">
                <li>• Reduced rate limiting (300ms vs 2000ms)</li>
                <li>• Chunked batch processing (3 at a time)</li>
                <li>• Optimized API parameters</li>
                <li>• Faster timeout settings (10s vs 30s)</li>
                <li>• Performance monitoring enabled</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium text-blue-600">📈 Expected Improvements</h4>
              <ul className="text-sm space-y-1">
                <li>• Single quotes: 3-8s → 1-3s</li>
                <li>• Batch requests: 15-40s → 5-12s</li>
                <li>• Market indices: 8-20s → 3-8s</li>
                <li>• Better error handling</li>
                <li>• Real-time performance tracking</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PerformanceDashboard;
