import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Calendar,
  Eye,
  EyeOff,
  Globe,
  MapPin
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import { useAdminQuarterlyOutlook, useDeleteQuarterlyOutlook } from '@/hooks/useContent';
import { Database } from '@/integrations/supabase/types';

type QuarterlyOutlookData = Database['public']['Tables']['quarterly_outlook']['Row'];

export const AdminQuarterlyOutlookList: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'draft' | 'published'>('all');
  const [marketFilter, setMarketFilter] = useState<'all' | 'us' | 'india'>('all');
  
  const { data: outlookData, isLoading, error } = useAdminQuarterlyOutlook();
  const deleteOutlookMutation = useDeleteQuarterlyOutlook();

  // Ensure we have an array to work with
  const outlookArray = Array.isArray(outlookData) ? outlookData : [];

  // Debug logging
  console.log('AdminQuarterlyOutlookList - Raw data:', outlookData);
  console.log('AdminQuarterlyOutlookList - Array data:', outlookArray);
  console.log('AdminQuarterlyOutlookList - Loading:', isLoading);
  console.log('AdminQuarterlyOutlookList - Error:', error);

  const handleDelete = async (id: string, title: string) => {
    try {
      await deleteOutlookMutation.mutateAsync(id);
      toast({
        title: "Quarterly outlook deleted",
        description: `"${title}" has been deleted successfully.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete quarterly outlook. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Filter data based on search term, status, and market
  const filteredData = outlookArray.filter((item: QuarterlyOutlookData) => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.summary.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || item.status === statusFilter;
    const matchesMarket = marketFilter === 'all' || item.market === marketFilter;

    return matchesSearch && matchesStatus && matchesMarket;
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Loading quarterly outlook data...</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-20 bg-muted animate-pulse rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error Loading Data</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            Failed to load quarterly outlook data. Please try again later.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Quarterly Outlook Management</CardTitle>
            <p className="text-sm text-muted-foreground mt-1">
              Manage quarterly market outlook and forecasts
            </p>
          </div>
          <Button asChild>
            <Link to="/admin/quarterly-outlook/new">
              <Plus className="h-4 w-4 mr-2" />
              New Quarterly Outlook
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search quarterly outlook..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={(value: 'all' | 'draft' | 'published') => setStatusFilter(value)}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="published">Published</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
            </SelectContent>
          </Select>
          <Select value={marketFilter} onValueChange={(value: 'all' | 'us' | 'india') => setMarketFilter(value)}>
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filter by market" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Markets</SelectItem>
              <SelectItem value="us">US Market</SelectItem>
              <SelectItem value="india">Indian Market</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Content List */}
        <div className="space-y-4">
          {filteredData.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No quarterly outlook found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== 'all' || marketFilter !== 'all'
                  ? "No quarterly outlook matches your current filters."
                  : "Get started by creating your first quarterly outlook."}
              </p>
              <Button asChild>
                <Link to="/admin/quarterly-outlook/new">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Quarterly Outlook
                </Link>
              </Button>
            </div>
          ) : (
            filteredData.map((item: QuarterlyOutlookData) => (
              <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    {item.market === 'us' ? (
                      <Globe className="h-5 w-5 text-blue-500" />
                    ) : (
                      <MapPin className="h-5 w-5 text-orange-500" />
                    )}
                    <h3 className="font-semibold">{item.title}</h3>
                    <Badge variant={item.status === 'published' ? 'default' : 'secondary'}>
                      {item.status === 'published' ? (
                        <Eye className="h-3 w-3 mr-1" />
                      ) : (
                        <EyeOff className="h-3 w-3 mr-1" />
                      )}
                      {item.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                    {item.summary}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span>{item.quarter} {item.year}</span>
                    <span>{item.market.toUpperCase()} Market</span>
                    <span>
                      {item.status === 'published' && item.published_at
                        ? `Published ${new Date(item.published_at).toLocaleDateString()}`
                        : `Created ${new Date(item.created_at).toLocaleDateString()}`}
                    </span>
                  </div>
                </div>
                <div className="flex items-center gap-2 ml-4">
                  <Button variant="outline" size="sm" asChild>
                    <Link to={`/admin/quarterly-outlook/edit/${item.id}`}>
                      <Edit className="h-4 w-4" />
                    </Link>
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Quarterly Outlook</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete "{item.title}"? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(item.id, item.title)}
                          className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Summary */}
        {filteredData.length > 0 && (
          <div className="flex items-center justify-between pt-4 border-t text-sm text-muted-foreground">
            <span>
              Showing {filteredData.length} of {outlookArray.length} quarterly outlook{outlookArray.length !== 1 ? 's' : ''}
            </span>
            <div className="flex gap-4">
              <span>
                Published: {outlookArray.filter((item: QuarterlyOutlookData) => item.status === 'published').length}
              </span>
              <span>
                Drafts: {outlookArray.filter((item: QuarterlyOutlookData) => item.status === 'draft').length}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
