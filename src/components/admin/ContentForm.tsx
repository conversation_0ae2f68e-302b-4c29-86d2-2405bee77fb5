import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useCreateContent, useUpdateContent, useAdminContentItem } from '@/hooks/useContent';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Save, Eye, Plus, X } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { SimpleEditor } from '@/components/ui/simple-editor';

type ContentType = 'insights' | 'news' | 'education' | 'case_studies';

interface ContentFormProps {
  contentType: ContentType;
  mode: 'create' | 'edit';
}

interface FormData {
  title: string;
  content: string;
  excerpt: string;
  category: string;
  tags: string[];
  featured_image: string;
  status: 'draft' | 'published';
  // Education specific
  difficulty_level?: 'beginner' | 'intermediate' | 'advanced';
  estimated_read_time?: number;
  // Case studies specific
  investment_amount?: number;
  return_percentage?: number;
  time_period?: string;
}

export const ContentForm: React.FC<ContentFormProps> = ({ contentType, mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [tagInput, setTagInput] = useState('');
  const [useRichEditor, setUseRichEditor] = useState(true);
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    content: '',
    excerpt: '',
    category: '',
    tags: [],
    featured_image: '',
    status: 'draft',
    ...(contentType === 'education' && {
      difficulty_level: 'beginner',
      estimated_read_time: 5,
    }),
    ...(contentType === 'case_studies' && {
      investment_amount: 0,
      return_percentage: 0,
      time_period: '',
    }),
  });

  // Hooks for data operations
  const { data: existingContent, isLoading: loadingContent } = useAdminContentItem(
    contentType,
    id || ''
  );
  
  const createMutation = useCreateContent(contentType);
  const updateMutation = useUpdateContent(contentType);

  // Load existing content for edit mode
  useEffect(() => {
    if (mode === 'edit' && existingContent) {
      setFormData({
        title: existingContent.title,
        content: existingContent.content,
        excerpt: existingContent.excerpt || '',
        category: existingContent.category || '',
        tags: existingContent.tags || [],
        featured_image: existingContent.featured_image || '',
        status: existingContent.status,
        ...(contentType === 'education' && {
          difficulty_level: existingContent.difficulty_level || 'beginner',
          estimated_read_time: existingContent.estimated_read_time || 5,
        }),
        ...(contentType === 'case_studies' && {
          investment_amount: existingContent.investment_amount || 0,
          return_percentage: existingContent.return_percentage || 0,
          time_period: existingContent.time_period || '',
        }),
      });
    }
  }, [existingContent, mode, contentType]);

  const handleSubmit = async (e: React.FormEvent, publishNow = false) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.content.trim()) {
      toast({
        title: "Validation Error",
        description: "Title and content are required.",
        variant: "destructive",
      });
      return;
    }

    const submitData = {
      ...formData,
      status: publishNow ? 'published' as const : formData.status,
      published_at: publishNow ? new Date().toISOString() : undefined,
    };

    try {
      if (mode === 'create') {
        await createMutation.mutateAsync(submitData);
        toast({
          title: "Content created",
          description: `${getContentTypeLabel()} has been ${publishNow ? 'published' : 'saved as draft'}.`,
        });
      } else {
        await updateMutation.mutateAsync({ id: id!, ...submitData });
        toast({
          title: "Content updated",
          description: `${getContentTypeLabel()} has been ${publishNow ? 'published' : 'updated'}.`,
        });
      }
      navigate(`/admin`);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save content. Please try again.",
        variant: "destructive",
      });
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const getContentTypeLabel = () => {
    switch (contentType) {
      case 'insights': return 'Insight';
      case 'news': return 'News Article';
      case 'education': return 'Educational Content';
      case 'case_studies': return 'Case Study';
      default: return 'Content';
    }
  };



  if (mode === 'edit' && loadingContent) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="ghost" onClick={() => navigate('/admin')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Admin
        </Button>
        <div>
          <h1 className="text-2xl font-bold">
            {mode === 'create' ? 'Create' : 'Edit'} {getContentTypeLabel()}
          </h1>
          <p className="text-muted-foreground">
            {mode === 'create' ? 'Create new' : 'Edit existing'} {contentType} content
          </p>
        </div>
      </div>

      <form onSubmit={(e) => handleSubmit(e, false)} className="space-y-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-4">
            <Card>
              <CardHeader className="pb-4">
                <CardTitle>Content Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-0">
                <div>
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Enter title..."
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="excerpt">Excerpt</Label>
                  <Textarea
                    id="excerpt"
                    value={formData.excerpt}
                    onChange={(e) => setFormData(prev => ({ ...prev, excerpt: e.target.value }))}
                    placeholder="Brief description..."
                    rows={3}
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <Label htmlFor="content">Content *</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setUseRichEditor(!useRichEditor)}
                    >
                      {useRichEditor ? 'Simple Editor' : 'Rich Editor'}
                    </Button>
                  </div>
                  <div className="mt-2">
                    {useRichEditor ? (
                      <RichTextEditor
                        value={formData.content}
                        onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                        placeholder="Write your content here..."
                      />
                    ) : (
                      <SimpleEditor
                        value={formData.content}
                        onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                        placeholder="Write your content here..."
                      />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Content Type Specific Fields */}
            {contentType === 'education' && (
              <Card>
                <CardHeader>
                  <CardTitle>Education Settings</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="difficulty">Difficulty Level</Label>
                    <Select
                      value={formData.difficulty_level}
                      onValueChange={(value: 'beginner' | 'intermediate' | 'advanced') =>
                        setFormData(prev => ({ ...prev, difficulty_level: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="beginner">Beginner</SelectItem>
                        <SelectItem value="intermediate">Intermediate</SelectItem>
                        <SelectItem value="advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="readTime">Estimated Read Time (minutes)</Label>
                    <Input
                      id="readTime"
                      type="number"
                      value={formData.estimated_read_time || ''}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        estimated_read_time: parseInt(e.target.value) || 0 
                      }))}
                      min="1"
                    />
                  </div>
                </CardContent>
              </Card>
            )}

            {contentType === 'case_studies' && (
              <Card>
                <CardHeader>
                  <CardTitle>Investment Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="investment">Investment Amount ($)</Label>
                    <Input
                      id="investment"
                      type="number"
                      value={formData.investment_amount || ''}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        investment_amount: parseFloat(e.target.value) || 0 
                      }))}
                      min="0"
                      step="0.01"
                    />
                  </div>

                  <div>
                    <Label htmlFor="return">Return Percentage (%)</Label>
                    <Input
                      id="return"
                      type="number"
                      value={formData.return_percentage || ''}
                      onChange={(e) => setFormData(prev => ({ 
                        ...prev, 
                        return_percentage: parseFloat(e.target.value) || 0 
                      }))}
                      step="0.1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="period">Time Period</Label>
                    <Input
                      id="period"
                      value={formData.time_period || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, time_period: e.target.value }))}
                      placeholder="e.g., 6 months, 2 years"
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Publish Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: 'draft' | 'published') =>
                      setFormData(prev => ({ ...prev, status: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Separator />

                <div className="flex gap-2">
                  <Button type="submit" variant="outline" className="flex-1">
                    <Save className="h-4 w-4 mr-2" />
                    Save Draft
                  </Button>
                  <Button 
                    type="button" 
                    onClick={(e) => handleSubmit(e, true)}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Publish
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Organization</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Input
                    id="category"
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    placeholder="Enter category..."
                  />
                </div>

                <div>
                  <Label htmlFor="featured_image">Featured Image URL</Label>
                  <Input
                    id="featured_image"
                    value={formData.featured_image}
                    onChange={(e) => setFormData(prev => ({ ...prev, featured_image: e.target.value }))}
                    placeholder="https://..."
                  />
                </div>

                <div>
                  <Label>Tags</Label>
                  <div className="flex gap-2 mb-2">
                    <Input
                      value={tagInput}
                      onChange={(e) => setTagInput(e.target.value)}
                      placeholder="Add tag..."
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    />
                    <Button type="button" onClick={addTag} size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <X 
                          className="h-3 w-3 cursor-pointer" 
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
};
