import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useCreatePage, useUpdatePage, useAdminPage } from '@/hooks/useContent';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Save, Eye } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { RichTextEditor } from '@/components/ui/rich-text-editor';
import { SimpleEditor } from '@/components/ui/simple-editor';
import { Link } from 'react-router-dom';

interface PageFormProps {
  mode: 'create' | 'edit';
}

interface FormData {
  title: string;
  slug: string;
  content: string;
  meta_description: string;
  status: 'draft' | 'published';
}

export const PageForm: React.FC<PageFormProps> = ({ mode }) => {
  const navigate = useNavigate();
  const { id } = useParams();
  const [useRichEditor, setUseRichEditor] = useState(true);
  
  const [formData, setFormData] = useState<FormData>({
    title: '',
    slug: '',
    content: '',
    meta_description: '',
    status: 'draft',
  });

  // Hooks for data operations
  const { data: existingPage, isLoading: loadingPage } = useAdminPage(id || '');
  const createMutation = useCreatePage();
  const updateMutation = useUpdatePage();

  // Load existing page for edit mode
  useEffect(() => {
    if (mode === 'edit' && existingPage) {
      setFormData({
        title: existingPage.title,
        slug: existingPage.slug,
        content: existingPage.content,
        meta_description: existingPage.meta_description || '',
        status: existingPage.status,
      });
    }
  }, [existingPage, mode]);

  // Auto-generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleTitleChange = (title: string) => {
    setFormData(prev => ({
      ...prev,
      title,
      // Only auto-generate slug if it's empty or in create mode
      slug: (mode === 'create' || !prev.slug) ? generateSlug(title) : prev.slug
    }));
  };

  const handleSubmit = async (e: React.FormEvent, publishNow = false) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.content.trim() || !formData.slug.trim()) {
      toast({
        title: "Validation Error",
        description: "Title, slug, and content are required.",
        variant: "destructive",
      });
      return;
    }

    const submitData = {
      ...formData,
      status: publishNow ? 'published' as const : formData.status,
      published_at: publishNow ? new Date().toISOString() : undefined,
    };

    try {
      if (mode === 'create') {
        await createMutation.mutateAsync(submitData);
        toast({
          title: "Page created",
          description: `Page has been ${publishNow ? 'published' : 'saved as draft'}.`,
        });
      } else {
        await updateMutation.mutateAsync({ id: id!, ...submitData });
        toast({
          title: "Page updated",
          description: `Page has been ${publishNow ? 'published' : 'updated'}.`,
        });
      }
      navigate('/admin?tab=pages');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save page. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (mode === 'edit' && loadingPage) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading page...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link to="/admin?tab=pages">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Pages
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">
          {mode === 'create' ? 'Create New Page' : 'Edit Page'}
        </h1>
        <p className="text-muted-foreground mt-2">
          {mode === 'create' 
            ? 'Create a new static page for your website.'
            : 'Edit the page content and settings.'
          }
        </p>
      </div>

      <form onSubmit={(e) => handleSubmit(e)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Page Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleTitleChange(e.target.value)}
                placeholder="Enter page title..."
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">URL Slug *</Label>
              <Input
                id="slug"
                value={formData.slug}
                onChange={(e) => setFormData(prev => ({ ...prev, slug: e.target.value }))}
                placeholder="page-url-slug"
                required
              />
              <p className="text-sm text-muted-foreground">
                This will be the URL: /{formData.slug}
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="meta_description">Meta Description</Label>
              <Textarea
                id="meta_description"
                value={formData.meta_description}
                onChange={(e) => setFormData(prev => ({ ...prev, meta_description: e.target.value }))}
                placeholder="Brief description for search engines..."
                rows={3}
              />
              <p className="text-sm text-muted-foreground">
                Recommended: 150-160 characters
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Content
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant={useRichEditor ? "default" : "outline"}
                  size="sm"
                  onClick={() => setUseRichEditor(true)}
                >
                  Rich Editor
                </Button>
                <Button
                  type="button"
                  variant={!useRichEditor ? "default" : "outline"}
                  size="sm"
                  onClick={() => setUseRichEditor(false)}
                >
                  Simple Editor
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {useRichEditor ? (
              <RichTextEditor
                value={formData.content}
                onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                placeholder="Write your page content here..."
              />
            ) : (
              <SimpleEditor
                value={formData.content}
                onChange={(content) => setFormData(prev => ({ ...prev, content }))}
                placeholder="Write your page content here..."
              />
            )}
          </CardContent>
        </Card>

        <Separator />

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <span className="text-sm text-muted-foreground">
              Status: <strong>{formData.status === 'published' ? 'Published' : 'Draft'}</strong>
            </span>
          </div>
          
          <div className="flex items-center gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/admin?tab=pages')}
            >
              Cancel
            </Button>
            
            <Button
              type="submit"
              variant="outline"
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
            
            <Button
              type="button"
              onClick={(e) => handleSubmit(e, true)}
              disabled={createMutation.isPending || updateMutation.isPending}
            >
              <Eye className="h-4 w-4 mr-2" />
              {formData.status === 'published' ? 'Update & Keep Published' : 'Publish Now'}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
};
