import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  MessageSquare, 
  Star, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Eye, 
  Trash2,
  Filter,
  Bar<PERSON>hart3,
  Mail,
  Calendar
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { 
  useAdminFeedback, 
  useUpdateFeedbackStatus, 
  useDeleteFeedback, 
  useFeedbackStats,
  formatFeedbackForDisplay,
  FeedbackRow 
} from '@/hooks/useFeedback';

const FeedbackManagement: React.FC = () => {
  const [selectedStatus, setSelectedStatus] = useState<FeedbackRow['status'] | 'all'>('all');
  const [selectedCategory, setSelectedCategory] = useState<FeedbackRow['category'] | 'all'>('all');
  const [selectedFeedback, setSelectedFeedback] = useState<FeedbackRow | null>(null);
  const [adminNotes, setAdminNotes] = useState('');

  // Hooks
  const { data: feedbackList, isLoading, error } = useAdminFeedback({
    status: selectedStatus === 'all' ? undefined : selectedStatus,
    category: selectedCategory === 'all' ? undefined : selectedCategory,
  });
  
  const { data: stats } = useFeedbackStats();
  const updateStatusMutation = useUpdateFeedbackStatus();
  const deleteFeedbackMutation = useDeleteFeedback();

  const handleStatusUpdate = async (id: string, status: FeedbackRow['status']) => {
    try {
      await updateStatusMutation.mutateAsync({
        id,
        status,
        adminNotes: adminNotes.trim() || undefined,
        resolvedBy: status === 'resolved' || status === 'closed' ? 'current-admin-id' : undefined, // TODO: Get actual admin ID
      });
      
      toast({
        title: "Status Updated",
        description: `Feedback status changed to ${status}`,
      });
      
      setAdminNotes('');
      setSelectedFeedback(null);
    } catch (error) {
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Failed to update feedback status",
        variant: "destructive",
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this feedback? This action cannot be undone.')) {
      return;
    }

    try {
      await deleteFeedbackMutation.mutateAsync(id);
      toast({
        title: "Feedback Deleted",
        description: "The feedback has been permanently deleted",
      });
      setSelectedFeedback(null);
    } catch (error) {
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete feedback",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: FeedbackRow['status']) => {
    const variants = {
      new: { variant: 'default' as const, icon: Clock, color: 'text-blue-600' },
      in_progress: { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
      resolved: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      closed: { variant: 'outline' as const, icon: XCircle, color: 'text-gray-600' },
    };
    
    const config = variants[status];
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center space-x-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        <span>{status.replace('_', ' ')}</span>
      </Badge>
    );
  };

  const getCategoryBadge = (category: FeedbackRow['category']) => {
    const colors = {
      general: 'bg-gray-100 text-gray-800',
      content: 'bg-blue-100 text-blue-800',
      technical: 'bg-red-100 text-red-800',
      suggestion: 'bg-green-100 text-green-800',
      complaint: 'bg-orange-100 text-orange-800',
      compliment: 'bg-purple-100 text-purple-800',
    };
    
    return (
      <Badge variant="outline" className={colors[category]}>
        {category}
      </Badge>
    );
  };

  const renderStars = (rating: number | null) => {
    if (!rating) return <span className="text-gray-400">No rating</span>;
    
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="text-sm text-gray-600 ml-2">({rating}/5)</span>
      </div>
    );
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Failed to load feedback data. Please try again later.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Feedback Management</h1>
          <p className="text-muted-foreground">Manage user feedback and support requests</p>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-blue-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Total Feedback</p>
                  <p className="text-2xl font-bold">{stats.total_feedback || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Clock className="h-5 w-5 text-orange-600" />
                <div>
                  <p className="text-sm text-muted-foreground">New</p>
                  <p className="text-2xl font-bold">{stats.new_feedback || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Resolved</p>
                  <p className="text-2xl font-bold">{stats.resolved || 0}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-muted-foreground">Avg Rating</p>
                  <p className="text-2xl font-bold">{stats.average_rating || 'N/A'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Filter className="h-5 w-5" />
            <span>Filters</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status-filter">Status</Label>
              <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label htmlFor="category-filter">Category</Label>
              <Select value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="content">Content</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                  <SelectItem value="suggestion">Suggestion</SelectItem>
                  <SelectItem value="complaint">Complaint</SelectItem>
                  <SelectItem value="compliment">Compliment</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feedback List */}
      <Card>
        <CardHeader>
          <CardTitle>Feedback Items</CardTitle>
          <CardDescription>
            {feedbackList?.length || 0} feedback items found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-16" />
                  </div>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ))}
            </div>
          ) : feedbackList && feedbackList.length > 0 ? (
            <div className="space-y-4">
              {feedbackList.map((feedback) => {
                const formatted = formatFeedbackForDisplay(feedback);
                return (
                  <div key={feedback.id} className="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="font-semibold">{feedback.subject}</h3>
                          {getStatusBadge(feedback.status)}
                          {getCategoryBadge(feedback.category)}
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mb-2">
                          <span className="flex items-center space-x-1">
                            <Mail className="h-4 w-4" />
                            <span>{feedback.name} ({feedback.email})</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{formatted.formattedDate}</span>
                          </span>
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {feedback.message}
                        </p>
                        
                        {feedback.rating && (
                          <div className="mb-2">
                            {renderStars(feedback.rating)}
                          </div>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedFeedback(feedback);
                                setAdminNotes(feedback.admin_notes || '');
                              }}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>Feedback Details</DialogTitle>
                              <DialogDescription>
                                Manage this feedback item
                              </DialogDescription>
                            </DialogHeader>
                            
                            {selectedFeedback && (
                              <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <Label>Name</Label>
                                    <p className="text-sm">{selectedFeedback.name}</p>
                                  </div>
                                  <div>
                                    <Label>Email</Label>
                                    <p className="text-sm">{selectedFeedback.email}</p>
                                  </div>
                                  <div>
                                    <Label>Category</Label>
                                    <p className="text-sm">{getCategoryBadge(selectedFeedback.category)}</p>
                                  </div>
                                  <div>
                                    <Label>Rating</Label>
                                    <div className="text-sm">{renderStars(selectedFeedback.rating)}</div>
                                  </div>
                                </div>
                                
                                <div>
                                  <Label>Subject</Label>
                                  <p className="text-sm font-medium">{selectedFeedback.subject}</p>
                                </div>
                                
                                <div>
                                  <Label>Message</Label>
                                  <p className="text-sm whitespace-pre-wrap bg-gray-50 p-3 rounded">
                                    {selectedFeedback.message}
                                  </p>
                                </div>
                                
                                <div>
                                  <Label htmlFor="admin-notes">Admin Notes</Label>
                                  <Textarea
                                    id="admin-notes"
                                    value={adminNotes}
                                    onChange={(e) => setAdminNotes(e.target.value)}
                                    placeholder="Add internal notes about this feedback..."
                                    rows={3}
                                  />
                                </div>
                                
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-2">
                                    <Label>Status:</Label>
                                    <Select
                                      value={selectedFeedback.status}
                                      onValueChange={(status) => handleStatusUpdate(selectedFeedback.id, status as FeedbackRow['status'])}
                                    >
                                      <SelectTrigger className="w-32">
                                        <SelectValue />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectItem value="new">New</SelectItem>
                                        <SelectItem value="in_progress">In Progress</SelectItem>
                                        <SelectItem value="resolved">Resolved</SelectItem>
                                        <SelectItem value="closed">Closed</SelectItem>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                  
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => handleDelete(selectedFeedback.id)}
                                    disabled={deleteFeedbackMutation.isPending}
                                  >
                                    <Trash2 className="h-4 w-4 mr-1" />
                                    Delete
                                  </Button>
                                </div>
                              </div>
                            )}
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No feedback found matching your filters</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default FeedbackManagement;
