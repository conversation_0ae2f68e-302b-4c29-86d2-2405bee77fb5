import React from 'react';
import { useAdminContent } from '@/hooks/useContent';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  TrendingUp, 
  Newspaper, 
  BookOpen, 
  FileText, 
  Users,
  Eye,
  EyeOff
} from 'lucide-react';

export const AdminStats: React.FC = () => {
  const { data: insights, isLoading: insightsLoading } = useAdminContent('insights');
  const { data: news, isLoading: newsLoading } = useAdminContent('news');
  const { data: education, isLoading: educationLoading } = useAdminContent('education');
  const { data: caseStudies, isLoading: caseStudiesLoading } = useAdminContent('case_studies');

  const isLoading = insightsLoading || newsLoading || educationLoading || caseStudiesLoading;

  const getStats = () => {
    return {
      insights: {
        total: insights?.length || 0,
        published: insights?.filter(item => item.status === 'published').length || 0,
        drafts: insights?.filter(item => item.status === 'draft').length || 0,
      },
      news: {
        total: news?.length || 0,
        published: news?.filter(item => item.status === 'published').length || 0,
        drafts: news?.filter(item => item.status === 'draft').length || 0,
      },
      education: {
        total: education?.length || 0,
        published: education?.filter(item => item.status === 'published').length || 0,
        drafts: education?.filter(item => item.status === 'draft').length || 0,
      },
      caseStudies: {
        total: caseStudies?.length || 0,
        published: caseStudies?.filter(item => item.status === 'published').length || 0,
        drafts: caseStudies?.filter(item => item.status === 'draft').length || 0,
      },
    };
  };

  const stats = getStats();
  const totalContent = stats.insights.total + stats.news.total + stats.education.total + stats.caseStudies.total;
  const totalPublished = stats.insights.published + stats.news.published + stats.education.published + stats.caseStudies.published;
  const totalDrafts = stats.insights.drafts + stats.news.drafts + stats.education.drafts + stats.caseStudies.drafts;

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-8 w-16" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Content</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalContent}</div>
            <p className="text-xs text-muted-foreground">
              All content items
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
            <Eye className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{totalPublished}</div>
            <p className="text-xs text-muted-foreground">
              Live content
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <EyeOff className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{totalDrafts}</div>
            <p className="text-xs text-muted-foreground">
              Unpublished content
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Content Type Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Insights</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.insights.total}</div>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Eye className="h-3 w-3 text-green-600" />
                {stats.insights.published}
              </span>
              <span className="flex items-center gap-1">
                <EyeOff className="h-3 w-3 text-orange-600" />
                {stats.insights.drafts}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">News</CardTitle>
            <Newspaper className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.news.total}</div>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Eye className="h-3 w-3 text-green-600" />
                {stats.news.published}
              </span>
              <span className="flex items-center gap-1">
                <EyeOff className="h-3 w-3 text-orange-600" />
                {stats.news.drafts}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Education</CardTitle>
            <BookOpen className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.education.total}</div>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Eye className="h-3 w-3 text-green-600" />
                {stats.education.published}
              </span>
              <span className="flex items-center gap-1">
                <EyeOff className="h-3 w-3 text-orange-600" />
                {stats.education.drafts}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Case Studies</CardTitle>
            <FileText className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.caseStudies.total}</div>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span className="flex items-center gap-1">
                <Eye className="h-3 w-3 text-green-600" />
                {stats.caseStudies.published}
              </span>
              <span className="flex items-center gap-1">
                <EyeOff className="h-3 w-3 text-orange-600" />
                {stats.caseStudies.drafts}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
