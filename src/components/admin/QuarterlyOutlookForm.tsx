import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { X, Plus } from 'lucide-react';
import { useCreateQuarterlyOutlook, useUpdateQuarterlyOutlook } from '@/hooks/useContent';
import { toast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type QuarterlyOutlookData = Database['public']['Tables']['quarterly_outlook']['Row'];
type QuarterlyOutlookInsert = Database['public']['Tables']['quarterly_outlook']['Insert'];

interface QuarterlyOutlookFormProps {
  mode: 'create' | 'edit';
  initialData?: QuarterlyOutlookData;
  onSuccess?: () => void;
  onCancel?: () => void;
}

interface FormData {
  title: string;
  quarter: 'Q1' | 'Q2' | 'Q3' | 'Q4';
  year: number;
  market: 'us' | 'india';
  summary: string;
  key_metrics: {
    gdp_growth?: number;
    inflation_rate?: number;
    interest_rate?: number;
    market_outlook?: 'bullish' | 'bearish' | 'neutral';
    sector_highlights?: string[];
  };
  detailed_analysis: string;
  risk_factors: string[];
  opportunities: string[];
  status: 'draft' | 'published';
}

const QuarterlyOutlookForm: React.FC<QuarterlyOutlookFormProps> = ({
  mode,
  initialData,
  onSuccess,
  onCancel,
}) => {
  const [formData, setFormData] = useState<FormData>({
    title: '',
    quarter: 'Q1',
    year: new Date().getFullYear(),
    market: 'us',
    summary: '',
    key_metrics: {
      sector_highlights: [],
    },
    detailed_analysis: '',
    risk_factors: [],
    opportunities: [],
    status: 'draft',
  });

  const [newRiskFactor, setNewRiskFactor] = useState('');
  const [newOpportunity, setNewOpportunity] = useState('');
  const [newSectorHighlight, setNewSectorHighlight] = useState('');

  const createMutation = useCreateQuarterlyOutlook();
  const updateMutation = useUpdateQuarterlyOutlook();

  useEffect(() => {
    if (mode === 'edit' && initialData) {
      setFormData({
        title: initialData.title,
        quarter: initialData.quarter,
        year: initialData.year,
        market: initialData.market,
        summary: initialData.summary,
        key_metrics: typeof initialData.key_metrics === 'object' ? initialData.key_metrics as any : {},
        detailed_analysis: initialData.detailed_analysis,
        risk_factors: initialData.risk_factors || [],
        opportunities: initialData.opportunities || [],
        status: initialData.status,
      });
    }
  }, [mode, initialData]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const submitData: QuarterlyOutlookInsert = {
        ...formData,
        key_metrics: formData.key_metrics,
        published_at: formData.status === 'published' ? new Date().toISOString() : null,
      };

      if (mode === 'create') {
        await createMutation.mutateAsync(submitData);
        toast({
          title: 'Success',
          description: 'Quarterly outlook created successfully.',
        });
      } else if (initialData) {
        await updateMutation.mutateAsync({
          id: initialData.id,
          ...submitData,
        });
        toast({
          title: 'Success',
          description: 'Quarterly outlook updated successfully.',
        });
      }

      onSuccess?.();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to save quarterly outlook. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const addRiskFactor = () => {
    if (newRiskFactor.trim()) {
      setFormData(prev => ({
        ...prev,
        risk_factors: [...prev.risk_factors, newRiskFactor.trim()],
      }));
      setNewRiskFactor('');
    }
  };

  const removeRiskFactor = (index: number) => {
    setFormData(prev => ({
      ...prev,
      risk_factors: prev.risk_factors.filter((_, i) => i !== index),
    }));
  };

  const addOpportunity = () => {
    if (newOpportunity.trim()) {
      setFormData(prev => ({
        ...prev,
        opportunities: [...prev.opportunities, newOpportunity.trim()],
      }));
      setNewOpportunity('');
    }
  };

  const removeOpportunity = (index: number) => {
    setFormData(prev => ({
      ...prev,
      opportunities: prev.opportunities.filter((_, i) => i !== index),
    }));
  };

  const addSectorHighlight = () => {
    if (newSectorHighlight.trim()) {
      setFormData(prev => ({
        ...prev,
        key_metrics: {
          ...prev.key_metrics,
          sector_highlights: [
            ...(prev.key_metrics.sector_highlights || []),
            newSectorHighlight.trim(),
          ],
        },
      }));
      setNewSectorHighlight('');
    }
  };

  const removeSectorHighlight = (index: number) => {
    setFormData(prev => ({
      ...prev,
      key_metrics: {
        ...prev.key_metrics,
        sector_highlights: prev.key_metrics.sector_highlights?.filter((_, i) => i !== index) || [],
      },
    }));
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Card className="max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>
          {mode === 'create' ? 'Create' : 'Edit'} Quarterly Outlook
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="title">Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                placeholder="Q1 2025 US Market Outlook"
                required
              />
            </div>
            <div>
              <Label htmlFor="market">Market</Label>
              <Select
                value={formData.market}
                onValueChange={(value: 'us' | 'india') => 
                  setFormData(prev => ({ ...prev, market: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="us">US Market</SelectItem>
                  <SelectItem value="india">Indian Market</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="quarter">Quarter</Label>
              <Select
                value={formData.quarter}
                onValueChange={(value: 'Q1' | 'Q2' | 'Q3' | 'Q4') => 
                  setFormData(prev => ({ ...prev, quarter: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Q1">Q1</SelectItem>
                  <SelectItem value="Q2">Q2</SelectItem>
                  <SelectItem value="Q3">Q3</SelectItem>
                  <SelectItem value="Q4">Q4</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="year">Year</Label>
              <Input
                id="year"
                type="number"
                value={formData.year}
                onChange={(e) => setFormData(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                min={2020}
                max={2030}
                required
              />
            </div>
          </div>

          {/* Summary */}
          <div>
            <Label htmlFor="summary">Summary</Label>
            <Textarea
              id="summary"
              value={formData.summary}
              onChange={(e) => setFormData(prev => ({ ...prev, summary: e.target.value }))}
              placeholder="Brief summary of the quarterly outlook..."
              rows={3}
              required
            />
          </div>

          {/* Key Metrics */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Key Metrics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="gdp_growth">GDP Growth (%)</Label>
                <Input
                  id="gdp_growth"
                  type="number"
                  step="0.1"
                  value={formData.key_metrics.gdp_growth || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    key_metrics: {
                      ...prev.key_metrics,
                      gdp_growth: e.target.value ? parseFloat(e.target.value) : undefined,
                    },
                  }))}
                  placeholder="2.8"
                />
              </div>
              <div>
                <Label htmlFor="inflation_rate">Inflation Rate (%)</Label>
                <Input
                  id="inflation_rate"
                  type="number"
                  step="0.1"
                  value={formData.key_metrics.inflation_rate || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    key_metrics: {
                      ...prev.key_metrics,
                      inflation_rate: e.target.value ? parseFloat(e.target.value) : undefined,
                    },
                  }))}
                  placeholder="3.2"
                />
              </div>
              <div>
                <Label htmlFor="interest_rate">Interest Rate (%)</Label>
                <Input
                  id="interest_rate"
                  type="number"
                  step="0.1"
                  value={formData.key_metrics.interest_rate || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    key_metrics: {
                      ...prev.key_metrics,
                      interest_rate: e.target.value ? parseFloat(e.target.value) : undefined,
                    },
                  }))}
                  placeholder="5.25"
                />
              </div>
              <div>
                <Label htmlFor="market_outlook">Market Outlook</Label>
                <Select
                  value={formData.key_metrics.market_outlook || ''}
                  onValueChange={(value: 'bullish' | 'bearish' | 'neutral') => 
                    setFormData(prev => ({
                      ...prev,
                      key_metrics: {
                        ...prev.key_metrics,
                        market_outlook: value,
                      },
                    }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select outlook" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="bullish">Bullish</SelectItem>
                    <SelectItem value="neutral">Neutral</SelectItem>
                    <SelectItem value="bearish">Bearish</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Sector Highlights */}
          <div>
            <Label>Sector Highlights</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newSectorHighlight}
                onChange={(e) => setNewSectorHighlight(e.target.value)}
                placeholder="Add sector highlight..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addSectorHighlight())}
              />
              <Button type="button" onClick={addSectorHighlight} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {formData.key_metrics.sector_highlights?.map((highlight, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {highlight}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeSectorHighlight(index)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Detailed Analysis */}
          <div>
            <Label htmlFor="detailed_analysis">Detailed Analysis</Label>
            <Textarea
              id="detailed_analysis"
              value={formData.detailed_analysis}
              onChange={(e) => setFormData(prev => ({ ...prev, detailed_analysis: e.target.value }))}
              placeholder="Comprehensive analysis of the quarterly outlook..."
              rows={6}
              required
            />
          </div>

          {/* Risk Factors */}
          <div>
            <Label>Risk Factors</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newRiskFactor}
                onChange={(e) => setNewRiskFactor(e.target.value)}
                placeholder="Add risk factor..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addRiskFactor())}
              />
              <Button type="button" onClick={addRiskFactor} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-2 mt-2">
              {formData.risk_factors.map((risk, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded">
                  <span className="flex-1">{risk}</span>
                  <X
                    className="h-4 w-4 cursor-pointer text-muted-foreground hover:text-destructive"
                    onClick={() => removeRiskFactor(index)}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Opportunities */}
          <div>
            <Label>Opportunities</Label>
            <div className="flex gap-2 mt-2">
              <Input
                value={newOpportunity}
                onChange={(e) => setNewOpportunity(e.target.value)}
                placeholder="Add opportunity..."
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addOpportunity())}
              />
              <Button type="button" onClick={addOpportunity} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="space-y-2 mt-2">
              {formData.opportunities.map((opportunity, index) => (
                <div key={index} className="flex items-center gap-2 p-2 bg-muted rounded">
                  <span className="flex-1">{opportunity}</span>
                  <X
                    className="h-4 w-4 cursor-pointer text-muted-foreground hover:text-destructive"
                    onClick={() => removeOpportunity(index)}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Status */}
          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              onValueChange={(value: 'draft' | 'published') => 
                setFormData(prev => ({ ...prev, status: value }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="published">Published</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Actions */}
          <div className="flex gap-4 pt-4">
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Saving...' : mode === 'create' ? 'Create' : 'Update'}
            </Button>
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default QuarterlyOutlookForm;
