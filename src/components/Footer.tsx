
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { MessageSquare } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-card mt-16">
      <div className="container py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="md:col-span-2">
            <h3 className="text-lg font-bold">Syed's Investments</h3>
            <p className="text-muted-foreground mt-2 mb-4">
              Empowering investors with accessible, accurate, and Shariah-compliant financial education
              to make informed investment decisions.
            </p>
            <div className="flex items-center space-x-4">
              <Input
                placeholder="Your email address"
                className="max-w-xs"
                type="email"
              />
              <Button>Subscribe</Button>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link to="/market-dashboard" className="text-muted-foreground hover:text-primary transition-colors">Market Dashboard</Link></li>
              <li><Link to="/insights" className="text-muted-foreground hover:text-primary transition-colors">Insights</Link></li>
              <li><Link to="/news" className="text-muted-foreground hover:text-primary transition-colors">News</Link></li>
              <li><Link to="/education" className="text-muted-foreground hover:text-primary transition-colors">Education</Link></li>
              <li><Link to="/case-studies" className="text-muted-foreground hover:text-primary transition-colors">Case Studies</Link></li>
              <li><Link to="/about" className="text-muted-foreground hover:text-primary transition-colors">About Us</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Contact</h3>
            <address className="not-italic text-muted-foreground">
              <p>Syed's Investments Advisory</p>
              <p>123 Finance Street</p>
              <p>Mumbai, Maharashtra 400001</p>
              <p className="mt-2">Email: <EMAIL></p>
              <p>Phone: +91 9876543210</p>
            </address>
          </div>
        </div>
        
        <Separator className="my-8" />
        
        <div className="flex flex-col md:flex-row justify-between items-center">
          <p className="text-muted-foreground text-sm">
            &copy; {new Date().getFullYear()} Syed's Investments. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="text-muted-foreground hover:text-primary text-sm">Privacy Policy</a>
            <a href="#" className="text-muted-foreground hover:text-primary text-sm">Terms of Service</a>
            <a href="#" className="text-muted-foreground hover:text-primary text-sm">Disclaimer</a>
            <Link to="/feedback" className="text-muted-foreground hover:text-primary text-sm flex items-center space-x-1">
              <MessageSquare className="h-3 w-3" />
              <span>Feedback</span>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
