import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Bold, Italic, List, Link } from 'lucide-react';

interface SimpleEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

export const SimpleEditor: React.FC<SimpleEditorProps> = ({
  value,
  onChange,
  placeholder = "Write your content here..."
}) => {
  const insertText = (before: string, after: string = '') => {
    const textarea = document.getElementById('content-textarea') as HTMLTextAreaElement;
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = value.substring(start, end);
    
    const newText = value.substring(0, start) + before + selectedText + after + value.substring(end);
    onChange(newText);
    
    // Restore cursor position
    setTimeout(() => {
      textarea.focus();
      textarea.setSelectionRange(start + before.length, end + before.length);
    }, 0);
  };

  const formatButtons = [
    {
      icon: Bold,
      label: 'Bold',
      action: () => insertText('**', '**'),
    },
    {
      icon: Italic,
      label: 'Italic',
      action: () => insertText('*', '*'),
    },
    {
      icon: List,
      label: 'List',
      action: () => insertText('\n- '),
    },
    {
      icon: Link,
      label: 'Link',
      action: () => insertText('[', '](url)'),
    },
  ];

  return (
    <div className="border rounded-md">
      {/* Toolbar */}
      <div className="flex items-center gap-1 p-2 border-b bg-gray-50">
        {formatButtons.map((button) => (
          <Button
            key={button.label}
            type="button"
            variant="ghost"
            size="sm"
            onClick={button.action}
            className="h-8 w-8 p-0"
            title={button.label}
          >
            <button.icon className="h-4 w-4" />
          </Button>
        ))}
        <div className="ml-auto text-xs text-gray-500">
          Markdown supported
        </div>
      </div>
      
      {/* Editor */}
      <Textarea
        id="content-textarea"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="min-h-[200px] border-0 resize-none focus:ring-0"
      />
      
      {/* Help Text */}
      <div className="p-2 text-xs text-gray-500 border-t bg-gray-50">
        <strong>Formatting tips:</strong> **bold**, *italic*, - list item, [link text](url)
      </div>
    </div>
  );
};
