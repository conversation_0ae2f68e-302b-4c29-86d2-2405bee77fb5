import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ChevronRight, TrendingUp, TrendingDown, Calendar, Globe, MapPin, Eye, ArrowRight, Shield, Database, Users, ChevronDown, BrainIcon, LucideDatabase, DatabaseZapIcon } from 'lucide-react';
import { useQuarterlyOutlook } from '@/hooks/useContent';
import ScrollArrow from '@/components/ScrollArrow';
import CompanyInfoModal from '@/components/CompanyInfoModal';

interface QuarterlyOutlookData {
  id: string;
  title: string;
  quarter: string;
  year: number;
  market: 'us' | 'india';
  summary: string;
  key_metrics: {
    gdp_growth?: number;
    inflation_rate?: number;
    interest_rate?: number;
    market_outlook?: 'bullish' | 'bearish' | 'neutral';
    sector_highlights?: string[];
  };
  detailed_analysis: string;
  risk_factors: string[];
  opportunities: string[];
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
}

// Fallback data for when API is unavailable
const fallbackQuarterlyData: QuarterlyOutlookData[] = [
  {
    id: '1',
    title: 'Q4 2024 US Market Outlook',
    quarter: 'Q4',
    year: 2024,
    market: 'us',
    summary: 'Strong economic fundamentals with cautious optimism amid global uncertainties.',
    key_metrics: {
      gdp_growth: 2.8,
      inflation_rate: 3.2,
      interest_rate: 5.25,
      market_outlook: 'bullish',
      sector_highlights: ['Technology', 'Healthcare', 'Financial Services']
    },
    detailed_analysis: 'The US market shows resilience with strong corporate earnings and consumer spending...',
    risk_factors: ['Geopolitical tensions', 'Inflation concerns', 'Supply chain disruptions'],
    opportunities: ['AI and Technology growth', 'Infrastructure investments', 'Green energy transition'],
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '2',
    title: 'Q1 2025 US Market Outlook',
    quarter: 'Q1',
    year: 2025,
    market: 'us',
    summary: 'Continued growth momentum with focus on emerging technologies and sustainable investments.',
    key_metrics: {
      gdp_growth: 3.1,
      inflation_rate: 2.9,
      interest_rate: 5.0,
      market_outlook: 'bullish',
      sector_highlights: ['Artificial Intelligence', 'Renewable Energy', 'Biotechnology']
    },
    detailed_analysis: 'Q1 2025 presents opportunities in emerging sectors with strong fundamentals...',
    risk_factors: ['Market volatility', 'Regulatory changes', 'Global economic slowdown'],
    opportunities: ['Tech innovation', 'ESG investments', 'Healthcare advancements'],
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '3',
    title: 'Q4 2024 Indian Market Outlook',
    quarter: 'Q4',
    year: 2024,
    market: 'india',
    summary: 'Robust domestic demand driving growth with strong manufacturing and services sectors.',
    key_metrics: {
      gdp_growth: 6.8,
      inflation_rate: 4.5,
      interest_rate: 6.5,
      market_outlook: 'bullish',
      sector_highlights: ['Manufacturing', 'IT Services', 'Financial Services']
    },
    detailed_analysis: 'India\'s economy continues to show strong fundamentals with domestic consumption...',
    risk_factors: ['Monsoon dependency', 'Global trade tensions', 'Commodity price volatility'],
    opportunities: ['Digital transformation', 'Infrastructure development', 'Manufacturing growth'],
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '4',
    title: 'Q1 2025 Indian Market Outlook',
    quarter: 'Q1',
    year: 2025,
    market: 'india',
    summary: 'Sustained growth trajectory with focus on digital economy and sustainable development.',
    key_metrics: {
      gdp_growth: 7.2,
      inflation_rate: 4.2,
      interest_rate: 6.25,
      market_outlook: 'bullish',
      sector_highlights: ['Digital Economy', 'Green Energy', 'Healthcare']
    },
    detailed_analysis: 'Q1 2025 outlook remains positive with strong policy support and investment flows...',
    risk_factors: ['External headwinds', 'Fiscal deficit concerns', 'Climate risks'],
    opportunities: ['Digital India initiatives', 'Renewable energy expansion', 'Healthcare innovation'],
    status: 'published',
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  }
];



const QuarterlyOutlookCard: React.FC<{
  data: QuarterlyOutlookData;
  onViewDetails: (data: QuarterlyOutlookData) => void;
}> = ({ data, onViewDetails }) => {
  const getOutlookColor = (outlook: string) => {
    switch (outlook) {
      case 'bullish': return 'text-market-up';
      case 'bearish': return 'text-market-down';
      default: return 'text-market-neutral';
    }
  };

  const getOutlookIcon = (outlook: string) => {
    switch (outlook) {
      case 'bullish': return <TrendingUp className="h-4 w-4" />;
      case 'bearish': return <TrendingDown className="h-4 w-4" />;
      default: return <Calendar className="h-4 w-4" />;
    }
  };

  return (
    <Card className="group hover:shadow-lg hover:shadow-primary/10 transition-all duration-300 cursor-pointer border-border hover:border-primary/30 hover:-translate-y-0.5 bg-gradient-to-br from-background to-background/50 backdrop-blur-sm"
          onClick={() => onViewDetails(data)}>
      <CardHeader className="pb-2 pt-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${data.market === 'us' ? 'bg-blue-500/10' : 'bg-orange-500/10'}`}>
              {data.market === 'us' ? (
                <Globe className="h-4 w-4 text-blue-500" />
              ) : (
                <MapPin className="h-4 w-4 text-orange-500" />
              )}
            </div>
            <CardTitle className="text-base font-semibold group-hover:text-primary transition-colors">{data.title}</CardTitle>
          </div>
          <ChevronRight className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-300" />
        </div>
      </CardHeader>
      <CardContent className="space-y-3 pt-0">
        <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">{data.summary}</p>

        <div className="flex flex-wrap gap-2">
          {data.key_metrics.market_outlook && (
            <Badge variant="outline" className={`${getOutlookColor(data.key_metrics.market_outlook)} border-current text-xs font-medium px-2 py-1`}>
              <span className="flex items-center gap-1.5">
                {getOutlookIcon(data.key_metrics.market_outlook)}
                {data.key_metrics.market_outlook.charAt(0).toUpperCase() + data.key_metrics.market_outlook.slice(1)}
              </span>
            </Badge>
          )}
          {data.key_metrics.gdp_growth && (
            <Badge variant="secondary" className="text-xs font-medium px-2 py-1">
              GDP: {data.key_metrics.gdp_growth}%
            </Badge>
          )}
        </div>

        <div className="grid grid-cols-2 gap-2 text-xs">
          {data.key_metrics.inflation_rate && (
            <div>
              <span className="text-muted-foreground">Inflation:</span>
              <span className="ml-1 font-medium">{data.key_metrics.inflation_rate}%</span>
            </div>
          )}
          {data.key_metrics.interest_rate && (
            <div>
              <span className="text-muted-foreground">Interest:</span>
              <span className="ml-1 font-medium">{data.key_metrics.interest_rate}%</span>
            </div>
          )}
        </div>

        {data.key_metrics.sector_highlights && data.key_metrics.sector_highlights.length > 0 && (
          <div>
            <p className="text-xs text-muted-foreground mb-1">Key Sectors:</p>
            <div className="flex flex-wrap gap-1">
              {data.key_metrics.sector_highlights.slice(0, 2).map((sector, index) => (
                <Badge key={index} variant="outline" className="text-xs px-1 py-0">
                  {sector}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

const QuarterlyOutlookSection: React.FC = () => {
  const navigate = useNavigate();
  const [selectedOutlook, setSelectedOutlook] = useState<QuarterlyOutlookData | null>(null);
  const [isCompanyInfoOpen, setIsCompanyInfoOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const { data: outlookResponse, isLoading, error } = useQuarterlyOutlook();
  const quarterlyOutlookRef = useRef<HTMLDivElement>(null);

  const handleScrollArrowClick = () => {
    setIsCompanyInfoOpen(true);
  };

  const handleExploreMarketsClick = () => {
    if (quarterlyOutlookRef.current) {
      quarterlyOutlookRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  useEffect(() => {
    // Trigger animations after component mounts
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  // Use fallback data if API fails or returns no data
  const outlookData = outlookResponse || fallbackQuarterlyData;

  // Debug logging
  console.log('QuarterlyOutlookSection Debug:', {
    isLoading,
    error,
    outlookResponse,
    outlookDataLength: outlookData?.length,
    outlookData: outlookData?.slice(0, 2)
  });

  const usMarketData = outlookData.filter(item => item.market === 'us').slice(0, 2);
  const indiaMarketData = outlookData.filter(item => item.market === 'india').slice(0, 2);

  const handleViewDetails = (data: QuarterlyOutlookData) => {
    setSelectedOutlook(data);
  };

  const closeDetails = () => {
    setSelectedOutlook(null);
  };

  if (isLoading) {
    return (
      <div className="relative min-h-screen lg:min-h-[95vh] xl:min-h-screen flex flex-col bg-gradient-to-br from-background via-background to-muted/20">
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-background/40"></div>

        <div className="relative flex-grow flex flex-col justify-center py-12 lg:py-16">
          {/* Hero Content Skeleton */}
          <div className="text-center mb-16 space-y-6">
            <div className="space-y-6">
              <div className="h-16 bg-muted animate-pulse rounded-lg max-w-4xl mx-auto"></div>
              <div className="h-6 bg-muted animate-pulse rounded max-w-3xl mx-auto"></div>
            </div>
            <div className="grid grid-cols-2 md:flex justify-center gap-6 md:gap-10 max-w-3xl mx-auto">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="h-4 w-32 bg-muted animate-pulse rounded"></div>
              ))}
            </div>
            <div className="flex flex-col sm:flex-row justify-center gap-6 px-4 pt-8">
              <div className="h-12 w-full sm:w-44 bg-muted animate-pulse rounded-lg"></div>
              <div className="h-12 w-full sm:w-44 bg-muted animate-pulse rounded-lg"></div>
            </div>

            {/* Animated Scroll Arrow Skeleton */}
            <div className="flex justify-center mt-8 lg:mt-12">
              <div className="flex flex-col items-center">
                <div className="relative flex flex-col items-center p-3 rounded-full bg-background/80 backdrop-blur-sm border border-border/50 shadow-lg">
                  <ChevronDown
                    className="h-6 w-6 text-muted-foreground/50 drop-shadow-sm"
                    style={{
                      animation: 'bounce 2s infinite',
                      animationDelay: '0s'
                    }}
                  />
                  <ChevronDown
                    className="h-6 w-6 text-muted-foreground/30 -mt-3 drop-shadow-sm"
                    style={{
                      animation: 'bounce 2s infinite',
                      animationDelay: '0.3s'
                    }}
                  />
                </div>
                <div className="h-3 w-24 bg-muted animate-pulse rounded mt-2 bg-background/80 backdrop-blur-sm border border-border/30"></div>
              </div>
            </div>
          </div>

          <div className="text-center mb-8 px-4 mt-24 lg:mt-32">
            <div className="h-8 bg-muted animate-pulse rounded max-w-lg mx-auto mb-4"></div>
            <div className="h-5 bg-muted animate-pulse rounded max-w-3xl mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-w-5xl mx-auto px-4">
            {[1, 2].map((i) => (
              <div key={i} className="space-y-4">
                <div className="h-14 bg-muted animate-pulse rounded-lg"></div>
                <div className="space-y-3">
                  {[1, 2].map((j) => (
                    <div key={j} className="h-40 bg-muted animate-pulse rounded-lg"></div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        <ScrollArrow onClick={handleScrollArrowClick} />
      </div>
    );
  }

  return (
    <>
      <div className="relative min-h-screen lg:min-h-[95vh] xl:min-h-screen flex flex-col bg-gradient-to-br from-background via-background to-muted/20">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-background/40"></div>

        <div className="relative flex-grow flex flex-col justify-center py-12 lg:py-16">
          {/* Hero Content */}
          <div className="text-center mb-16 space-y-6">
            <div className="space-y-6">
              <h1 className="text-4xl md:text-5xl lg:text-6xl my-2 p-5 font-bold tracking-tight bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                Where Vision Meets Valuation
              </h1>
              <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                Powered by deep research analysis and advanced Artificial Intelligence, our platform offers a new way to understand the U.S. and Indian stock markets
              </p>
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-2 md:flex md:flex-wrap justify-center gap-6 md:gap-10 text-sm max-w-3xl mx-auto">
              <div className="flex items-center gap-2 text-muted-foreground justify-center md:justify-start">
                <Calendar className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="font-medium">5+ Years Experience</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground justify-center md:justify-start">
                <Shield className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="font-medium">Shariah-Compliant</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground justify-center md:justify-start">
                <DatabaseZapIcon className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="font-medium">Real-time Data</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground justify-center md:justify-start">
                <BrainIcon className="h-4 w-4 text-primary flex-shrink-0" />
                <span className="font-medium">Deep Research Analysis</span>
              </div>
            </div>

            {/* Call to Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center pt-8 px-4">
              <Button
                size="lg"
                className="w-full sm:w-auto bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-4 text-base font-semibold shadow-lg hover:shadow-xl hover:scale-105 transition-all duration-300"
                onClick={handleExploreMarketsClick}
              >
                Explore Markets
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="w-full sm:w-auto border-2 px-8 py-4 text-base font-semibold hover:bg-muted/50 hover:scale-105 transition-all duration-300"
                onClick={() => navigate('/insights')}
              >
                View Latest Insights
              </Button>
            </div>

            {/* Animated Scroll Arrow */}
            <div className="flex justify-center mt-8 lg:mt-12">
              <div
                className="flex flex-col items-center cursor-pointer group hover:scale-110 transition-transform duration-300"
                onClick={handleExploreMarketsClick}
              >
                <div className="relative flex flex-col items-center p-3 mt-10 backdrop-blur-sm border border-border/50 group-hover:border-primary/30 group-hover:bg-primary/5 transition-all duration-300 shadow-lg">
                  <ChevronDown
                    className="h-6 w-6 text-muted-foreground group-hover:text-primary transition-all duration-500 drop-shadow-sm"
                    style={{
                      animation: 'bounce 2s infinite',
                      animationDelay: '0s'
                    }}
                  />
                  <ChevronDown
                    className="h-6 w-6 text-muted-foreground/60 group-hover:text-primary/60 transition-all duration-500 -mt-3 drop-shadow-sm"
                    style={{
                      animation: 'bounce 2s infinite',
                      animationDelay: '0.3s'
                    }}
                  />
                </div>
                <span className="text-xs text-muted-foreground group-hover:text-primary transition-colors duration-300 mt-2 font-medium tracking-wide bg-background/80 backdrop-blur-sm px-2 py-1 rounded-full border border-border/30">
                  Scroll to explore
                </span>
              </div>
            </div>
          </div>

          {/* Quarterly Outlook Section */}
          <div ref={quarterlyOutlookRef} className="text-center mb-8 px-4 mt-24 lg:mt-32">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              Quarterly Market Outlook
            </h2>
            <p className="text-base md:text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Comprehensive quarterly analysis and forecasts for US and Indian markets.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 max-w-5xl mx-auto px-4">
            {/* US Market Column */}
            <div className="space-y-3 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
              <div className="flex items-center justify-between mb-3 p-3 bg-gradient-to-r from-blue-500/10 to-blue-500/5 rounded-lg border border-blue-500/20">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 bg-blue-500/20 rounded-full">
                    <Globe className="h-4 w-4 text-blue-500" />
                  </div>
                  <h3 className="text-lg font-bold text-blue-600">US Market</h3>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/markets/us-quarterly')}
                  className="flex items-center gap-2 text-sm border-blue-500/30 hover:bg-blue-500/10 hover:border-blue-500/50 transition-all duration-300"
                >
                  <Eye className="h-4 w-4" />
                  View All
                </Button>
              </div>
              <div className="space-y-3">
                {usMarketData.map((data, index) => (
                  <div
                    key={data.id}
                    className="animate-fade-in-up"
                    style={{ animationDelay: `${0.4 + index * 0.1}s` }}
                  >
                    <QuarterlyOutlookCard
                      data={data}
                      onViewDetails={handleViewDetails}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Indian Market Column */}
            <div className="space-y-3 animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
              <div className="flex items-center justify-between mb-3 p-3 bg-gradient-to-r from-orange-500/10 to-orange-500/5 rounded-lg border border-orange-500/20">
                <div className="flex items-center gap-2">
                  <div className="p-1.5 bg-orange-500/20 rounded-full">
                    <MapPin className="h-4 w-4 text-orange-500" />
                  </div>
                  <h3 className="text-lg font-bold text-orange-600">Indian Market</h3>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate('/markets/indian-quarterly')}
                  className="flex items-center gap-2 text-sm border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50 transition-all duration-300"
                >
                  <Eye className="h-4 w-4" />
                  View All
                </Button>
              </div>
              <div className="space-y-3">
                {indiaMarketData.map((data, index) => (
                  <div
                    key={data.id}
                    className="animate-fade-in-up"
                    style={{ animationDelay: `${0.5 + index * 0.1}s` }}
                  >
                    <QuarterlyOutlookCard
                      data={data}
                      onViewDetails={handleViewDetails}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Detailed View Modal/Overlay */}
          {selectedOutlook && (
            <DetailedOutlookView
              data={selectedOutlook}
              onClose={closeDetails}
            />
          )}
        </div>

        {/* Scroll Arrow Indicator */}
        {/* <ScrollArrow onClick={handleScrollArrowClick} /> */}
      </div>

      {/* Company Info Modal */}
      <CompanyInfoModal
        isOpen={isCompanyInfoOpen}
        onClose={() => setIsCompanyInfoOpen(false)}
      />
    </>
  );
};

const DetailedOutlookView: React.FC<{
  data: QuarterlyOutlookData;
  onClose: () => void;
}> = ({ data, onClose }) => {
  const getOutlookColor = (outlook: string) => {
    switch (outlook) {
      case 'bullish': return 'text-market-up';
      case 'bearish': return 'text-market-down';
      default: return 'text-market-neutral';
    }
  };

  const getOutlookIcon = (outlook: string) => {
    switch (outlook) {
      case 'bullish': return <TrendingUp className="h-5 w-5" />;
      case 'bearish': return <TrendingDown className="h-5 w-5" />;
      default: return <Calendar className="h-5 w-5" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-card rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="sticky top-0 bg-card border-b border-border p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {data.market === 'us' ? (
                <Globe className="h-6 w-6 text-blue-500" />
              ) : (
                <MapPin className="h-6 w-6 text-orange-500" />
              )}
              <h2 className="text-2xl font-bold">{data.title}</h2>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Summary */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Executive Summary</h3>
            <p className="text-muted-foreground">{data.summary}</p>
          </div>

          {/* Key Metrics */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Key Metrics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {data.key_metrics.market_outlook && (
                <Card className="p-4">
                  <div className={`flex items-center gap-2 ${getOutlookColor(data.key_metrics.market_outlook)}`}>
                    {getOutlookIcon(data.key_metrics.market_outlook)}
                    <span className="font-medium">
                      {data.key_metrics.market_outlook.charAt(0).toUpperCase() + data.key_metrics.market_outlook.slice(1)} Outlook
                    </span>
                  </div>
                </Card>
              )}
              {data.key_metrics.gdp_growth && (
                <Card className="p-4">
                  <div className="text-sm text-muted-foreground">GDP Growth</div>
                  <div className="text-2xl font-bold text-market-up">{data.key_metrics.gdp_growth}%</div>
                </Card>
              )}
              {data.key_metrics.inflation_rate && (
                <Card className="p-4">
                  <div className="text-sm text-muted-foreground">Inflation Rate</div>
                  <div className="text-2xl font-bold">{data.key_metrics.inflation_rate}%</div>
                </Card>
              )}
              {data.key_metrics.interest_rate && (
                <Card className="p-4">
                  <div className="text-sm text-muted-foreground">Interest Rate</div>
                  <div className="text-2xl font-bold">{data.key_metrics.interest_rate}%</div>
                </Card>
              )}
            </div>
          </div>

          {/* Sector Highlights */}
          {data.key_metrics.sector_highlights && data.key_metrics.sector_highlights.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-3">Sector Highlights</h3>
              <div className="flex flex-wrap gap-2">
                {data.key_metrics.sector_highlights.map((sector, index) => (
                  <Badge key={index} variant="secondary" className="px-3 py-1">
                    {sector}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Detailed Analysis */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Detailed Analysis</h3>
            <div className="prose prose-sm max-w-none text-muted-foreground">
              <p>{data.detailed_analysis}</p>
            </div>
          </div>

          {/* Risk Factors and Opportunities */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold mb-3 text-market-down">Risk Factors</h3>
              <ul className="space-y-2">
                {data.risk_factors.map((risk, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-market-down mt-1">•</span>
                    <span className="text-sm text-muted-foreground">{risk}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-3 text-market-up">Opportunities</h3>
              <ul className="space-y-2">
                {data.opportunities.map((opportunity, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-market-up mt-1">•</span>
                    <span className="text-sm text-muted-foreground">{opportunity}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuarterlyOutlookSection;
