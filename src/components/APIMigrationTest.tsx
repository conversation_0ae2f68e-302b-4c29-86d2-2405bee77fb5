import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { CheckCircle, XCircle, Clock, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react';

// Import hooks for testing
import { useStockQuote, usePopularStocks, useSymbolSearch } from '@/hooks/useStockData';
import {
  useIndianStockQuote,
  useIndianMarketIndicesWithFallback,
  useBatchIndianStockQuotesWithFallback
} from '@/hooks/useIndianStockData';
import { formatPrice, isIndianStock } from '@/utils/stockUtils';

const APIMigrationTest: React.FC = () => {
  const [testResults, setTestResults] = useState<any>({});

  // Test symbols
  const testSymbols = {
    indian: ['RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS'],
    us: ['AAPL', 'GOOGL', 'MSFT', 'TSLA'],
    mixed: ['RELIANCE.NS', 'AAPL', 'TCS.NS', 'GOOGL'],
  };

  // Test individual Indian stock
  const { data: relianceData, isLoading: relianceLoading, error: relianceError } = 
    useIndianStockQuote('RELIANCE.NS');

  // Test individual US stock
  const { data: appleData, isLoading: appleLoading, error: appleError } = 
    useStockQuote('AAPL');

  // Test Indian market indices
  const { data: indianIndices, isLoading: indicesLoading, error: indicesError } = 
    useIndianMarketIndicesWithFallback();

  // Test batch Indian stocks
  const { data: batchIndianData, isLoading: batchIndianLoading, error: batchIndianError } = 
    useBatchIndianStockQuotesWithFallback(testSymbols.indian);

  // Test popular stocks (mixed)
  const { data: popularData, isLoading: popularLoading, error: popularError } = 
    usePopularStocks();

  // Test unified search
  const { data: searchData, isLoading: searchLoading, error: searchError } =
    useSymbolSearch('REL', true);



  const TestCard = ({ title, status, data, error, loading, details }: any) => {
    const getStatusIcon = () => {
      if (loading) return <Clock className="h-4 w-4 text-yellow-500" />;
      if (error) return <XCircle className="h-4 w-4 text-red-500" />;
      if (data) return <CheckCircle className="h-4 w-4 text-green-500" />;
      return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    };

    const getStatusText = () => {
      if (loading) return 'Loading...';
      if (error) return 'Failed';
      if (data) return 'Success';
      return 'Pending';
    };

    const getStatusColor = () => {
      if (loading) return 'bg-yellow-100 text-yellow-800';
      if (error) return 'bg-red-100 text-red-800';
      if (data) return 'bg-green-100 text-green-800';
      return 'bg-gray-100 text-gray-800';
    };

    return (
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <Badge className={getStatusColor()}>
              <div className="flex items-center gap-1">
                {getStatusIcon()}
                {getStatusText()}
              </div>
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          {details && (
            <div className="text-xs text-gray-600 mb-2">
              {details}
            </div>
          )}
          {error && (
            <div className="text-xs text-red-600 bg-red-50 p-2 rounded">
              Error: {error.message}
            </div>
          )}
          {data && (
            <div className="text-xs text-green-600 bg-green-50 p-2 rounded">
              Data received successfully
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const DataPreview = ({ title, data, formatter }: any) => {
    if (!data) return null;

    return (
      <Card className="mb-4">
        <CardHeader>
          <CardTitle className="text-sm">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-32">
            {formatter ? formatter(data) : JSON.stringify(data, null, 2)}
          </pre>
        </CardContent>
      </Card>
    );
  };

  const formatStockData = (data: any) => {
    if (Array.isArray(data)) {
      return data.slice(0, 3).map(item => ({
        symbol: item.symbol,
        price: item.data?.price || item.data?.c,
        change: item.data?.change || item.data?.d,
      }));
    }
    return {
      symbol: data.symbol,
      price: data.price || data.c,
      change: data.change || data.d,
    };
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">API Migration Test Dashboard</h1>
        <p className="text-gray-600">
          Testing the migration to ensure Indian stocks use Yahoo Finance and US stocks use Finnhub
        </p>
      </div>

      <Tabs defaultValue="individual" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="individual">Individual Stocks</TabsTrigger>
          <TabsTrigger value="batch">Batch Operations</TabsTrigger>
          <TabsTrigger value="search">Search & Indices</TabsTrigger>
          <TabsTrigger value="data">Data Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="individual" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TestCard
              title="Indian Stock (RELIANCE.NS) - Yahoo Finance"
              status={relianceError ? 'error' : relianceData ? 'success' : 'loading'}
              data={relianceData}
              error={relianceError}
              loading={relianceLoading}
              details="Should use Yahoo Finance API for Indian stocks"
            />
            
            <TestCard
              title="US Stock (AAPL) - Finnhub"
              status={appleError ? 'error' : appleData ? 'success' : 'loading'}
              data={appleData}
              error={appleError}
              loading={appleLoading}
              details="Should use Finnhub API for US stocks"
            />
          </div>
        </TabsContent>

        <TabsContent value="batch" className="space-y-4">
          <TestCard
            title="Batch Indian Stocks - Yahoo Finance"
            status={batchIndianError ? 'error' : batchIndianData ? 'success' : 'loading'}
            data={batchIndianData}
            error={batchIndianError}
            loading={batchIndianLoading}
            details={`Testing ${testSymbols.indian.length} Indian stocks via Yahoo Finance`}
          />
          
          <TestCard
            title="Popular Stocks (Mixed) - Unified API"
            status={popularError ? 'error' : popularData ? 'success' : 'loading'}
            data={popularData}
            error={popularError}
            loading={popularLoading}
            details="Should automatically route Indian stocks to Yahoo Finance and US stocks to Finnhub"
          />
        </TabsContent>



        <TabsContent value="search" className="space-y-4">
          <TestCard
            title="Indian Market Indices - Yahoo Finance"
            status={indicesError ? 'error' : indianIndices ? 'success' : 'loading'}
            data={indianIndices}
            error={indicesError}
            loading={indicesLoading}
            details="NIFTY 50, BSE SENSEX, etc. should come from Yahoo Finance"
          />
          
          <TestCard
            title="Unified Search (REL) - Combined APIs"
            status={searchError ? 'error' : searchData ? 'success' : 'loading'}
            data={searchData}
            error={searchError}
            loading={searchLoading}
            details="Should combine results from both Yahoo Finance (Indian) and Finnhub (US)"
          />
        </TabsContent>

        <TabsContent value="data" className="space-y-4">
          <DataPreview
            title="Indian Stock Data (RELIANCE.NS)"
            data={relianceData}
            formatter={formatStockData}
          />
          
          <DataPreview
            title="US Stock Data (AAPL)"
            data={appleData}
            formatter={formatStockData}
          />
          
          <DataPreview
            title="Popular Stocks Data"
            data={popularData}
            formatter={formatStockData}
          />
          
          <DataPreview
            title="Search Results"
            data={searchData}
          />


        </TabsContent>
      </Tabs>

      {/* Summary */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Migration Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-green-600">
                {relianceData && !relianceError ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">Indian Stocks</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {appleData && !appleError ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">US Stocks</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {indianIndices && !indicesError ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">Indian Indices</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {searchData && !searchError ? '✓' : '✗'}
              </div>
              <div className="text-sm text-gray-600">Unified Search</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default APIMigrationTest;
