
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { usePublishedContent } from '@/hooks/useContent';

// Dynamic data will be fetched from Supabase

const InsightCard: React.FC<{ insight: any }> = ({ insight }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Badge variant="outline">{insight.category}</Badge>
          <span className="text-xs text-muted-foreground">
            {formatDate(insight.published_at || insight.created_at)}
          </span>
        </div>
        <CardTitle className="text-xl mt-2">{insight.title}</CardTitle>
        <CardDescription>
          {insight.excerpt || insight.content?.substring(0, 150) + '...'}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="flex flex-wrap gap-2">
          {insight.tags?.map((tag: string, index: number) => (
            <Badge key={index} variant="secondary" className="text-xs">{tag}</Badge>
          ))}
        </div>
      </CardContent>
      <CardFooter className="pt-2">
        <Link to={`/insights/${insight.id}`} className="text-primary text-sm hover:underline">
          Read Analysis →
        </Link>
      </CardFooter>
    </Card>
  );
};

const InsightsSection: React.FC = () => {
  const { data: allInsights, isLoading, error } = usePublishedContent('insights', { limit: 6 });

  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(6)].map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-3 w-20" />
            </div>
            <Skeleton className="h-6 w-full mt-2" />
            <Skeleton className="h-4 w-full mt-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-5 w-14" />
            </div>
          </CardContent>
          <CardFooter>
            <Skeleton className="h-4 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  if (error) {
    return (
      <div className="container py-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Expert Insights</h2>
          <p className="text-muted-foreground">Unable to load insights at the moment. Please try again later.</p>
        </div>
      </div>
    );
  }

  const insights = allInsights || [];

  return (
    <div className="container py-12">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold">Expert Insights</h2>
        <p className="text-muted-foreground mt-2">In-depth analysis and research on market trends</p>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <div className="flex justify-center mb-6">
          <TabsList className="grid grid-cols-3 w-96">
            <TabsTrigger value="all">All Insights</TabsTrigger>
            <TabsTrigger value="us">U.S. Stocks</TabsTrigger>
            <TabsTrigger value="india">Indian Stocks</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="all" className="mt-0">
          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {insights.map((insight) => (
                <InsightCard key={insight.id} insight={insight} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="us" className="mt-0">
          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {insights
                .filter(insight => insight.tags?.includes('us-markets') || insight.category?.toLowerCase().includes('us'))
                .map((insight) => (
                  <InsightCard key={insight.id} insight={insight} />
                ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="india" className="mt-0">
          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {insights
                .filter(insight => insight.tags?.includes('indian-markets') || insight.category?.toLowerCase().includes('india'))
                .map((insight) => (
                  <InsightCard key={insight.id} insight={insight} />
                ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <div className="mt-10 text-center">
        <Link to="/insights" className="text-primary hover:underline">
          View All Insights →
        </Link>
      </div>
    </div>
  );
};

export default InsightsSection;
