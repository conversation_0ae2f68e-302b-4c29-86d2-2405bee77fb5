import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Loader2, Play, CheckCircle, XCircle, AlertTriangle, TrendingUp, TrendingDown } from 'lucide-react';
import { 
  runYahooFinanceIndianTest, 
  quickYahooFinanceTest,
  testSingleIndianStock 
} from '@/utils/testYahooFinanceIndian';

interface TestResult {
  test: string;
  status: 'PASS' | 'FAIL';
  duration: number;
  error?: string;
}

interface TestSummary {
  totalTests: number;
  passed: number;
  failed: number;
  successRate: number;
  avgDuration: number;
  results: TestResult[];
}

const YahooFinanceIndianTest: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestSummary | null>(null);
  const [quickTestResult, setQuickTestResult] = useState<boolean | null>(null);
  const [singleTestResult, setSingleTestResult] = useState<any>(null);

  const runFullTest = async () => {
    setIsRunning(true);
    setTestResults(null);
    
    try {
      const results = await runYahooFinanceIndianTest();
      setTestResults(results);
    } catch (error) {
      console.error('Test failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const runQuickTest = async () => {
    setIsRunning(true);
    setQuickTestResult(null);
    
    try {
      const result = await quickYahooFinanceTest();
      setQuickTestResult(result);
    } catch (error) {
      console.error('Quick test failed:', error);
      setQuickTestResult(false);
    } finally {
      setIsRunning(false);
    }
  };

  const runSingleTest = async () => {
    setIsRunning(true);
    setSingleTestResult(null);
    
    try {
      const result = await testSingleIndianStock('RELIANCE.NS', 'Reliance Industries');
      setSingleTestResult(result);
    } catch (error) {
      console.error('Single test failed:', error);
      setSingleTestResult({ success: false, error: 'Test execution failed' });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: 'PASS' | 'FAIL') => {
    return status === 'PASS' ? (
      <CheckCircle className="h-4 w-4 text-green-600" />
    ) : (
      <XCircle className="h-4 w-4 text-red-600" />
    );
  };

  const getSuccessRateColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600';
    if (rate >= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSuccessRateBadge = (rate: number) => {
    if (rate >= 80) return 'default';
    if (rate >= 50) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Yahoo Finance Indian Stock Data Test</h1>
        <p className="text-muted-foreground">Verify if Yahoo Finance API is working for Indian market data</p>
      </div>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4 flex-wrap">
            <Button onClick={runQuickTest} disabled={isRunning} variant="default">
              {isRunning ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              Quick Test (RELIANCE.NS)
            </Button>
            
            <Button onClick={runSingleTest} disabled={isRunning} variant="outline">
              {isRunning ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              Single Stock Test
            </Button>
            
            <Button onClick={runFullTest} disabled={isRunning} variant="secondary">
              {isRunning ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Play className="h-4 w-4 mr-2" />}
              Full Test Suite
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Test Results */}
      {quickTestResult !== null && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Quick Test Result
              {quickTestResult ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className={`text-lg font-semibold ${quickTestResult ? 'text-green-600' : 'text-red-600'}`}>
              {quickTestResult ? '✅ Yahoo Finance is working for Indian stocks!' : '❌ Yahoo Finance test failed'}
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Check browser console for detailed logs
            </p>
          </CardContent>
        </Card>
      )}

      {/* Single Test Results */}
      {singleTestResult && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Single Stock Test Result
              {singleTestResult.success ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600" />
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {singleTestResult.success ? (
              <div className="space-y-2">
                <div className="text-lg font-semibold">RELIANCE.NS Data Retrieved Successfully</div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Price:</span> ₹{singleTestResult.data?.price?.toFixed(2)}
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="font-medium">Change:</span>
                    {singleTestResult.data?.change >= 0 ? (
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    ) : (
                      <TrendingDown className="h-4 w-4 text-red-600" />
                    )}
                    <span className={singleTestResult.data?.change >= 0 ? 'text-green-600' : 'text-red-600'}>
                      {singleTestResult.data?.changePercent?.toFixed(2)}%
                    </span>
                  </div>
                  <div>
                    <span className="font-medium">Exchange:</span> {singleTestResult.data?.exchange}
                  </div>
                  <div>
                    <span className="font-medium">Market State:</span> {singleTestResult.data?.marketState}
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  Response time: {singleTestResult.duration}ms
                </div>
              </div>
            ) : (
              <div className="text-red-600">
                <div className="font-semibold">Test Failed</div>
                <div className="text-sm">{singleTestResult.error}</div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Full Test Results */}
      {testResults && (
        <div className="space-y-4">
          {/* Summary Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                Test Summary
                <Badge variant={getSuccessRateBadge(testResults.successRate)}>
                  {testResults.successRate.toFixed(1)}% Success Rate
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{testResults.totalTests}</div>
                  <div className="text-sm text-muted-foreground">Total Tests</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{testResults.passed}</div>
                  <div className="text-sm text-muted-foreground">Passed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{testResults.failed}</div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{testResults.avgDuration.toFixed(0)}ms</div>
                  <div className="text-sm text-muted-foreground">Avg Duration</div>
                </div>
              </div>
              
              <div className={`mt-4 p-4 rounded-lg ${
                testResults.successRate >= 80 ? 'bg-green-50 border border-green-200' :
                testResults.successRate >= 50 ? 'bg-yellow-50 border border-yellow-200' :
                'bg-red-50 border border-red-200'
              }`}>
                <div className={`font-semibold ${getSuccessRateColor(testResults.successRate)}`}>
                  {testResults.successRate >= 80 ? '✅ Yahoo Finance is working well for Indian stocks' :
                   testResults.successRate >= 50 ? '⚠️ Yahoo Finance has some issues with Indian stocks' :
                   '❌ Yahoo Finance is failing for Indian stocks'}
                </div>
                <div className="text-sm mt-1">
                  {testResults.successRate >= 80 ? 'Recommendation: Keep current setup' :
                   testResults.successRate >= 50 ? 'Recommendation: Monitor closely, consider alternatives' :
                   'Recommendation: Find alternative data source urgently'}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Detailed Results */}
          <Card>
            <CardHeader>
              <CardTitle>Detailed Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {testResults.results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result.status)}
                      <span className="font-medium">{result.test}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>{result.duration}ms</span>
                      {result.error && (
                        <AlertTriangle className="h-4 w-4 text-yellow-600" title={result.error} />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Use</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p className="text-sm text-muted-foreground">
            <strong>Quick Test:</strong> Tests a single stock (RELIANCE.NS) to verify basic functionality
          </p>
          <p className="text-sm text-muted-foreground">
            <strong>Single Stock Test:</strong> Detailed test of one stock with data quality checks
          </p>
          <p className="text-sm text-muted-foreground">
            <strong>Full Test Suite:</strong> Comprehensive test of multiple stocks and market indices
          </p>
          <p className="text-sm text-muted-foreground">
            <strong>Console Logs:</strong> Check browser console (F12) for detailed API response logs
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

export default YahooFinanceIndianTest;
