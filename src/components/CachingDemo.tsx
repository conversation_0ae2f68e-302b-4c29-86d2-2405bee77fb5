import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Loader2, RefreshCw, Clock, Database } from 'lucide-react';
import { useCompanyProfile, useCompanyFundamentals } from '@/hooks/useStockData';

export const CachingDemo: React.FC = () => {
  const [symbol, setSymbol] = useState('AAPL');
  const [testSymbol, setTestSymbol] = useState('AAPL');

  // Using the enhanced company profile hook
  const {
    data: profileData,
    isLoading: profileLoading,
    isError: profileError,
    isFetching: profileFetching,
    refetch: refetchProfile,
  } = useCompanyProfile(testSymbol);

  // Using the new company fundamentals hook with maximum caching
  const {
    data: fundamentalsData,
    isLoading: fundamentalsLoading,
    isError: fundamentalsError,
    isFetching: fundamentalsFetching,
    refetch: refetchFundamentals,
    companyName,
    sector,
    country,
    exchange,
    currency,
    marketCap,
  } = useCompanyFundamentals(testSymbol);

  const handleSearch = () => {
    setTestSymbol(symbol.toUpperCase());
  };

  const formatMarketCap = (marketCap: number | undefined) => {
    if (!marketCap) return 'N/A';
    if (marketCap >= 1e12) return `$${(marketCap / 1e12).toFixed(2)}T`;
    if (marketCap >= 1e9) return `$${(marketCap / 1e9).toFixed(2)}B`;
    if (marketCap >= 1e6) return `$${(marketCap / 1e6).toFixed(2)}M`;
    return `$${marketCap.toLocaleString()}`;
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Caching Demo - Non-Real-time Data
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-3 mb-4">
            <div className="relative flex-1">
              <Input
                value={symbol}
                onChange={(e) => setSymbol(e.target.value)}
                placeholder="Enter stock symbol (e.g., AAPL, GOOGL)"
                className="pr-4 h-10 bg-background/50 border border-border/50 rounded-lg
                           focus:border-primary/50 focus:bg-background transition-all duration-200
                           placeholder:text-muted-foreground/70 shadow-sm hover:shadow-md focus:shadow-lg"
                onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              />
              {symbol && (
                <button
                  onClick={() => setSymbol('')}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-muted-foreground
                             hover:text-foreground transition-colors duration-200"
                >
                  <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
            <Button
              onClick={handleSearch}
              className="px-6 h-10 rounded-lg font-medium shadow-sm hover:shadow-md transition-all duration-200"
            >
              Search
            </Button>
          </div>
          
          <div className="text-sm text-muted-foreground mb-4">
            <p>This demo shows enhanced caching for company data that doesn't change frequently.</p>
            <p>Company Profile: 1 hour stale time, 90 minutes cache time</p>
            <p>Company Fundamentals: Maximum caching with additional optimizations</p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Company Profile Hook */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Company Profile Hook</span>
              <div className="flex items-center gap-2">
                {profileFetching && <Loader2 className="h-4 w-4 animate-spin" />}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchProfile()}
                  disabled={profileFetching}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex gap-2">
                <Badge variant={profileLoading ? "secondary" : "default"}>
                  {profileLoading ? "Loading" : "Loaded"}
                </Badge>
                <Badge variant={profileError ? "destructive" : "default"}>
                  {profileError ? "Error" : "Success"}
                </Badge>
                <Badge variant={profileFetching ? "secondary" : "outline"}>
                  {profileFetching ? "Fetching" : "Cached"}
                </Badge>
              </div>
              
              {profileData && (
                <div className="space-y-2 text-sm">
                  <p><strong>Name:</strong> {profileData.name}</p>
                  <p><strong>Exchange:</strong> {profileData.exchange}</p>
                  <p><strong>Country:</strong> {profileData.country}</p>
                  <p><strong>Industry:</strong> {profileData.finnhubIndustry}</p>
                  <p><strong>Market Cap:</strong> {formatMarketCap(profileData.marketCapitalization)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Company Fundamentals Hook */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Company Fundamentals Hook</span>
              <div className="flex items-center gap-2">
                {fundamentalsFetching && <Loader2 className="h-4 w-4 animate-spin" />}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchFundamentals()}
                  disabled={fundamentalsFetching}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex gap-2">
                <Badge variant={fundamentalsLoading ? "secondary" : "default"}>
                  {fundamentalsLoading ? "Loading" : "Loaded"}
                </Badge>
                <Badge variant={fundamentalsError ? "destructive" : "default"}>
                  {fundamentalsError ? "Error" : "Success"}
                </Badge>
                <Badge variant={fundamentalsFetching ? "secondary" : "outline"}>
                  {fundamentalsFetching ? "Fetching" : "Cached"}
                </Badge>
              </div>
              
              {fundamentalsData && (
                <div className="space-y-2 text-sm">
                  <p><strong>Company:</strong> {companyName}</p>
                  <p><strong>Sector:</strong> {sector}</p>
                  <p><strong>Location:</strong> {country}</p>
                  <p><strong>Exchange:</strong> {exchange}</p>
                  <p><strong>Currency:</strong> {currency}</p>
                  <p><strong>Market Cap:</strong> {formatMarketCap(marketCap)}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Caching Benefits
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p>• <strong>Reduced API Calls:</strong> Data is cached for 1-1.5 hours, minimizing unnecessary requests</p>
            <p>• <strong>Better Performance:</strong> Subsequent requests return instantly from cache</p>
            <p>• <strong>Cost Efficiency:</strong> Fewer API calls mean lower costs for rate-limited APIs</p>
            <p>• <strong>Improved UX:</strong> Instant loading for cached data with proper loading states</p>
            <p>• <strong>Smart Invalidation:</strong> Data automatically refreshes when stale</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CachingDemo;
