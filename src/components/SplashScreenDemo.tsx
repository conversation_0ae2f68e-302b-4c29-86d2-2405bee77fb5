import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import SplashScreen from '@/components/SplashScreen';

const SplashScreenDemo: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string | null>(null);

  const demos = [
    {
      id: 'short',
      name: 'Short Duration',
      description: 'Quick 2-second splash screen',
      duration: 2000
    },
    {
      id: 'default',
      name: 'Default Duration',
      description: 'Standard 3-second splash screen',
      duration: 3000
    },
    {
      id: 'long',
      name: 'Long Duration',
      description: 'Extended 5-second splash screen',
      duration: 5000
    }
  ];

  const handleDemoComplete = () => {
    setActiveDemo(null);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-4">Splash Screen Demo</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Explore different splash screen configurations for the Syed's Investments platform. 
          Each theme demonstrates different visual styles and customization options.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {demos.map((demo) => (
          <Card key={demo.id} className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {demo.name}
                <Button
                  onClick={() => setActiveDemo(demo.id)}
                  size="sm"
                  variant="outline"
                >
                  Preview
                </Button>
              </CardTitle>
              <CardDescription>{demo.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Duration:</span>
                  <span>{demo.duration}ms</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Company:</span>
                  <span>Syed's Investments</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Tagline:</span>
                  <span className="text-right">Where Vision Meets Valuation</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Configuration Details */}
      <div className="mt-12">
        <Card>
          <CardHeader>
            <CardTitle>Configuration Options</CardTitle>
            <CardDescription>
              The splash screen component supports extensive customization through configuration objects.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Display Settings</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Duration and timing controls</li>
                  <li>• Skip on revisit option</li>
                  <li>• Minimum display time</li>
                  <li>• Animation delays</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Visual Customization</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Gradient color schemes</li>
                  <li>• Logo size configuration</li>
                  <li>• Custom text content</li>
                  <li>• Animation effects</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Active Demo */}
      {activeDemo && (
        <SplashScreen
          onComplete={handleDemoComplete}
          duration={demos.find(d => d.id === activeDemo)?.duration || 3000}
        />
      )}
    </div>
  );
};

export default SplashScreenDemo;
