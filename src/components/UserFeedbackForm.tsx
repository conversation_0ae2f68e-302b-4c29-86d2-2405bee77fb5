import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Star, Send, CheckCircle, AlertCircle, MessageSquare } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useSubmitFeedback, FeedbackSubmission } from '@/hooks/useFeedback';

type FeedbackFormData = FeedbackSubmission;

interface UserFeedbackFormProps {
  onSuccess?: () => void;
  className?: string;
}

const CATEGORIES = [
  { value: 'general', label: 'General Inquiry', icon: '💬' },
  { value: 'content', label: 'Content Feedback', icon: '📝' },
  { value: 'technical', label: 'Technical Issue', icon: '🔧' },
  { value: 'suggestion', label: 'Suggestion', icon: '💡' },
  { value: 'complaint', label: 'Complaint', icon: '⚠️' },
  { value: 'compliment', label: 'Compliment', icon: '👏' },
] as const;

const UserFeedbackForm: React.FC<UserFeedbackFormProps> = ({ onSuccess, className = '' }) => {
  const [formData, setFormData] = useState<FeedbackFormData>({
    name: '',
    email: '',
    subject: '',
    category: 'general',
    message: '',
    rating: null,
  });

  const [errors, setErrors] = useState<Partial<FeedbackFormData>>({});
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [hoveredRating, setHoveredRating] = useState<number | null>(null);

  const submitFeedbackMutation = useSubmitFeedback();

  const validateForm = (): boolean => {
    const newErrors: Partial<FeedbackFormData> = {};

    // Required field validation
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.subject.trim()) {
      newErrors.subject = 'Subject is required';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'Message is required';
    } else if (formData.message.trim().length < 10) {
      newErrors.message = 'Message must be at least 10 characters long';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setSubmitStatus('idle');

    try {
      await submitFeedbackMutation.mutateAsync(formData);

      setSubmitStatus('success');
      toast({
        title: "Feedback Submitted Successfully!",
        description: "Thank you for your feedback. We'll review it and get back to you soon.",
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        subject: '',
        category: 'general',
        message: '',
        rating: null,
      });
      setErrors({});

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
      setSubmitStatus('error');
      toast({
        title: "Submission Failed",
        description: error instanceof Error ? error.message : "There was an error submitting your feedback. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (field: keyof FeedbackFormData, value: string | number | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const renderStarRating = () => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            className={`p-1 transition-colors ${
              (hoveredRating || formData.rating || 0) >= star
                ? 'text-yellow-400'
                : 'text-gray-300 hover:text-yellow-300'
            }`}
            onMouseEnter={() => setHoveredRating(star)}
            onMouseLeave={() => setHoveredRating(null)}
            onClick={() => handleInputChange('rating', star)}
          >
            <Star className="h-5 w-5 fill-current" />
          </button>
        ))}
        {formData.rating && (
          <span className="ml-2 text-sm text-muted-foreground">
            {formData.rating} out of 5 stars
          </span>
        )}
      </div>
    );
  };

  const selectedCategory = CATEGORIES.find(cat => cat.value === formData.category);

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader>
        <div className="flex items-center space-x-2">
          <MessageSquare className="h-6 w-6 text-primary" />
          <CardTitle>Share Your Feedback</CardTitle>
        </div>
        <CardDescription>
          We value your opinion! Help us improve by sharing your thoughts, suggestions, or reporting any issues.
        </CardDescription>
      </CardHeader>
      <CardContent>
        {submitStatus === 'success' && (
          <Alert className="mb-6 border-green-200 bg-green-50 text-green-800">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Thank you for your feedback! We've received your message and will review it shortly.
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name and Email Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Your full name"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-600 mt-1">{errors.name}</p>
              )}
            </div>

            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                className={errors.email ? 'border-red-500' : ''}
              />
              {errors.email && (
                <p className="text-sm text-red-600 mt-1">{errors.email}</p>
              )}
            </div>
          </div>

          {/* Category Selection */}
          <div>
            <Label htmlFor="category">Category</Label>
            <Select
              value={formData.category}
              onValueChange={(value) => handleInputChange('category', value)}
            >
              <SelectTrigger>
                <SelectValue>
                  {selectedCategory && (
                    <div className="flex items-center space-x-2">
                      <span>{selectedCategory.icon}</span>
                      <span>{selectedCategory.label}</span>
                    </div>
                  )}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {CATEGORIES.map((category) => (
                  <SelectItem key={category.value} value={category.value}>
                    <div className="flex items-center space-x-2">
                      <span>{category.icon}</span>
                      <span>{category.label}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Subject */}
          <div>
            <Label htmlFor="subject">Subject *</Label>
            <Input
              id="subject"
              type="text"
              value={formData.subject}
              onChange={(e) => handleInputChange('subject', e.target.value)}
              placeholder="Brief description of your feedback"
              className={errors.subject ? 'border-red-500' : ''}
            />
            {errors.subject && (
              <p className="text-sm text-red-600 mt-1">{errors.subject}</p>
            )}
          </div>

          {/* Rating */}
          <div>
            <Label>Overall Rating (Optional)</Label>
            <div className="mt-2">
              {renderStarRating()}
            </div>
            <p className="text-sm text-muted-foreground mt-1">
              Rate your overall experience with our website
            </p>
          </div>

          {/* Message */}
          <div>
            <Label htmlFor="message">Message *</Label>
            <Textarea
              id="message"
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              placeholder="Please share your detailed feedback, suggestions, or describe any issues you've encountered..."
              rows={6}
              className={errors.message ? 'border-red-500' : ''}
            />
            {errors.message && (
              <p className="text-sm text-red-600 mt-1">{errors.message}</p>
            )}
            <p className="text-sm text-muted-foreground mt-1">
              {formData.message.length}/1000 characters
            </p>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={submitFeedbackMutation.isPending}
              className="flex items-center space-x-2"
            >
              {submitFeedbackMutation.isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Submitting...</span>
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  <span>Submit Feedback</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default UserFeedbackForm;
