
import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { usePublishedContent } from '@/hooks/useContent';

// Dynamic data will be fetched from Supabase

const NewsCard: React.FC<{ news: any }> = ({ news }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <Badge variant="outline">{news.category}</Badge>
          <span className="text-xs text-muted-foreground">
            {formatDate(news.published_at || news.created_at)}
          </span>
        </div>
        <CardTitle className="text-xl mt-2">{news.title}</CardTitle>
      </CardHeader>
      <CardContent className="flex-grow">
        <p className="text-muted-foreground">
          {news.excerpt || news.content?.substring(0, 150) + '...'}
        </p>
      </CardContent>
      <CardFooter className="flex justify-between items-center pt-4 border-t border-border">
        <span className="text-xs text-muted-foreground">
          {news.author && `By ${news.author}`}
        </span>
        <Link to={`/news/${news.id}`} className="text-primary text-sm hover:underline">
          Read More →
        </Link>
      </CardFooter>
    </Card>
  );
};

const NewsSection: React.FC = () => {
  const { data: allNews, isLoading, error } = usePublishedContent('news', { limit: 6 });

  const LoadingSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(6)].map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-16" />
              <Skeleton className="h-3 w-20" />
            </div>
            <Skeleton className="h-6 w-full mt-2" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </CardContent>
          <CardFooter>
            <Skeleton className="h-3 w-24" />
          </CardFooter>
        </Card>
      ))}
    </div>
  );

  if (error) {
    return (
      <div className="container py-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold mb-4">Market News</h2>
          <p className="text-muted-foreground">Unable to load news at the moment. Please try again later.</p>
        </div>
      </div>
    );
  }

  const newsItems = allNews || [];

  return (
    <div className="container py-12">
      <div className="text-center mb-10">
        <h2 className="text-3xl font-bold">Market News</h2>
        <p className="text-muted-foreground mt-2">Curated economic and market updates from trusted sources</p>
      </div>

      <Tabs defaultValue="all" className="w-full">
        <div className="flex justify-center mb-6">
          <TabsList className="grid grid-cols-3 w-96">
            <TabsTrigger value="all">All News</TabsTrigger>
            <TabsTrigger value="us">U.S. Markets</TabsTrigger>
            <TabsTrigger value="india">Indian Markets</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="all" className="mt-0">
          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {newsItems.map((news) => (
                <NewsCard key={news.id} news={news} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="us" className="mt-0">
          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {newsItems
                .filter(news => news.tags?.includes('us-markets') || news.category?.toLowerCase().includes('us'))
                .map((news) => (
                  <NewsCard key={news.id} news={news} />
                ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="india" className="mt-0">
          {isLoading ? (
            <LoadingSkeleton />
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {newsItems
                .filter(news => news.tags?.includes('indian-markets') || news.category?.toLowerCase().includes('india'))
                .map((news) => (
                  <NewsCard key={news.id} news={news} />
                ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      <div className="mt-10 text-center">
        <Link to="/news" className="text-primary hover:underline">
          View All News →
        </Link>
      </div>
    </div>
  );
};

export default NewsSection;
