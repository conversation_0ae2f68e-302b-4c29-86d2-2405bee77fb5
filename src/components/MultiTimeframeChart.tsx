import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  createSampleChartData,
  generateYahooFinanceUrl,
  YAHOO_FINANCE_RANGES,
  type TransformedChartData 
} from '@/utils/yahooFinanceDataTransformer';

const MultiTimeframeChart: React.FC = () => {
  const [selectedRange, setSelectedRange] = useState<keyof typeof YAHOO_FINANCE_RANGES>('5y');
  const [chartData, setChartData] = useState<TransformedChartData | null>(null);
  const [loading, setLoading] = useState(false);

  // Load data for selected timeframe
  const loadData = async (range: keyof typeof YAHOO_FINANCE_RANGES) => {
    setLoading(true);
    
    try {
      // In a real implementation, you would fetch from Yahoo Finance API
      // const url = generateYahooFinanceUrl('BHARTIARTL.NS', range);
      // const response = await fetch(url);
      // const data = await response.json();
      // const transformed = transformYahooFinanceData(data);
      
      // For demo, we'll use sample data
      const sampleData = createSampleChartData(range);
      setChartData(sampleData);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData(selectedRange);
  }, [selectedRange]);

  // Custom tooltip for different timeframes
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      const isLongTerm = ['5y', '10y', 'max'].includes(selectedRange);
      
      return (
        <div className="bg-background border rounded-lg p-3 shadow-lg">
          <p className="font-medium">{data.date}</p>
          {!isLongTerm && <p className="text-sm text-muted-foreground">{data.time}</p>}
          <div className="mt-2 space-y-1">
            <p className="text-sm">Open: <span className="font-medium">₹{data.open}</span></p>
            <p className="text-sm">High: <span className="font-medium text-green-600">₹{data.high}</span></p>
            <p className="text-sm">Low: <span className="font-medium text-red-600">₹{data.low}</span></p>
            <p className="text-sm">Close: <span className="font-medium">₹{data.close}</span></p>
            <p className="text-sm">Volume: <span className="font-medium">{data.volume.toLocaleString()}</span></p>
          </div>
        </div>
      );
    }
    return null;
  };

  const timeframeButtons = [
    { key: '1d', label: '1D' },
    { key: '5d', label: '5D' },
    { key: '1mo', label: '1M' },
    { key: '3mo', label: '3M' },
    { key: '6mo', label: '6M' },
    { key: '1y', label: '1Y' },
    { key: '2y', label: '2Y' },
    { key: '5y', label: '5Y' },
    { key: '10y', label: '10Y' },
    { key: 'max', label: 'MAX' }
  ] as const;

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Multi-Timeframe Stock Charts</h1>
        <p className="text-muted-foreground">
          Demonstrating Yahoo Finance API support for 5-year and long-term historical data
        </p>
      </div>

      {/* Timeframe Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Timeframe</CardTitle>
          <CardDescription>
            Yahoo Finance supports all these time ranges including 5-year historical data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {timeframeButtons.map(({ key, label }) => (
              <Button
                key={key}
                variant={selectedRange === key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedRange(key)}
                disabled={loading}
              >
                {label}
              </Button>
            ))}
          </div>
          
          <div className="mt-4 p-4 bg-muted rounded-lg">
            <h4 className="font-medium mb-2">Current Selection: {YAHOO_FINANCE_RANGES[selectedRange].label}</h4>
            <p className="text-sm text-muted-foreground">
              <strong>API URL:</strong> {generateYahooFinanceUrl('BHARTIARTL.NS', selectedRange)}
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              <strong>Range:</strong> {YAHOO_FINANCE_RANGES[selectedRange].range} | 
              <strong> Interval:</strong> {YAHOO_FINANCE_RANGES[selectedRange].interval}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Chart Display */}
      {chartData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              Bharti Airtel Limited
              <Badge variant="secondary">BHARTIARTL.NS</Badge>
              <Badge variant="outline">{YAHOO_FINANCE_RANGES[selectedRange].label}</Badge>
            </CardTitle>
            <CardDescription>
              {chartData.summary.totalPoints} data points | {chartData.summary.timeRange}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="h-96 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                  <p className="text-muted-foreground">Loading {YAHOO_FINANCE_RANGES[selectedRange].label} data...</p>
                </div>
              </div>
            ) : (
              <ResponsiveContainer width="100%" height={400}>
                <AreaChart data={chartData.data}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date"
                    tick={{ fontSize: 12 }}
                    interval="preserveStartEnd"
                  />
                  <YAxis 
                    domain={['dataMin - 10', 'dataMax + 10']}
                    tick={{ fontSize: 12 }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area 
                    type="monotone" 
                    dataKey="close" 
                    stroke="#3b82f6" 
                    fill="#3b82f6" 
                    fillOpacity={0.1}
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            )}
          </CardContent>
        </Card>
      )}

      {/* 5-Year Data Information */}
      <Card className="border-green-200">
        <CardHeader>
          <CardTitle className="text-green-700">✅ 5-Year Data Support</CardTitle>
          <CardDescription>
            Yahoo Finance API fully supports 5-year historical data and more
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Supported Time Ranges</h4>
              <ul className="text-sm space-y-1">
                <li>• <strong>Short-term:</strong> 1d, 5d (intraday data)</li>
                <li>• <strong>Medium-term:</strong> 1mo, 3mo, 6mo</li>
                <li>• <strong>Long-term:</strong> 1y, 2y, <strong className="text-green-600">5y</strong>, 10y</li>
                <li>• <strong>Maximum:</strong> All available history</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Data Intervals</h4>
              <ul className="text-sm space-y-1">
                <li>• <strong>5-Year:</strong> Weekly intervals (1wk)</li>
                <li>• <strong>Data Points:</strong> ~260 weeks</li>
                <li>• <strong>File Size:</strong> Optimized for performance</li>
                <li>• <strong>Update Frequency:</strong> Real-time</li>
              </ul>
            </div>
          </div>
          
          <div className="p-4 bg-green-50 rounded-lg">
            <h4 className="font-medium text-green-800 mb-2">Implementation Example</h4>
            <pre className="text-sm text-green-700 overflow-x-auto">
{`// Get 5-year data for Indian stock
const url = generateYahooFinanceUrl('BHARTIARTL.NS', '5y');
// Result: /v8/finance/chart/BHARTIARTL.NS?range=5y&interval=1wk

// Fetch and transform
const response = await fetch(url);
const data = await response.json();
const chartData = transformYahooFinanceData(data);

// chartData will contain ~260 weekly data points over 5 years`}
            </pre>
          </div>
        </CardContent>
      </Card>

      {/* API Endpoints Reference */}
      <Card>
        <CardHeader>
          <CardTitle>API Endpoints Reference</CardTitle>
          <CardDescription>
            Complete Yahoo Finance API endpoints for all timeframes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm font-mono">
            {Object.entries(YAHOO_FINANCE_RANGES).map(([key, config]) => (
              <div key={key} className="flex justify-between items-center p-2 bg-muted rounded">
                <span className="font-medium">{config.label}:</span>
                <span className="text-blue-600">
                  ?range={config.range}&interval={config.interval}
                </span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MultiTimeframeChart;
