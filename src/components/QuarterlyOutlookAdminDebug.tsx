import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, Database, AlertCircle, CheckCircle, Plus } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface QuarterlyOutlookData {
  id: string;
  title: string;
  quarter: string;
  year: number;
  market: string;
  status: string;
  created_at: string;
  author_id: string;
}

const QuarterlyOutlookAdminDebug: React.FC = () => {
  const { user } = useAuth();
  const [outlookData, setOutlookData] = useState<QuarterlyOutlookData[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log('Fetching quarterly outlook data...');
      console.log('Current user:', user);
      
      // Check authentication status
      const { data: { session } } = await supabase.auth.getSession();
      console.log('Current session:', session);

      // Test simple query first
      const { data: allData, error: allError } = await supabase
        .from('quarterly_outlook')
        .select('*');

      console.log('All quarterly outlook data (no filters):', { allData, allError });

      if (allError) {
        console.error('Error fetching quarterly outlook data:', allError);
        setError(`Database error: ${allError.message}`);
        return;
      }

      setOutlookData(allData || []);

    } catch (err: any) {
      console.error('Unexpected error:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const createSampleData = async () => {
    if (!user) {
      setError('You must be logged in to create sample data');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const sampleData = [
        {
          title: 'Q4 2024 US Market Outlook',
          quarter: 'Q4',
          year: 2024,
          market: 'us',
          summary: 'Strong economic fundamentals with cautious optimism amid global uncertainties.',
          key_metrics: {
            gdp_growth: 2.8,
            inflation_rate: 3.2,
            interest_rate: 5.25,
            market_outlook: 'bullish',
            sector_highlights: ['Technology', 'Healthcare', 'Financial Services']
          },
          detailed_analysis: 'The US market shows resilience with strong corporate earnings and consumer spending. Technology sector continues to lead with AI innovations, while healthcare remains stable. Financial services benefit from higher interest rates.',
          risk_factors: ['Geopolitical tensions', 'Inflation concerns', 'Supply chain disruptions'],
          opportunities: ['AI and Technology growth', 'Infrastructure investments', 'Green energy transition'],
          status: 'published',
          author_id: user.id,
        },
        {
          title: 'Q1 2025 US Market Outlook',
          quarter: 'Q1',
          year: 2025,
          market: 'us',
          summary: 'Continued growth momentum with focus on emerging technologies and sustainable investments.',
          key_metrics: {
            gdp_growth: 3.1,
            inflation_rate: 2.9,
            interest_rate: 5.0,
            market_outlook: 'bullish',
            sector_highlights: ['Artificial Intelligence', 'Renewable Energy', 'Biotechnology']
          },
          detailed_analysis: 'Q1 2025 presents opportunities in emerging sectors with strong fundamentals. AI adoption accelerates across industries, renewable energy investments surge, and biotechnology shows promising developments.',
          risk_factors: ['Market volatility', 'Regulatory changes', 'Global economic slowdown'],
          opportunities: ['Tech innovation', 'ESG investments', 'Healthcare advancements'],
          status: 'published',
          author_id: user.id,
        },
        {
          title: 'Q4 2024 Indian Market Outlook',
          quarter: 'Q4',
          year: 2024,
          market: 'india',
          summary: 'Robust domestic demand driving growth with strong manufacturing and services sectors.',
          key_metrics: {
            gdp_growth: 6.8,
            inflation_rate: 4.5,
            interest_rate: 6.5,
            market_outlook: 'bullish',
            sector_highlights: ['Manufacturing', 'IT Services', 'Financial Services']
          },
          detailed_analysis: 'India\'s economy continues to show strong fundamentals with domestic consumption driving growth. Manufacturing sector benefits from government initiatives, IT services remain globally competitive.',
          risk_factors: ['Monsoon dependency', 'Global trade tensions', 'Commodity price volatility'],
          opportunities: ['Digital transformation', 'Infrastructure development', 'Manufacturing growth'],
          status: 'published',
          author_id: user.id,
        },
        {
          title: 'Q1 2025 Indian Market Outlook',
          quarter: 'Q1',
          year: 2025,
          market: 'india',
          summary: 'Sustained growth trajectory with focus on digital economy and sustainable development.',
          key_metrics: {
            gdp_growth: 7.2,
            inflation_rate: 4.2,
            interest_rate: 6.25,
            market_outlook: 'bullish',
            sector_highlights: ['Digital Economy', 'Green Energy', 'Healthcare']
          },
          detailed_analysis: 'Q1 2025 outlook remains positive with strong policy support and investment flows. Digital economy initiatives gain momentum, green energy sector expands rapidly.',
          risk_factors: ['External headwinds', 'Fiscal deficit concerns', 'Climate risks'],
          opportunities: ['Digital India initiatives', 'Renewable energy expansion', 'Healthcare innovation'],
          status: 'published',
          author_id: user.id,
        }
      ];

      const { data, error } = await supabase
        .from('quarterly_outlook')
        .insert(sampleData)
        .select();

      if (error) {
        console.error('Error creating sample data:', error);
        setError(`Error creating sample data: ${error.message}`);
      } else {
        console.log('Created sample data:', data);
        // Refresh the data
        await fetchData();
      }
    } catch (err: any) {
      console.error('Unexpected error creating sample data:', err);
      setError(`Unexpected error: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Quarterly Outlook Admin Debugger
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={fetchData} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh Data
            </Button>
            {outlookData.length === 0 && user && (
              <Button onClick={createSampleData} disabled={loading} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Create Sample Data
              </Button>
            )}
          </div>

          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-red-600" />
              <span className="text-red-800">{error}</span>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Database Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    {user ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span>User Authentication: {user ? 'Logged in' : 'Not logged in'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {outlookData.length > 0 ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <AlertCircle className="h-4 w-4 text-orange-600" />
                    )}
                    <span>Quarterly Outlook Records: {outlookData.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Quarterly Outlook Data ({outlookData.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {outlookData.length > 0 ? (
                    outlookData.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-2 border rounded">
                        <div>
                          <div className="font-medium">{item.title}</div>
                          <div className="text-sm text-gray-600">{item.quarter} {item.year} - {item.market.toUpperCase()}</div>
                        </div>
                        <Badge variant={item.status === 'published' ? 'default' : 'secondary'}>
                          {item.status}
                        </Badge>
                      </div>
                    ))
                  ) : (
                    <div className="text-gray-500">No quarterly outlook data found</div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuarterlyOutlookAdminDebug;
