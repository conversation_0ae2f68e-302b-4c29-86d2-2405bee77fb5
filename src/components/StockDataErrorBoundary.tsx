import React, { Component, ReactNode } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, TrendingDown } from 'lucide-react';
import { getDataSource, getAPIErrorMessage } from '@/utils/stockUtils';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  symbol?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: any;
}

class StockDataErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Stock Data Error Boundary caught an error:', error, errorInfo);
    
    // Log to external service if needed
    if (this.props.symbol) {
      const dataSource = getDataSource(this.props.symbol);
      console.error(`Error in ${dataSource} API for symbol ${this.props.symbol}:`, error);
    }

    this.setState({
      error,
      errorInfo,
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
    
    // Force a page refresh as a last resort
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { symbol } = this.props;
      const { error } = this.state;
      const dataSource = symbol ? getDataSource(symbol) : 'API';
      const apiName = dataSource === 'yahoo' ? 'Yahoo Finance' : 'Finnhub';
      const errorMessage = symbol && error ? getAPIErrorMessage(error, symbol) : 'An unexpected error occurred';

      return (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="h-5 w-5" />
              Stock Data Error
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm text-red-600">
              <p className="font-medium mb-2">
                {errorMessage}
              </p>
              {symbol && (
                <p className="text-xs text-red-500">
                  Symbol: {symbol} | Source: {apiName}
                </p>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={this.handleRetry}
                className="border-red-300 text-red-700 hover:bg-red-100"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.location.href = '/'}
                className="text-red-600 hover:bg-red-100"
              >
                <TrendingDown className="h-4 w-4 mr-2" />
                Go to Dashboard
              </Button>
            </div>

            {process.env.NODE_ENV === 'development' && error && (
              <details className="mt-4">
                <summary className="text-xs text-red-500 cursor-pointer">
                  Error Details (Development)
                </summary>
                <pre className="mt-2 text-xs text-red-600 bg-red-100 p-2 rounded overflow-auto max-h-32">
                  {error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </details>
            )}
          </CardContent>
        </Card>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for wrapping stock-related components
export const withStockErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  symbol?: string
) => {
  return React.forwardRef<any, P>((props, ref) => (
    <StockDataErrorBoundary symbol={symbol}>
      <Component {...props} ref={ref} />
    </StockDataErrorBoundary>
  ));
};

// Hook for handling API errors consistently
export const useStockErrorHandler = (symbol?: string) => {
  const handleError = React.useCallback((error: any) => {
    const errorMessage = symbol ? getAPIErrorMessage(error, symbol) : 'An error occurred';
    console.error('Stock data error:', errorMessage, error);
    
    // You can extend this to send errors to monitoring service
    return errorMessage;
  }, [symbol]);

  return { handleError };
};

export default StockDataErrorBoundary;
