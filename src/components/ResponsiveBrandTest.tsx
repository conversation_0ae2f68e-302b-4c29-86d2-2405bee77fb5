import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Monitor, Tablet, Smartphone, Maximize } from 'lucide-react';

const ResponsiveBrandTest: React.FC = () => {
  const [selectedView, setSelectedView] = useState<'mobile' | 'tablet' | 'desktop' | 'large'>('desktop');

  const viewports = [
    { 
      key: 'mobile' as const, 
      label: 'Mobile', 
      icon: <Smartphone className="h-4 w-4" />, 
      width: '375px',
      description: '375px - iPhone/Android'
    },
    { 
      key: 'tablet' as const, 
      label: 'Tablet', 
      icon: <Tablet className="h-4 w-4" />, 
      width: '768px',
      description: '768px - iPad/Tablet'
    },
    { 
      key: 'desktop' as const, 
      label: 'Desktop', 
      icon: <Monitor className="h-4 w-4" />, 
      width: '1024px',
      description: '1024px - Desktop'
    },
    { 
      key: 'large' as const, 
      label: 'Large', 
      icon: <Maximize className="h-4 w-4" />, 
      width: '1440px',
      description: '1440px - Large Desktop'
    }
  ];

  const getResponsiveClasses = (viewport: string) => {
    const baseClasses = "font-bold tracking-wider flex flex-col items-center gap-0 leading-tight";
    const fontFamily = { fontFamily: 'Zen Dots, monospace' };
    
    switch (viewport) {
      case 'mobile':
        return {
          container: `${baseClasses} text-sm`,
          syed: "text-base font-black uppercase tracking-widest",
          investments: "text-xs font-bold uppercase tracking-widest -mt-1",
          style: fontFamily
        };
      case 'tablet':
        return {
          container: `${baseClasses} text-base`,
          syed: "text-lg font-black uppercase tracking-widest",
          investments: "text-sm font-bold uppercase tracking-widest -mt-1.5",
          style: fontFamily
        };
      case 'desktop':
        return {
          container: `${baseClasses} text-lg`,
          syed: "text-2xl font-black uppercase tracking-widest",
          investments: "text-base font-bold uppercase tracking-widest -mt-2",
          style: fontFamily
        };
      case 'large':
        return {
          container: `${baseClasses} text-xl`,
          syed: "text-3xl font-black uppercase tracking-widest",
          investments: "text-lg font-bold uppercase tracking-widest -mt-2",
          style: fontFamily
        };
      default:
        return {
          container: baseClasses,
          syed: "text-2xl font-black uppercase tracking-widest",
          investments: "text-base font-bold uppercase tracking-widest -mt-2",
          style: fontFamily
        };
    }
  };

  const classes = getResponsiveClasses(selectedView);

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Responsive Brand Logo Test</h1>
        <p className="text-muted-foreground">
          Test the brand logo across different screen sizes and viewports
        </p>
      </div>

      {/* Viewport Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Viewport</CardTitle>
          <CardDescription>
            Choose a viewport size to see how the brand logo adapts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {viewports.map((viewport) => (
              <Button
                key={viewport.key}
                variant={selectedView === viewport.key ? 'default' : 'outline'}
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => setSelectedView(viewport.key)}
              >
                {viewport.icon}
                <span className="font-medium">{viewport.label}</span>
                <span className="text-xs opacity-70">{viewport.width}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Brand Logo Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Brand Logo Preview
            <Badge variant="secondary">{viewports.find(v => v.key === selectedView)?.description}</Badge>
          </CardTitle>
          <CardDescription>
            How the brand logo appears on {selectedView} devices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div 
            className="mx-auto border-2 border-dashed border-muted-foreground/30 p-8 flex items-center justify-center bg-background"
            style={{ 
              width: viewports.find(v => v.key === selectedView)?.width,
              maxWidth: '100%',
              minHeight: '200px'
            }}
          >
            <div className="relative">
              <h1 className={classes.container} style={classes.style}>
                <span className={classes.syed} style={{ letterSpacing: '0.1em' }}>
                  Syed's
                </span>
                <span className="flex items-center gap-1">
                  <span className={classes.investments} style={{ letterSpacing: '0.1em' }}>
                    Investments
                  </span>
                </span>
              </h1>
              
              {/* TM positioned responsively */}
              <sup 
                className="absolute -top-1 -right-2 text-xs opacity-70" 
                style={{ 
                  fontFamily: 'Arial', 
                  fontSize: '8px',
                  lineHeight: '1'
                }}
              >
                TM
              </sup>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Responsive Specifications */}
      <Card>
        <CardHeader>
          <CardTitle>Responsive Specifications</CardTitle>
          <CardDescription>
            Technical details for each viewport size
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {viewports.map((viewport) => {
              const specs = getResponsiveClasses(viewport.key);
              return (
                <div key={viewport.key} className="p-4 border rounded-lg">
                  <div className="flex items-center gap-2 mb-3">
                    {viewport.icon}
                    <h3 className="font-semibold">{viewport.label}</h3>
                    {selectedView === viewport.key && <Badge>Active</Badge>}
                  </div>
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Viewport:</span>
                      <span className="ml-2 font-mono">{viewport.width}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">"Syed's":</span>
                      <span className="ml-2 font-mono text-xs">{specs.syed.split(' ').find(c => c.startsWith('text-'))}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">"Investments":</span>
                      <span className="ml-2 font-mono text-xs">{specs.investments.split(' ').find(c => c.startsWith('text-'))}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Implementation Code */}
      <Card>
        <CardHeader>
          <CardTitle>Implementation Code</CardTitle>
          <CardDescription>
            Responsive Tailwind CSS classes used in the navbar
          </CardDescription>
        </CardHeader>
        <CardContent>
          <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
{`<h1 className="font-bold tracking-wider flex flex-col items-start sm:items-center gap-0 leading-tight"
    style={{ fontFamily: 'Zen Dots, monospace' }}>
  <span className="text-base sm:text-lg md:text-2xl lg:text-3xl font-black uppercase tracking-widest"
        style={{ letterSpacing: '0.1em' }}>
    Syed's
  </span>
  <span className="flex items-center gap-1 -mt-1 sm:-mt-1.5">
    <span className="text-xs sm:text-sm md:text-base lg:text-lg font-bold uppercase tracking-widest"
          style={{ letterSpacing: '0.1em' }}>
      Investments
    </span>
  </span>
</h1>`}
          </pre>
        </CardContent>
      </Card>

      {/* Breakpoint Reference */}
      <Card>
        <CardHeader>
          <CardTitle>Tailwind Breakpoint Reference</CardTitle>
          <CardDescription>
            Understanding the responsive breakpoints used
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Breakpoints</h4>
              <ul className="text-sm space-y-1">
                <li><code className="bg-muted px-2 py-1 rounded">sm:</code> 640px and up</li>
                <li><code className="bg-muted px-2 py-1 rounded">md:</code> 768px and up</li>
                <li><code className="bg-muted px-2 py-1 rounded">lg:</code> 1024px and up</li>
                <li><code className="bg-muted px-2 py-1 rounded">xl:</code> 1280px and up</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Text Sizes</h4>
              <ul className="text-sm space-y-1">
                <li><code className="bg-muted px-2 py-1 rounded">text-xs</code> 12px</li>
                <li><code className="bg-muted px-2 py-1 rounded">text-sm</code> 14px</li>
                <li><code className="bg-muted px-2 py-1 rounded">text-base</code> 16px</li>
                <li><code className="bg-muted px-2 py-1 rounded">text-lg</code> 18px</li>
                <li><code className="bg-muted px-2 py-1 rounded">text-xl</code> 20px</li>
                <li><code className="bg-muted px-2 py-1 rounded">text-2xl</code> 24px</li>
                <li><code className="bg-muted px-2 py-1 rounded">text-3xl</code> 30px</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ResponsiveBrandTest;
