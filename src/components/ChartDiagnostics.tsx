import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { testYahooDataTransformation, troubleshootingGuide } from '@/utils/testYahooData';
import { validateYahooFinanceResponse } from '@/utils/yahooFinanceDataTransformer';

const ChartDiagnostics: React.FC = () => {
  const [diagnosticResults, setDiagnosticResults] = useState<any>(null);
  const [rawData, setRawData] = useState<string>('');

  const runDiagnostics = () => {
    console.clear();
    const results = testYahooDataTransformation();
    setDiagnosticResults(results);
  };

  const testRawData = () => {
    try {
      const parsed = JSON.parse(rawData);
      const isValid = validateYahooFinanceResponse(parsed);
      
      if (isValid) {
        alert('✅ Raw data is valid Yahoo Finance format!');
      } else {
        alert('❌ Raw data is not in valid Yahoo Finance format. Check console for details.');
      }
    } catch (error) {
      alert('❌ Invalid JSON format. Please check your data.');
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Chart Diagnostics</h1>
        <p className="text-muted-foreground">
          Diagnose and troubleshoot Yahoo Finance chart issues
        </p>
      </div>

      <Tabs defaultValue="diagnostics" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="diagnostics">Diagnostics</TabsTrigger>
          <TabsTrigger value="data-test">Data Test</TabsTrigger>
          <TabsTrigger value="troubleshooting">Troubleshooting</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
        </TabsList>

        <TabsContent value="diagnostics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Automated Diagnostics</CardTitle>
              <CardDescription>
                Run automated tests to validate data transformation and chart compatibility
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={runDiagnostics} className="w-full">
                Run Diagnostics
              </Button>
              
              {diagnosticResults && (
                <div className="space-y-4">
                  <Alert>
                    <AlertDescription>
                      Check the browser console for detailed diagnostic output
                    </AlertDescription>
                  </Alert>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Data Points</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">{diagnosticResults.data.length}</p>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Company</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm font-medium">{diagnosticResults.meta.companyName}</p>
                        <Badge variant="secondary">{diagnosticResults.meta.symbol}</Badge>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="data-test" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Raw Data Validator</CardTitle>
              <CardDescription>
                Paste your Yahoo Finance API response to validate its structure
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <textarea
                className="w-full h-64 p-3 border rounded-md font-mono text-sm"
                placeholder="Paste your Yahoo Finance API response JSON here..."
                value={rawData}
                onChange={(e) => setRawData(e.target.value)}
              />
              <Button onClick={testRawData} disabled={!rawData.trim()}>
                Validate Data Structure
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="troubleshooting" className="space-y-4">
          {Object.entries(troubleshootingGuide).map(([issue, solutions]) => (
            <Card key={issue}>
              <CardHeader>
                <CardTitle className="text-lg">{issue}</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {solutions.map((solution, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-blue-500 mt-1">•</span>
                      <span className="text-sm">{solution}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="examples" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Expected Data Structure</CardTitle>
              <CardDescription>
                This is what your Yahoo Finance API response should look like
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
{`{
  "result": [
    {
      "meta": {
        "currency": "INR",
        "symbol": "BHARTIARTL.NS",
        "regularMarketPrice": 1901,
        "previousClose": 1929.9,
        "regularMarketDayHigh": 1916,
        "regularMarketDayLow": 1891.8,
        "regularMarketVolume": 4643834,
        "longName": "Bharti Airtel Limited"
      },
      "timestamp": [1752810300, 1752810360, ...],
      "indicators": {
        "quote": [
          {
            "open": [1912.8, 1912, ...],
            "high": [1914.5, 1912.9, ...],
            "low": [1909, 1908.9, ...],
            "close": [1911.7, 1910.3, ...],
            "volume": [0, 37426, ...]
          }
        ]
      }
    }
  ],
  "error": null
}`}
              </pre>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Common API Endpoints</CardTitle>
              <CardDescription>
                Yahoo Finance API endpoints for different data types
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium">Intraday Data (1 day, 1-minute intervals)</h4>
                <code className="text-sm bg-muted p-2 rounded block mt-1">
                  /v8/finance/chart/BHARTIARTL.NS?range=1d&interval=1m
                </code>
              </div>
              <div>
                <h4 className="font-medium">Daily Data (1 month, daily intervals)</h4>
                <code className="text-sm bg-muted p-2 rounded block mt-1">
                  /v8/finance/chart/BHARTIARTL.NS?range=1mo&interval=1d
                </code>
              </div>
              <div>
                <h4 className="font-medium">Historical Data (1 year, weekly intervals)</h4>
                <code className="text-sm bg-muted p-2 rounded block mt-1">
                  /v8/finance/chart/BHARTIARTL.NS?range=1y&interval=1wk
                </code>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Chart Implementation Example</CardTitle>
              <CardDescription>
                Basic Recharts implementation with transformed data
              </CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
{`import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts';
import { transformYahooFinanceData } from '@/utils/yahooFinanceDataTransformer';

const StockChart = ({ yahooData }) => {
  const transformed = transformYahooFinanceData(yahooData);
  
  if (!transformed) return <div>No data available</div>;
  
  return (
    <ResponsiveContainer width="100%" height={400}>
      <LineChart data={transformed.data}>
        <XAxis dataKey="time" />
        <YAxis domain={['dataMin - 5', 'dataMax + 5']} />
        <Line 
          type="monotone" 
          dataKey="close" 
          stroke="#3b82f6" 
          strokeWidth={2}
          dot={false}
        />
      </LineChart>
    </ResponsiveContainer>
  );
};`}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ChartDiagnostics;
