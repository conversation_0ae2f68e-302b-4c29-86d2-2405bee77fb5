import React from 'react';
import { useLocation } from 'react-router-dom';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import MarketTicker from '@/components/MarketTicker';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  
  // Pages that should not have the main layout (navbar, footer, etc.)
  const excludedPaths = [
    '/admin',
    '/test'
  ];
  
  // Check if current path should be excluded from main layout
  const isExcludedPath = excludedPaths.some(path => 
    location.pathname.startsWith(path)
  );
  
  // If it's an excluded path, just render children without layout
  if (isExcludedPath) {
    return <>{children}</>;
  }
  
  // For all other pages, render with full layout
  return (
    <div className="min-h-screen flex flex-col">
      <MarketTicker />
      <Navbar />
      <main className="flex-grow">
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default Layout;
