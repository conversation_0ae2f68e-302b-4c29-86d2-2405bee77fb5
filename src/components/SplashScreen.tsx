import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface SplashScreenProps {
  onComplete: () => void;
  duration?: number;
}

const SplashScreen: React.FC<SplashScreenProps> = ({
  onComplete,
  duration = 3000
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isLogoVisible, setIsLogoVisible] = useState(false);
  const [isTextVisible, setIsTextVisible] = useState(false);
  const [isFadingOut, setIsFadingOut] = useState(false);

  useEffect(() => {
    // Stagger the animations
    const logoTimer = setTimeout(() => setIsLogoVisible(true), 300);
    const textTimer = setTimeout(() => setIsTextVisible(true), 800);

    // Start fade out before completion
    const fadeOutTimer = setTimeout(() => {
      setIsFadingOut(true);
    }, duration - 500);

    // Complete the splash screen
    const completeTimer = setTimeout(() => {
      setIsVisible(false);
      onComplete();
    }, duration);

    return () => {
      clearTimeout(logoTimer);
      clearTimeout(textTimer);
      clearTimeout(fadeOutTimer);
      clearTimeout(completeTimer);
    };
  }, [duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        "fixed inset-0 z-[9999] flex items-center justify-center",
        "bg-gradient-to-br from-gray-950 via-slate-950 to-black",
        "transition-opacity duration-500",
        isFadingOut ? "opacity-0" : "opacity-100"
      )}
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient orbs */}
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-blue-600/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-indigo-600/8 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute top-1/2 right-1/3 w-64 h-64 bg-cyan-600/6 rounded-full blur-3xl animate-pulse delay-2000" />
        
        {/* Grid pattern overlay */}
        <div className="absolute inset-0 bg-grid-pattern opacity-5" />
        
        {/* Subtle animated lines */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-blue-500/15 to-transparent animate-pulse" />
          <div className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-indigo-500/10 to-transparent animate-pulse delay-500" />
          <div className="absolute top-1/3 left-0 w-full h-px bg-gradient-to-r from-transparent via-blue-500/10 to-transparent animate-pulse delay-1000" />
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 text-center">
        {/* Logo */}
        <div
          className={cn(
            "mb-8 transition-all duration-1000 ease-out",
            isLogoVisible 
              ? "opacity-100 transform translate-y-0 scale-100" 
              : "opacity-0 transform translate-y-8 scale-95"
          )}
        >
          <div className="relative">
            {/* Logo glow effect */}
            <div className="absolute inset-0 bg-blue-400/25 rounded-full blur-2xl scale-150 animate-pulse" />
            {/* <img
              src={logo}
              alt={splashConfig.companyName}
              className={`relative ${splashConfig.logoSize.width} ${splashConfig.logoSize.height} mx-auto drop-shadow-2xl`}
            /> */}
          </div>
        </div>

        {/* Company name and tagline */}
        <div
          className={cn(
            "transition-all duration-1000 ease-out delay-300",
            isTextVisible 
              ? "opacity-100 transform translate-y-0" 
              : "opacity-0 transform translate-y-4"
          )}
        >
          <h1 className="text-4xl md:text-5xl text-white mb-4 tracking-widest uppercase flex flex-col items-center leading-tight" style={{ fontFamily: 'Zen Dots, monospace' }}>
            <span className="bg-gradient-to-r from-blue-400 via-blue-300 to-cyan-100 bg-clip-text text-transparent animate-pulse text-5xl md:text-6xl font-black">
              Syed's
            </span>
            <span className="bg-gradient-to-r from-blue-400 via-blue-300 to-cyan-100 bg-clip-text text-transparent animate-pulse text-2xl md:text-3xl font-bold -mt-2">
              Investments
            </span>
          </h1>

          <p className="text-lg md:text-xl text-blue-100/80 font-light tracking-wide mb-2">
            Where Vision Meets Valuation
          </p>

          <p className="text-sm text-blue-200/60 font-light italic">
            Bridging Markets, Empowering Investors
          </p>

          <div className="mt-6 flex justify-center">
            <div className="w-24 h-0.5 bg-gradient-to-r from-transparent via-blue-400 to-transparent animate-pulse" />
          </div>
        </div>

        {/* Loading indicator */}
        <div
          className={cn(
            "mt-12 transition-all duration-1000 ease-out delay-700",
            isTextVisible 
              ? "opacity-100 transform translate-y-0" 
              : "opacity-0 transform translate-y-4"
          )}
        >
          <div className="flex justify-center items-center space-x-2">
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-100" />
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-200" />
          </div>
          <p className="text-blue-200/60 text-sm mt-4 font-light">
            Loading your financial compass...
          </p>
        </div>
      </div>

      {/* Bottom accent */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-600 via-indigo-500 to-cyan-500" />
    </div>
  );
};

export default SplashScreen;
