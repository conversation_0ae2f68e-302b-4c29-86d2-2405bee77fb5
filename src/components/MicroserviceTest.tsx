/**
 * Test component for Vercel Python API
 * Use this to verify your API is working correctly
 */

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Loader2, CheckCircle, XCircle, Clock } from 'lucide-react';
import * as microserviceClient from '@/integrations/microservice/client';
import { shouldUseMicroservice, logPerformance } from '@/utils/featureFlags';

const MicroserviceTest: React.FC = () => {
  const [testResults, setTestResults] = useState<any>({});
  const [loading, setLoading] = useState<string | null>(null);
  const [customSymbol, setCustomSymbol] = useState('RELIANCE.NS');

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    setLoading(testName);
    const startTime = Date.now();
    
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      setTestResults(prev => ({
        ...prev,
        [testName]: {
          success: true,
          data: result,
          duration,
          error: null,
          timestamp: new Date().toISOString()
        }
      }));
      
      logPerformance(testName, duration, 'microservice', true);
    } catch (error) {
      const duration = Date.now() - startTime;
      
      setTestResults(prev => ({
        ...prev,
        [testName]: {
          success: false,
          data: null,
          duration,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      }));
      
      logPerformance(testName, duration, 'microservice', false);
    } finally {
      setLoading(null);
    }
  };

  const tests = [
    {
      name: 'health',
      label: 'Health Check',
      description: 'Test if the API is running',
      fn: () => microserviceClient.checkHealth()
    },
    {
      name: 'singleQuote',
      label: 'Single Quote (RELIANCE.NS)',
      description: 'Fetch a single Indian stock quote',
      fn: () => microserviceClient.getStockQuote('RELIANCE.NS')
    },
    {
      name: 'customQuote',
      label: `Custom Quote (${customSymbol})`,
      description: 'Test with custom symbol',
      fn: () => microserviceClient.getStockQuote(customSymbol)
    },
    {
      name: 'batchQuotes',
      label: 'Batch Quotes',
      description: 'Fetch multiple stocks at once',
      fn: () => microserviceClient.getBatchStockQuotes(['RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS'])
    },
    {
      name: 'indices',
      label: 'Indian Indices',
      description: 'Fetch market indices',
      fn: () => microserviceClient.getIndianIndices()
    }
  ];

  const getStatusIcon = (result: any) => {
    if (!result) return <Clock className="h-4 w-4 text-gray-400" />;
    if (result.success) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusBadge = (result: any) => {
    if (!result) return <Badge variant="secondary">Not Run</Badge>;
    if (result.success) return <Badge variant="default">Success</Badge>;
    return <Badge variant="destructive">Failed</Badge>;
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            🧪 Microservice API Test Suite
            <Badge variant={shouldUseMicroservice() ? "default" : "secondary"}>
              {shouldUseMicroservice() ? "Enabled" : "Disabled"}
            </Badge>
          </CardTitle>
          <p className="text-sm text-gray-600">
            Test your Vercel Python API endpoints to ensure they're working correctly.
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Custom Symbol Input */}
            <div className="flex gap-2">
              <Input
                placeholder="Enter symbol (e.g., AAPL, TCS.NS)"
                value={customSymbol}
                onChange={(e) => setCustomSymbol(e.target.value.toUpperCase())}
                className="max-w-xs"
              />
            </div>

            {/* Test Buttons */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {tests.map((test) => (
                <Card key={test.name} className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">{test.label}</h3>
                    {getStatusIcon(testResults[test.name])}
                  </div>
                  <p className="text-sm text-gray-600 mb-3">{test.description}</p>
                  
                  <div className="space-y-2">
                    <Button
                      onClick={() => runTest(test.name, test.fn)}
                      disabled={loading === test.name}
                      size="sm"
                      className="w-full"
                    >
                      {loading === test.name ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Testing...
                        </>
                      ) : (
                        'Run Test'
                      )}
                    </Button>
                    
                    <div className="flex justify-between items-center">
                      {getStatusBadge(testResults[test.name])}
                      {testResults[test.name] && (
                        <span className="text-xs text-gray-500">
                          {testResults[test.name].duration}ms
                        </span>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Run All Tests */}
            <div className="pt-4 border-t">
              <Button
                onClick={() => {
                  tests.forEach((test, index) => {
                    setTimeout(() => runTest(test.name, test.fn), index * 1000);
                  });
                }}
                disabled={loading !== null}
                className="w-full"
              >
                {loading ? 'Running Tests...' : 'Run All Tests'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Display */}
      {Object.keys(testResults).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(testResults).map(([testName, result]: [string, any]) => (
                <div key={testName} className="border rounded p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-medium">{testName}</h4>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(result)}
                      <span className="text-sm text-gray-500">{result.duration}ms</span>
                    </div>
                  </div>
                  
                  {result.error && (
                    <div className="text-red-600 text-sm mb-2">
                      Error: {result.error}
                    </div>
                  )}
                  
                  {result.data && (
                    <details className="text-sm">
                      <summary className="cursor-pointer text-blue-600">View Response Data</summary>
                      <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto max-h-40">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MicroserviceTest;
