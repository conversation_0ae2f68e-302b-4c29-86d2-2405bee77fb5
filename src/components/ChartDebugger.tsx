import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Loader2, Refresh<PERSON>w, Bug } from 'lucide-react';
import { useStockQuote } from '@/hooks/useStockData';

export const ChartDebugger: React.FC = () => {
  const [symbol, setSymbol] = useState('AAPL');
  const [testSymbol, setTestSymbol] = useState('AAPL');

  const {
    data: quoteData,
    isLoading: quoteLoading,
    error: quoteError
  } = useStockQuote(testSymbol);

  const handleTest = () => {
    setTestSymbol(symbol.toUpperCase());
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            Stock Quote Debugger
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Input
              value={symbol}
              onChange={(e) => setSymbol(e.target.value)}
              placeholder="Enter stock symbol (e.g., AAPL, RELIANCE.NS)"
              className="flex-1"
            />
            <Button onClick={handleTest}>Test</Button>
          </div>
          

        </CardContent>
      </Card>

      {/* Quote Data */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Quote Data</span>
              <div className="flex gap-2">
                <Badge variant={quoteLoading ? "secondary" : "default"}>
                  {quoteLoading ? "Loading" : "Loaded"}
                </Badge>
                <Badge variant={quoteError ? "destructive" : "default"}>
                  {quoteError ? "Error" : "Success"}
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {quoteLoading && (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading quote...</span>
              </div>
            )}
            
            {quoteError && (
              <div className="text-red-500 text-sm">
                <p>Error: {quoteError.message}</p>
              </div>
            )}
            
            {quoteData && (
              <div className="space-y-2 text-sm">
                <p><strong>Current Price:</strong> ${quoteData.c}</p>
                <p><strong>Change:</strong> ${quoteData.d} ({quoteData.dp}%)</p>
                <p><strong>High:</strong> ${quoteData.h}</p>
                <p><strong>Low:</strong> ${quoteData.l}</p>
                <p><strong>Open:</strong> ${quoteData.o}</p>
                <p><strong>Previous Close:</strong> ${quoteData.pc}</p>
              </div>
            )}
          </CardContent>
        </Card>

      {/* Raw Data Display */}
      <Card>
        <CardHeader>
          <CardTitle>Raw API Response</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Quote Response:</h4>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(quoteData, null, 2)}
              </pre>
            </div>

          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChartDebugger;
