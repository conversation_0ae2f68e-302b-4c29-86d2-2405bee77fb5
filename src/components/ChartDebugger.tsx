import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Loader2, RefreshCw, Bug } from 'lucide-react';
import { useStockCandles, useStockQuote } from '@/hooks/useStockData';
import { ChartTimeframe } from '@/integrations/finnhub/types';

export const ChartDebugger: React.FC = () => {
  const [symbol, setSymbol] = useState('AAPL');
  const [testSymbol, setTestSymbol] = useState('AAPL');
  const [timeframe, setTimeframe] = useState<ChartTimeframe>('1D');

  const { 
    data: candleData, 
    isLoading: candleLoading, 
    error: candleError,
    refetch: refetchCandles 
  } = useStockCandles(testSymbol, timeframe);

  const { 
    data: quoteData, 
    isLoading: quoteLoading, 
    error: quoteError 
  } = useStockQuote(testSymbol);

  const handleTest = () => {
    setTestSymbol(symbol.toUpperCase());
  };

  const timeframeOptions = [
    { value: '1D' as ChartTimeframe, label: '1D' },
    { value: '1W' as ChartTimeframe, label: '1W' },
    { value: '1M' as ChartTimeframe, label: '1M' },
    { value: '3M' as ChartTimeframe, label: '3M' },
  ];

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="h-5 w-5" />
            Chart Data Debugger
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2 mb-4">
            <Input
              value={symbol}
              onChange={(e) => setSymbol(e.target.value)}
              placeholder="Enter stock symbol (e.g., AAPL, RELIANCE.NS)"
              className="flex-1"
            />
            <Button onClick={handleTest}>Test</Button>
          </div>
          
          <div className="flex gap-2 mb-4">
            {timeframeOptions.map((option) => (
              <Button
                key={option.value}
                variant={timeframe === option.value ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeframe(option.value)}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Quote Data */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Quote Data</span>
              <div className="flex gap-2">
                <Badge variant={quoteLoading ? "secondary" : "default"}>
                  {quoteLoading ? "Loading" : "Loaded"}
                </Badge>
                <Badge variant={quoteError ? "destructive" : "default"}>
                  {quoteError ? "Error" : "Success"}
                </Badge>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {quoteLoading && (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading quote...</span>
              </div>
            )}
            
            {quoteError && (
              <div className="text-red-500 text-sm">
                <p>Error: {quoteError.message}</p>
              </div>
            )}
            
            {quoteData && (
              <div className="space-y-2 text-sm">
                <p><strong>Current Price:</strong> ${quoteData.c}</p>
                <p><strong>Change:</strong> ${quoteData.d} ({quoteData.dp}%)</p>
                <p><strong>High:</strong> ${quoteData.h}</p>
                <p><strong>Low:</strong> ${quoteData.l}</p>
                <p><strong>Open:</strong> ${quoteData.o}</p>
                <p><strong>Previous Close:</strong> ${quoteData.pc}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Candle Data */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Candle Data ({timeframe})</span>
              <div className="flex gap-2">
                <Badge variant={candleLoading ? "secondary" : "default"}>
                  {candleLoading ? "Loading" : "Loaded"}
                </Badge>
                <Badge variant={candleError ? "destructive" : "default"}>
                  {candleError ? "Error" : "Success"}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => refetchCandles()}
                  disabled={candleLoading}
                >
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {candleLoading && (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Loading candles...</span>
              </div>
            )}
            
            {candleError && (
              <div className="text-red-500 text-sm">
                <p>Error: {candleError.message}</p>
              </div>
            )}
            
            {candleData && (
              <div className="space-y-2 text-sm">
                <p><strong>Status:</strong> {candleData.s}</p>
                <p><strong>Data Points:</strong> {candleData.c?.length || 0}</p>
                {candleData.c && candleData.c.length > 0 && (
                  <>
                    <p><strong>First Close:</strong> ${candleData.c[0]}</p>
                    <p><strong>Last Close:</strong> ${candleData.c[candleData.c.length - 1]}</p>
                    <p><strong>First Timestamp:</strong> {new Date(candleData.t[0] * 1000).toLocaleString()}</p>
                    <p><strong>Last Timestamp:</strong> {new Date(candleData.t[candleData.t.length - 1] * 1000).toLocaleString()}</p>
                  </>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Raw Data Display */}
      <Card>
        <CardHeader>
          <CardTitle>Raw API Response</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-2">Quote Response:</h4>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(quoteData, null, 2)}
              </pre>
            </div>
            <div>
              <h4 className="font-medium mb-2">Candle Response:</h4>
              <pre className="bg-gray-100 p-3 rounded text-xs overflow-auto max-h-40">
                {JSON.stringify(candleData, null, 2)}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChartDebugger;
