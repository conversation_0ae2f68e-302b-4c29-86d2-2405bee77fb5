import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import MarketTicker, { type TickerSpeed } from '@/components/MarketTicker';
import { Play, Pause, Settings, Zap, Clock, Turtle } from 'lucide-react';

const TickerSpeedTest: React.FC = () => {
  const [speed, setSpeed] = useState<TickerSpeed>('normal');
  const [pauseOnHover, setPauseOnHover] = useState(true);
  const [showCompanyNames, setShowCompanyNames] = useState(true);

  const speedOptions: Array<{ value: TickerSpeed; label: string; icon: React.ReactNode; description: string }> = [
    {
      value: 'slow',
      label: 'Slow',
      icon: <Turtle className="h-4 w-4" />,
      description: 'Best for reading detailed information (180s duration)'
    },
    {
      value: 'normal',
      label: 'Normal',
      icon: <Clock className="h-4 w-4" />,
      description: 'Balanced speed for general viewing (120s duration)'
    },
    {
      value: 'fast',
      label: 'Fast',
      icon: <Zap className="h-4 w-4" />,
      description: 'Quick overview of market data (80s duration)'
    }
  ];

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-4">Stock Ticker Speed Test</h1>
        <p className="text-muted-foreground">
          Test different ticker speeds and configurations for optimal readability
        </p>
      </div>

      {/* Live Ticker Demo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Live Market Ticker
          </CardTitle>
          <CardDescription>
            Current configuration: {speed} speed, {pauseOnHover ? 'pause on hover enabled' : 'no pause on hover'}, 
            {showCompanyNames ? 'showing company names' : 'symbols only'}
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <MarketTicker 
            speed={speed}
            pauseOnHover={pauseOnHover}
            showCompanyNames={showCompanyNames}
          />
        </CardContent>
      </Card>

      {/* Speed Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Ticker Configuration
          </CardTitle>
          <CardDescription>
            Adjust ticker settings to find the optimal reading experience
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Speed Selection */}
          <div>
            <Label className="text-base font-medium mb-3 block">Animation Speed</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {speedOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={speed === option.value ? 'default' : 'outline'}
                  className="h-auto p-4 flex flex-col items-start gap-2"
                  onClick={() => setSpeed(option.value)}
                >
                  <div className="flex items-center gap-2">
                    {option.icon}
                    <span className="font-medium">{option.label}</span>
                    {speed === option.value && <Badge variant="secondary">Active</Badge>}
                  </div>
                  <p className="text-xs text-left opacity-70">
                    {option.description}
                  </p>
                </Button>
              ))}
            </div>
          </div>

          {/* Additional Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="flex items-center space-x-2">
              <Switch
                id="pause-hover"
                checked={pauseOnHover}
                onCheckedChange={setPauseOnHover}
              />
              <Label htmlFor="pause-hover" className="flex flex-col">
                <span className="font-medium">Pause on Hover</span>
                <span className="text-sm text-muted-foreground">
                  Pause animation when hovering over ticker
                </span>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="show-names"
                checked={showCompanyNames}
                onCheckedChange={setShowCompanyNames}
              />
              <Label htmlFor="show-names" className="flex flex-col">
                <span className="font-medium">Show Company Names</span>
                <span className="text-sm text-muted-foreground">
                  Display full company names instead of just symbols
                </span>
              </Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Speed Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Speed Comparison & Recommendations</CardTitle>
          <CardDescription>
            Choose the right speed based on your use case
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Turtle className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold">Slow Speed</h3>
              </div>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Best for detailed reading</li>
                <li>• Ideal for financial analysis</li>
                <li>• Recommended for desktop users</li>
                <li>• Auto-selected for 15+ stocks</li>
              </ul>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="h-5 w-5 text-green-600" />
                <h3 className="font-semibold">Normal Speed</h3>
              </div>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Balanced viewing experience</li>
                <li>• Good for general monitoring</li>
                <li>• Works well on all devices</li>
                <li>• Default recommendation</li>
              </ul>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="h-5 w-5 text-orange-600" />
                <h3 className="font-semibold">Fast Speed</h3>
              </div>
              <ul className="text-sm space-y-1 text-muted-foreground">
                <li>• Quick market overview</li>
                <li>• Good for mobile devices</li>
                <li>• Less detailed reading</li>
                <li>• Auto-selected for &lt;8 stocks</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Technical Details */}
      <Card>
        <CardHeader>
          <CardTitle>Technical Implementation</CardTitle>
          <CardDescription>
            How the ticker speed optimization works
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Adaptive Speed Algorithm</h4>
              <p className="text-sm text-muted-foreground">
                The ticker automatically adjusts speed based on the amount of data:
              </p>
              <ul className="text-sm text-muted-foreground mt-2 space-y-1">
                <li>• <strong>15+ stocks:</strong> Automatically switches to slow speed</li>
                <li>• <strong>8-14 stocks:</strong> Uses selected speed</li>
                <li>• <strong>&lt;8 stocks:</strong> Automatically switches to fast speed</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">Animation Durations</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="p-3 bg-muted rounded">
                  <div className="font-medium">Slow</div>
                  <div className="text-muted-foreground">180 seconds</div>
                </div>
                <div className="p-3 bg-muted rounded">
                  <div className="font-medium">Normal</div>
                  <div className="text-muted-foreground">120 seconds</div>
                </div>
                <div className="p-3 bg-muted rounded">
                  <div className="font-medium">Fast</div>
                  <div className="text-muted-foreground">80 seconds</div>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">User Experience Features</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• <strong>Pause on Hover:</strong> Allows users to read specific stocks</li>
                <li>• <strong>Improved Spacing:</strong> Better readability with increased padding</li>
                <li>• <strong>Company Names Toggle:</strong> Show full names or just symbols</li>
                <li>• <strong>Responsive Design:</strong> Works on all screen sizes</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TickerSpeedTest;
