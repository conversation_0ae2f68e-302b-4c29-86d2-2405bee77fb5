
// import React, { useEffect, useState } from 'react';
// import { getStockData } from '../integrations/finnhub/client';

// const FinnhubTestComponent: React.FC = () => {
//   const [stockData, setStockData] = useState<any>(null);
//   const [error, setError] = useState<string | null>(null);

//   useEffect(() => {
//     const fetchStock = async () => {
//       try {
//         const data = await getStockData('AAPL');
//         setStockData(data);
//       } catch (err) {
//         setError('Failed to fetch stock data.');
//         console.error(err);
//       }
//     };

//     fetchStock();
//   }, []);

//   // if (error) {
//   //   return <div>Error: {error}</div>;
//   // }

//   // if (!stockData) {
//   //   return <div>Loading stock data...</div>;
//   // }

//   // return (
//   //   <div>
//   //     <h2>AAPL Stock Data</h2>
//   //     <p>Current Price: {stockData.c}</p>
//   //     <p>High Price: {stockData.h}</p>
//   //     <p>Low Price: {stockData.l}</p>
//   //     <p>Open Price: {stockData.o}</p>
//   //     <p>Previous Close: {stockData.pc}</p>
//   //   </div>
//   // );
// };


// export default FinnhubTestComponent;