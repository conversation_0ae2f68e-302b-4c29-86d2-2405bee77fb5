import React, { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useQuarterlyOutlook } from '@/hooks/useContent';

const QuarterlyOutlookDebug: React.FC = () => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<any>(null);

  // Test the hook directly
  const { data: hookData, isLoading: hookLoading, error: hookError } = useQuarterlyOutlook();

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching quarterly outlook data...');

        // Check authentication status
        const { data: { session } } = await supabase.auth.getSession();
        console.log('Current session:', session);

        // Test simple query first
        const { data: allData, error: allError } = await supabase
          .from('quarterly_outlook')
          .select('*');

        console.log('All quarterly outlook data (no filters):', { allData, allError });

        // Test with status filter
        const { data: result, error: queryError } = await supabase
          .from('quarterly_outlook')
          .select('*')
          .eq('status', 'published')
          .order('year', { ascending: false })
          .order('quarter', { ascending: false });

        console.log('Direct Supabase query result:', { result, queryError });
        console.log('Hook data:', { hookData, hookLoading, hookError });

        if (queryError) {
          setError(queryError);
        } else {
          setData(result);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div className="p-4 bg-yellow-100">Loading quarterly outlook data...</div>;
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100">
        <h3 className="font-bold text-red-800">Error:</h3>
        <pre className="text-sm text-red-700">{JSON.stringify(error, null, 2)}</pre>
      </div>
    );
  }

  return (
    <div className="p-4 bg-green-100">
      <h3 className="font-bold text-green-800 mb-2">Quarterly Outlook Debug Data:</h3>
      <div className="mb-4">
        <h4 className="font-semibold">Direct Query:</h4>
        <p className="text-green-700 mb-2">Found {data?.length || 0} records</p>
      </div>
      <div className="mb-4">
        <h4 className="font-semibold">Hook Data:</h4>
        <p className="text-green-700 mb-2">
          Hook Loading: {hookLoading ? 'Yes' : 'No'},
          Hook Error: {hookError ? 'Yes' : 'No'},
          Hook Data: {hookData?.length || 0} records
        </p>
      </div>
      <pre className="text-xs text-green-700 bg-white p-2 rounded overflow-auto max-h-96">
        Direct Query Data: {JSON.stringify(data, null, 2)}

        Hook Data: {JSON.stringify(hookData, null, 2)}
      </pre>
    </div>
  );
};

export default QuarterlyOutlookDebug;
