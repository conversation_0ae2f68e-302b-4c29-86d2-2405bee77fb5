import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { getStockQuote } from "@/integrations/finnhub/client";

const ApiDebugger: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const testHealthEndpoint = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/health');
      const data = await response.json();
      setResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testFinnhubApi = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const data = await getStockQuote('AAPL');
      setResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testDirectFinnhub = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/finnhub/quote?symbol=AAPL');
      const data = await response.json();
      setResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testIndianStock = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/finnhub/quote?symbol=BHARTIARTL.NS');
      const data = await response.json();
      setResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  const testDebugEndpoint = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/debug');
      const data = await response.json();
      setResult(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔧 API Debugger
          <Badge variant="outline">Development Tool</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={testHealthEndpoint}
            disabled={isLoading}
            variant="outline"
          >
            Test Health
          </Button>
          <Button
            onClick={testDebugEndpoint}
            disabled={isLoading}
            variant="outline"
          >
            Test Debug
          </Button>
          <Button
            onClick={testFinnhubApi}
            disabled={isLoading}
            variant="outline"
          >
            Test Client (AAPL)
          </Button>
          <Button
            onClick={testDirectFinnhub}
            disabled={isLoading}
            variant="outline"
          >
            Test Direct (AAPL)
          </Button>
          <Button
            onClick={testIndianStock}
            disabled={isLoading}
            variant="outline"
          >
            Test Indian Stock
          </Button>
        </div>

        {isLoading && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-muted-foreground">Testing API...</p>
          </div>
        )}

        {error && (
          <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
            <h3 className="font-semibold text-destructive mb-2">❌ Error</h3>
            <pre className="text-sm text-destructive whitespace-pre-wrap">{error}</pre>
          </div>
        )}

        {result && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold text-green-800 mb-2">✅ Success</h3>
            <pre className="text-sm text-green-700 whitespace-pre-wrap overflow-auto max-h-64">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="text-xs text-muted-foreground space-y-1">
          <p><strong>Environment:</strong> {import.meta.env.MODE}</p>
          <p><strong>Production:</strong> {import.meta.env.PROD ? 'Yes' : 'No'}</p>
          <p><strong>Has API Key:</strong> {import.meta.env.VITE_FINNHUB_API_KEY ? 'Yes' : 'No'}</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ApiDebugger;
