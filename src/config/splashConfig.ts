export interface SplashScreenConfig {
  // Display settings
  duration: number;
  minDisplayTime: number;
  skipOnRevisit: boolean;
  
  // Animation timings
  logoDelay: number;
  textDelay: number;
  fadeOutOffset: number;
  
  // Content
  companyName: string;
  primaryTagline: string;
  secondaryTagline: string;
  loadingText: string;
  
  // Styling
  gradientColors: {
    background: string[];
    text: string[];
    accent: string[];
  };
  
  // Logo settings
  logoSize: {
    width: string;
    height: string;
  };
}

export const defaultSplashConfig: SplashScreenConfig = {
  // Display settings
  duration: 3000,
  minDisplayTime: 2000,
  skipOnRevisit: true,
  
  // Animation timings
  logoDelay: 300,
  textDelay: 800,
  fadeOutOffset: 500,
  
  // Content
  companyName: "Syed's Investments",
  primaryTagline: "Where Vision Meets Valuation",
  secondaryTagline: "Bridging Markets, Empowering Investors",
  loadingText: "Loading your financial compass...",
  
  // Styling
  gradientColors: {
    background: ["from-slate-900", "via-blue-900", "to-slate-800"],
    text: ["from-blue-400", "via-blue-300", "to-cyan-300"],
    accent: ["from-blue-600", "via-indigo-500", "to-cyan-500"]
  },
  
  // Logo settings
  logoSize: {
    width: "w-32",
    height: "h-32"
  }
};

// Alternative configurations for different themes
export const lightSplashConfig: SplashScreenConfig = {
  ...defaultSplashConfig,
  gradientColors: {
    background: ["from-blue-50", "via-indigo-100", "to-blue-100"],
    text: ["from-blue-600", "via-indigo-600", "to-blue-700"],
    accent: ["from-blue-500", "via-indigo-500", "to-blue-600"]
  }
};

export const premiumSplashConfig: SplashScreenConfig = {
  ...defaultSplashConfig,
  duration: 4000,
  gradientColors: {
    background: ["from-gray-900", "via-purple-900", "to-indigo-900"],
    text: ["from-gold-400", "via-yellow-300", "to-amber-300"],
    accent: ["from-gold-500", "via-yellow-500", "to-amber-500"]
  }
};
