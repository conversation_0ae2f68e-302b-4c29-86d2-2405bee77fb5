import React, { useState, useEffect } from 'react';
import { useIsAdmin } from '@/hooks/useContent';
import { useAuth } from '@/contexts/AuthContext';
import { useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Plus,
  FileText,
  Newspaper,
  BookOpen,
  Users,
  TrendingUp,
  Settings,
  Shield,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { AdminContentList } from '@/components/admin/AdminContentList';
import { AdminStats } from '@/components/admin/AdminStats';
import { AdminQuarterlyOutlookList } from '@/components/admin/AdminQuarterlyOutlookList';
import { AdminPagesList } from '@/components/admin/AdminPagesList';
import FeedbackManagement from '@/components/admin/FeedbackManagement';

const Admin: React.FC = () => {
  const { user } = useAuth();
  const isAdmin = useIsAdmin();
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState('overview');

  // Handle URL tab parameter
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
          <p className="text-muted-foreground">Please sign in to access the admin dashboard.</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4 text-red-600">Access Denied</h1>
          <p className="text-muted-foreground">
            You don't have permission to access the admin dashboard. 
            Please contact an administrator if you believe this is an error.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">Admin Dashboard</h1>
        <p className="text-muted-foreground">
          Manage content, users, and platform settings
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-8">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="news">News</TabsTrigger>
          <TabsTrigger value="education">Education</TabsTrigger>
          <TabsTrigger value="case-studies">Case Studies</TabsTrigger>
          <TabsTrigger value="quarterly-outlook">Quarterly Outlook</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
          <TabsTrigger value="pages">Pages</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <AdminStats />
          
          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <TrendingUp className="h-8 w-8 text-blue-600" />
                  <Badge variant="secondary">Insights</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold mb-2">Create Insight</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Add new market analysis and insights
                </p>
                <Button asChild size="sm" className="w-full">
                  <Link to="/admin/insights/new">
                    <Plus className="h-4 w-4 mr-2" />
                    New Insight
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <Newspaper className="h-8 w-8 text-green-600" />
                  <Badge variant="secondary">News</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold mb-2">Create News</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Publish latest financial news
                </p>
                <Button asChild size="sm" className="w-full">
                  <Link to="/admin/news/new">
                    <Plus className="h-4 w-4 mr-2" />
                    New Article
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <BookOpen className="h-8 w-8 text-purple-600" />
                  <Badge variant="secondary">Education</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold mb-2">Create Tutorial</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Add educational content
                </p>
                <Button asChild size="sm" className="w-full">
                  <Link to="/admin/education/new">
                    <Plus className="h-4 w-4 mr-2" />
                    New Tutorial
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <FileText className="h-8 w-8 text-orange-600" />
                  <Badge variant="secondary">Case Studies</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold mb-2">Create Case Study</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Document investment cases
                </p>
                <Button asChild size="sm" className="w-full">
                  <Link to="/admin/case-studies/new">
                    <Plus className="h-4 w-4 mr-2" />
                    New Case Study
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <Calendar className="h-8 w-8 text-indigo-600" />
                  <Badge variant="secondary">Quarterly Outlook</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold mb-2">Create Quarterly Outlook</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Add quarterly market analysis and forecasts
                </p>
                <Button asChild size="sm" className="w-full">
                  <Link to="/admin/quarterly-outlook/new">
                    <Plus className="h-4 w-4 mr-2" />
                    New Outlook
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <FileText className="h-8 w-8 text-purple-600" />
                  <Badge variant="secondary">Pages</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold mb-2">Create Page</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Add static pages like About Us, Privacy Policy
                </p>
                <Button asChild size="sm" className="w-full">
                  <Link to="/admin/pages/new">
                    <Plus className="h-4 w-4 mr-2" />
                    New Page
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => setActiveTab('feedback')}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <MessageSquare className="h-8 w-8 text-purple-600" />
                  <Badge variant="secondary">Feedback</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <h3 className="font-semibold mb-2">Manage Feedback</h3>
                <p className="text-sm text-muted-foreground mb-3">
                  View and respond to user feedback
                </p>
                <Button size="sm" className="w-full">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  View Feedback
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
              <CardDescription>
                Latest content updates and user activity
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-3 rounded-lg">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">New insight published</p>
                    <p className="text-xs text-muted-foreground">2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center gap-4 p-3  rounded-lg">
                  <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">News article updated</p>
                    <p className="text-xs text-muted-foreground">4 hours ago</p>
                  </div>
                </div>
                <div className="flex items-center gap-4 p-3  rounded-lg">
                  <div className="h-2 w-2 bg-purple-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">New user registered</p>
                    <p className="text-xs text-muted-foreground">6 hours ago</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights">
          <AdminContentList contentType="insights" />
        </TabsContent>

        <TabsContent value="news">
          <AdminContentList contentType="news" />
        </TabsContent>

        <TabsContent value="education">
          <AdminContentList contentType="education" />
        </TabsContent>

        <TabsContent value="case-studies">
          <AdminContentList contentType="case_studies" />
        </TabsContent>

        <TabsContent value="quarterly-outlook">
          <AdminQuarterlyOutlookList />
        </TabsContent>

        <TabsContent value="pages">
          <AdminPagesList />
        </TabsContent>

        <TabsContent value="feedback" className="space-y-6">
          <FeedbackManagement />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Admin;
