import React, { useState } from 'react';
import { usePublishedContent, useContentCategories } from '@/hooks/useContent';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Search, Calendar, User, BookOpen, Clock, GraduationCap } from 'lucide-react';
import { Link } from 'react-router-dom';

const Education: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('');
  
  const { data: education, isLoading, error } = usePublishedContent('education', {
    search: searchTerm,
    category: selectedCategory || undefined,
  });
  
  const { data: categories } = useContentCategories('education');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredEducation = education?.filter(item => 
    !selectedDifficulty || item.difficulty_level === selectedDifficulty
  );

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error Loading Education Content</h1>
          <p className="text-muted-foreground">There was an error loading the educational content. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-4">Financial Education</h1>
        <p className="text-xl text-muted-foreground">
          Learn the fundamentals of investing and build your financial knowledge
        </p>
      </div>

      {/* Enhanced Search and Filters */}
      <div className="mb-8 space-y-6">
        <form onSubmit={handleSearch} className="flex gap-4">
          <div className="relative flex-1">
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-muted-foreground transition-colors duration-200" />
            </div>
            <Input
              placeholder="Search investment tutorials, guides, and educational resources..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 pr-4 h-12 text-base bg-background/50 border-2 border-border/50 rounded-xl
                         focus:border-primary/50 focus:bg-background transition-all duration-200
                         placeholder:text-muted-foreground/70 shadow-sm hover:shadow-md focus:shadow-lg"
            />
            {searchTerm && (
              <button
                type="button"
                onClick={() => setSearchTerm('')}
                className="absolute inset-y-0 right-0 pr-4 flex items-center text-muted-foreground
                           hover:text-foreground transition-colors duration-200"
              >
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </div>
          <Button
            type="submit"
            size="lg"
            className="px-8 h-12 rounded-xl font-medium shadow-sm hover:shadow-md transition-all duration-200"
          >
            Search
          </Button>
        </form>

        {/* Difficulty Filter */}
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium text-muted-foreground self-center">Difficulty:</span>
          <Button
            variant={selectedDifficulty === '' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedDifficulty('')}
          >
            All Levels
          </Button>
          {['beginner', 'intermediate', 'advanced'].map((level) => (
            <Button
              key={level}
              variant={selectedDifficulty === level ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedDifficulty(level)}
              className="capitalize"
            >
              {level}
            </Button>
          ))}
        </div>

        {/* Categories */}
        {categories && categories.length > 0 && (
          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium text-muted-foreground self-center">Topics:</span>
            <Button
              variant={selectedCategory === '' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('')}
            >
              All Topics
            </Button>
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Content Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full mb-4" />
                <Skeleton className="h-3 w-full mb-2" />
                <Skeleton className="h-3 w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredEducation && filteredEducation.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredEducation.map((item) => (
            <Card key={item.id} className="hover:shadow-lg transition-shadow">
              {item.featured_image && (
                <div className="aspect-video overflow-hidden rounded-t-lg">
                  <img
                    src={item.featured_image}
                    alt={item.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
              <CardHeader>
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {item.category && (
                      <Badge variant="secondary">{item.category}</Badge>
                    )}
                    <Badge 
                      className={`${getDifficultyColor(item.difficulty_level)} border-0 capitalize`}
                    >
                      {item.difficulty_level}
                    </Badge>
                  </div>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="h-3 w-3 mr-1" />
                    {new Date(item.published_at || item.created_at).toLocaleDateString()}
                  </div>
                </div>
                <CardTitle className="line-clamp-2">
                  <Link 
                    to={`/education/${item.id}`}
                    className="hover:text-primary transition-colors"
                  >
                    {item.title}
                  </Link>
                </CardTitle>
                {item.excerpt && (
                  <CardDescription className="line-clamp-3">
                    {item.excerpt}
                  </CardDescription>
                )}
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center text-sm text-muted-foreground">
                    <User className="h-3 w-3 mr-1" />
                    Author
                  </div>
                  {item.estimated_read_time && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Clock className="h-3 w-3 mr-1" />
                      {item.estimated_read_time} min read
                    </div>
                  )}
                </div>
                {item.tags && item.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {item.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {item.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{item.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <GraduationCap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No educational content found</h3>
          <p className="text-muted-foreground">
            {searchTerm || selectedCategory || selectedDifficulty
              ? 'Try adjusting your search criteria'
              : 'Check back later for new educational content'}
          </p>
        </div>
      )}

      {/* Learning Path Suggestion */}
      {!searchTerm && !selectedCategory && !selectedDifficulty && (
        <div className="mt-12 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg ">
          <div className="flex items-center mb-4">
            <BookOpen className="h-6 w-6 text-blue-600 mr-2" />
            <h2 className="text-xl font-bold text-gray-900">Start Your Learning Journey</h2>
          </div>
          <p className="text-muted-foreground mb-4">
            New to investing? We recommend starting with beginner-level content to build a solid foundation.
          </p>
          <Button 
            onClick={() => setSelectedDifficulty('beginner')}
            className="bg-blue-600 hover:bg-blue-700"
          >
            View Beginner Content
          </Button>
        </div>
      )}
    </div>
  );
};

export default Education;
