import React from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useContentItem } from '@/hooks/useContent';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Calendar, User, Tag, Clock, GraduationCap } from 'lucide-react';

const EducationDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { data: content, isLoading, error } = useContentItem('education', id || '');

  const getDifficultyColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'bg-green-100 text-green-800';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800';
      case 'advanced':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto space-y-6">
          <Skeleton className="h-8 w-32" />
          <Skeleton className="h-12 w-3/4" />
          <Skeleton className="h-64 w-full" />
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !content) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Content Not Found</h1>
          <p className="text-muted-foreground mb-4">
            The educational content you're looking for doesn't exist or has been removed.
          </p>
          <Button asChild>
            <Link to="/education">Back to Education</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Navigation */}
        <Button variant="ghost" asChild className="mb-6">
          <Link to="/education">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Education
          </Link>
        </Button>

        {/* Content Header */}
        <div className="mb-8">
          {content.featured_image && (
            <div className="aspect-video overflow-hidden rounded-lg mb-6">
              <img
                src={content.featured_image}
                alt={content.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div className="space-y-4">
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                {new Date(content.published_at || content.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                Author
              </div>
              {content.estimated_read_time && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {content.estimated_read_time} min read
                </div>
              )}
              {content.category && (
                <Badge variant="secondary">{content.category}</Badge>
              )}
              <Badge 
                className={`${getDifficultyColor(content.difficulty_level)} border-0 capitalize`}
              >
                <GraduationCap className="h-3 w-3 mr-1" />
                {content.difficulty_level}
              </Badge>
            </div>

            <h1 className="text-4xl font-bold leading-tight">{content.title}</h1>

            {content.excerpt && (
              <p className="text-xl text-muted-foreground leading-relaxed">
                {content.excerpt}
              </p>
            )}

            {content.tags && content.tags.length > 0 && (
              <div className="flex items-center gap-2">
                <Tag className="h-4 w-4 text-muted-foreground" />
                <div className="flex flex-wrap gap-2">
                  {content.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Content */}
        <Card>
          <CardContent className="p-8">
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: content.content }}
            />
          </CardContent>
        </Card>

        {/* Related Content */}
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">Related Educational Content</h2>
          <div className="text-center py-8 text-muted-foreground">
            <p>Related educational content will be displayed here.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EducationDetail;
