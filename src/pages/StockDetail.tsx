import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  ArrowUp, 
  ArrowDown, 
  Star, 
  StarOff, 
  Globe, 
  Building, 
  Calendar,
  TrendingUp,
  Volume2,
  DollarSign,
  Loader2
} from 'lucide-react';
import { useStockQuote, useCompanyProfile, useMarketNews } from '@/hooks/useStockData';
import { useIndianStockQuote } from '@/hooks/useIndianStockData';
import StockChart from '@/components/StockChart';

const StockDetail: React.FC = () => {
  const { symbol } = useParams<{ symbol: string }>();
  const navigate = useNavigate();
  const [isInWatchlist, setIsInWatchlist] = useState(false);

  if (!symbol) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Invalid Stock Symbol</h1>
          <Button onClick={() => navigate('/')} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </div>
    );
  }

  // Determine if this is an Indian stock
  const isIndianStock = symbol.endsWith('.NS') || symbol.endsWith('.BO');

  // Use appropriate hook based on stock type
  const { data: quote, isLoading: quoteLoading, error: quoteError } = isIndianStock
    ? useIndianStockQuote(symbol)
    : useStockQuote(symbol);

  // Only fetch company profile for non-Indian stocks (Indian stocks don't have Finnhub profiles)
  const { data: profile, isLoading: profileLoading } = useCompanyProfile(symbol, !isIndianStock);

  // Only fetch news when quote data is available to avoid unnecessary calls
  const { data: news } = useMarketNews('general', !!quote);

  const formatPrice = (price: number): string => {
    if (symbol.endsWith('.NS') || symbol.endsWith('.BO')) {
      return `₹${price.toLocaleString('en-IN', { maximumFractionDigits: 2 })}`;
    }
    return `$${price.toLocaleString('en-US', { maximumFractionDigits: 2 })}`;
  };

  const formatMarketCap = (marketCap: number): string => {
    if (marketCap >= 1e12) {
      return `${(marketCap / 1e12).toFixed(2)}T`;
    } else if (marketCap >= 1e9) {
      return `${(marketCap / 1e9).toFixed(2)}B`;
    } else if (marketCap >= 1e6) {
      return `${(marketCap / 1e6).toFixed(2)}M`;
    }
    return marketCap.toString();
  };

  const toggleWatchlist = () => {
    setIsInWatchlist(!isInWatchlist);
    // Here you would typically save to localStorage or backend
  };

  if (quoteLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="text-lg">Loading stock data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (quoteError || !quote) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600">Failed to Load Stock Data</h1>
          <p className="text-gray-600 mt-2">Unable to fetch data for {symbol}</p>
          <Button onClick={() => navigate('/')} className="mt-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Home
          </Button>
        </div>
      </div>
    );
  }

  // Handle different quote structures (Finnhub vs Yahoo Finance)
  const currentPrice = isIndianStock ? quote.price : quote.c;
  const change = isIndianStock ? quote.change : quote.d;
  const changePercent = isIndianStock ? quote.changePercent : quote.dp;
  const high = isIndianStock ? quote.high : quote.h;
  const low = isIndianStock ? quote.low : quote.l;
  const open = isIndianStock ? quote.open : quote.o;
  const previousClose = isIndianStock ? quote.previousClose : quote.pc;
  const volume = isIndianStock ? quote.volume : 0;

  const isPositive = change >= 0;
  const stockName = isIndianStock ? quote.name : (profile?.name || symbol.replace('.NS', '').replace('.BO', ''));

  return (
    <div className="container mx-auto px-4 py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Button variant="ghost" onClick={() => navigate('/')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
        <Button
          variant={isInWatchlist ? "default" : "outline"}
          onClick={toggleWatchlist}
        >
          {isInWatchlist ? (
            <>
              <Star className="h-4 w-4 mr-2 fill-current" />
              In Watchlist
            </>
          ) : (
            <>
              <StarOff className="h-4 w-4 mr-2" />
              Add to Watchlist
            </>
          )}
        </Button>
      </div>

      {/* Stock Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold">{stockName}</h1>
                <Badge variant="secondary">{symbol}</Badge>
                {profile?.exchange && (
                  <Badge variant="outline">{profile.exchange}</Badge>
                )}
              </div>
              {profile?.finnhubIndustry && (
                <p className="text-gray-600">{profile.finnhubIndustry}</p>
              )}
            </div>
            <div className="text-right">
              <p className="text-4xl font-bold">{formatPrice(currentPrice)}</p>
              <div className={`flex items-center justify-end mt-1 ${
                isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {isPositive ? (
                  <ArrowUp className="h-5 w-5 mr-1" />
                ) : (
                  <ArrowDown className="h-5 w-5 mr-1" />
                )}
                <span className="text-lg font-semibold">
                  {isPositive ? '+' : ''}{formatPrice(change)} ({changePercent.toFixed(2)}%)
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Last updated: {new Date((isIndianStock ? quote.timestamp : quote.t) * 1000).toLocaleString()}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <DollarSign className="h-8 w-8 mx-auto mb-2 text-blue-500" />
            <p className="text-sm text-gray-600">Open</p>
            <p className="text-lg font-semibold">{formatPrice(open)}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-green-500" />
            <p className="text-sm text-gray-600">High</p>
            <p className="text-lg font-semibold">{formatPrice(high)}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <ArrowDown className="h-8 w-8 mx-auto mb-2 text-red-500" />
            <p className="text-sm text-gray-600">Low</p>
            <p className="text-lg font-semibold">{formatPrice(low)}</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Volume2 className="h-8 w-8 mx-auto mb-2 text-purple-500" />
            <p className="text-sm text-gray-600">Prev Close</p>
            <p className="text-lg font-semibold">{formatPrice(previousClose)}</p>
          </CardContent>
        </Card>
      </div>

      {/* Stock Chart */}
      <StockChart
        symbol={symbol}
        name={stockName}
        height={500}
        showControls={true}
        currentQuote={quote}
      />

      {/* Company Profile & News */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Company Profile */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2" />
              Company Profile
            </CardTitle>
          </CardHeader>
          <CardContent>
            {profileLoading ? (
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4"></div>
              </div>
            ) : profile ? (
              <div className="space-y-4">
                <div>
                  <h3 className="font-semibold text-lg">{profile.name}</h3>
                  <p className="text-gray-600">{profile.finnhubIndustry}</p>
                </div>
                <Separator />
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-600">Country</p>
                    <p className="font-medium">{profile.country}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Currency</p>
                    <p className="font-medium">{profile.currency}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">Exchange</p>
                    <p className="font-medium">{profile.exchange}</p>
                  </div>
                  <div>
                    <p className="text-gray-600">IPO Date</p>
                    <p className="font-medium">{profile.ipo}</p>
                  </div>
                  {profile.marketCapitalization && (
                    <div className="col-span-2">
                      <p className="text-gray-600">Market Cap</p>
                      <p className="font-medium">{formatMarketCap(profile.marketCapitalization * 1000000)}</p>
                    </div>
                  )}
                </div>
                {profile.weburl && (
                  <div className="pt-2">
                    <a 
                      href={profile.weburl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="flex items-center text-blue-600 hover:text-blue-800"
                    >
                      <Globe className="h-4 w-4 mr-2" />
                      Visit Website
                    </a>
                  </div>
                )}
              </div>
            ) : (
              <p className="text-gray-500">Company profile not available</p>
            )}
          </CardContent>
        </Card>

        {/* Market News */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Market News
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {news && news.length > 0 ? (
                news.slice(0, 5).map((article, index) => (
                  <div key={index} className="border-b pb-3 last:border-0">
                    <h4 className="font-medium text-sm mb-1 line-clamp-2">
                      {article.headline}
                    </h4>
                    <p className="text-xs text-gray-600 mb-2 line-clamp-2">
                      {article.summary}
                    </p>
                    <div className="flex justify-between items-center text-xs text-gray-500">
                      <span>{article.source}</span>
                      <span>{new Date(article.datetime * 1000).toLocaleDateString()}</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500">No recent news available</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StockDetail;
