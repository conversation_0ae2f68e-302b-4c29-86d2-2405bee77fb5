import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePortfolios, useCreatePortfolio } from '@/hooks/usePortfolio';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, TrendingUp, DollarSign, PieChart, Loader2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const { data: portfolios, isLoading, error } = usePortfolios();
  const createPortfolioMutation = useCreatePortfolio();
  
  const [newPortfolio, setNewPortfolio] = useState({
    name: '',
    description: '',
    total_value: 0,
  });
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  const handleCreatePortfolio = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createPortfolioMutation.mutateAsync(newPortfolio);
      setNewPortfolio({ name: '', description: '', total_value: 0 });
      setIsCreateModalOpen(false);
      toast({
        title: "Portfolio created",
        description: "Your new portfolio has been created successfully.",
      });
    } catch (error) {
      console.error('Error creating portfolio:', error);
    }
  };

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Please sign in to access your dashboard</h1>
          <p className="text-muted-foreground">You need to be authenticated to view your portfolios and financial data.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading your dashboard...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4 text-red-600">Error loading dashboard</h1>
          <p className="text-muted-foreground">There was an error loading your data. Please try again later.</p>
        </div>
      </div>
    );
  }

  const totalPortfolioValue = portfolios?.reduce((sum, portfolio) => sum + portfolio.total_value, 0) || 0;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Welcome back, {user.email}</h1>
        <p className="text-muted-foreground">Here's an overview of your investment portfolios</p>
      </div>



      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalPortfolioValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Across all portfolios</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Portfolios</CardTitle>
            <PieChart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{portfolios?.length || 0}</div>
            <p className="text-xs text-muted-foreground">Investment portfolios</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Performance</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12.5%</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
      </div>

      {/* Portfolios Section */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Your Portfolios</h2>
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Portfolio
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Portfolio</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreatePortfolio} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="portfolio-name">Portfolio Name</Label>
                <Input
                  id="portfolio-name"
                  value={newPortfolio.name}
                  onChange={(e) => setNewPortfolio({ ...newPortfolio, name: e.target.value })}
                  placeholder="e.g., Growth Portfolio"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="portfolio-description">Description (Optional)</Label>
                <Textarea
                  id="portfolio-description"
                  value={newPortfolio.description}
                  onChange={(e) => setNewPortfolio({ ...newPortfolio, description: e.target.value })}
                  placeholder="Describe your investment strategy..."
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="initial-value">Initial Value</Label>
                <Input
                  id="initial-value"
                  type="number"
                  value={newPortfolio.total_value}
                  onChange={(e) => setNewPortfolio({ ...newPortfolio, total_value: parseFloat(e.target.value) || 0 })}
                  placeholder="0.00"
                  step="0.01"
                />
              </div>
              <Button type="submit" className="w-full" disabled={createPortfolioMutation.isPending}>
                {createPortfolioMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create Portfolio
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Portfolio Grid */}
      {portfolios && portfolios.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {portfolios.map((portfolio) => (
            <Card key={portfolio.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">{portfolio.name}</CardTitle>
                <CardDescription>{portfolio.description || 'No description'}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600 mb-2">
                  ${portfolio.total_value.toLocaleString()}
                </div>
                <p className="text-sm text-muted-foreground">
                  Created {new Date(portfolio.created_at).toLocaleDateString()}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <PieChart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No portfolios yet</h3>
            <p className="text-muted-foreground mb-4">
              Create your first portfolio to start tracking your investments
            </p>
            <Button onClick={() => setIsCreateModalOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Portfolio
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default Dashboard;
