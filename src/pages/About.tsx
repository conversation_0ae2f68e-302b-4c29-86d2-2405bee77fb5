import React from 'react';
import { usePage } from '@/hooks/useContent';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { Users, Target, Award, TrendingUp, Shield, Heart } from 'lucide-react';

const About: React.FC = () => {
  const { data: aboutPage, isLoading, error } = usePage('about-us');

  if (error) {
    console.error('Error loading about page:', error);
    // Fallback content if no page is found in database
    return <AboutFallback />;
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="space-y-6">
          <Skeleton className="h-12 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (aboutPage) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold mb-6">{aboutPage.title}</h1>
          <div 
            className="prose prose-lg max-w-none"
            dangerouslySetInnerHTML={{ __html: aboutPage.content }}
          />
        </div>
      </div>
    );
  }

  return <AboutFallback />;
};

const AboutFallback: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">About Syed's Investments</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Where vision meets valuation. We're dedicated to empowering investors with accessible, 
          accurate, and Shariah-compliant financial education to make informed investment decisions.
        </p>
      </div>

      {/* Mission & Vision */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-6 w-6 text-blue-600" />
              <CardTitle>Our Mission</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              To democratize financial education and provide every investor with the tools, 
              knowledge, and insights needed to build sustainable wealth through informed 
              investment decisions.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-6 w-6 text-green-600" />
              <CardTitle>Our Vision</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              To become the leading platform for ethical and Shariah-compliant investment 
              education, fostering a community of informed investors who create positive 
              impact through their financial decisions.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Core Values */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-center mb-8">Our Core Values</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="text-center">
            <CardHeader>
              <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <CardTitle>Integrity</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                We maintain the highest standards of honesty and transparency in all our 
                educational content and investment guidance.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Users className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <CardTitle>Community</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                We believe in building a supportive community where investors can learn, 
                share experiences, and grow together.
              </p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Heart className="h-12 w-12 text-red-600 mx-auto mb-4" />
              <CardTitle>Ethical Investing</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                We promote investment strategies that align with Islamic principles and 
                contribute to positive social and environmental impact.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* What We Offer */}
      <div className="mb-12">
        <h2 className="text-3xl font-bold text-center mb-8">What We Offer</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Expert Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-3">
                Get access to professional market analysis and investment insights from 
                experienced financial experts.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Market Analysis</Badge>
                <Badge variant="secondary">Investment Strategies</Badge>
                <Badge variant="secondary">Risk Assessment</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Educational Resources
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-3">
                Comprehensive learning materials designed for investors at every level, 
                from beginners to advanced practitioners.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Beginner Guides</Badge>
                <Badge variant="secondary">Advanced Strategies</Badge>
                <Badge variant="secondary">Case Studies</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Shariah Compliance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-3">
                All our investment recommendations and educational content are carefully 
                reviewed for Shariah compliance.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Halal Investments</Badge>
                <Badge variant="secondary">Islamic Finance</Badge>
                <Badge variant="secondary">Ethical Screening</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Real-Time Updates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-3">
                Stay informed with the latest financial news, market trends, and 
                investment opportunities as they happen.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge variant="secondary">Market News</Badge>
                <Badge variant="secondary">Price Alerts</Badge>
                <Badge variant="secondary">Trend Analysis</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Contact Information */}
      <div className="text-center from-blue-50 to-indigo-50 p-8 rounded-lg">
        <h2 className="text-2xl font-bold mb-4">Get in Touch</h2>
        <p className="text-muted-foreground mb-6">
          Have questions about our platform or need personalized investment guidance? 
          We're here to help you on your investment journey.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Card className="p-4">
            <p className="font-semibold">Email</p>
            <p className="text-muted-foreground"><EMAIL></p>
          </Card>
          <Card className="p-4">
            <p className="font-semibold">Phone</p>
            <p className="text-muted-foreground">+****************</p>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default About;
