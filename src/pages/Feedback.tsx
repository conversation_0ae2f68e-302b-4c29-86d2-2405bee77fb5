import React from 'react';
import Navbar from '@/components/Navbar';
import MarketTicker from '@/components/MarketTicker';
import Footer from '@/components/Footer';
import UserFeedbackForm from '@/components/UserFeedbackForm';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { MessageSquare, Heart, Lightbulb, Bug, Star, Users } from 'lucide-react';

const Feedback: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <MarketTicker />
      <Navbar />
      <main className="flex-grow">
        <div className="container py-8">
          {/* Page Header */}
          <div className="text-center mb-12">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-primary/10 rounded-full">
                <MessageSquare className="h-8 w-8 text-primary" />
              </div>
            </div>
            <h1 className="text-4xl font-bold mb-4">We Value Your Feedback</h1>
            <p className="text-muted-foreground max-w-2xl mx-auto text-lg">
              Your thoughts and suggestions help us improve our platform and provide better financial insights. 
              We read every message and appreciate your input.
            </p>
          </div>

          {/* Feedback Categories Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-2">
                  <Lightbulb className="h-8 w-8 text-yellow-600" />
                </div>
                <CardTitle className="text-lg">Suggestions</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Share ideas for new features, content improvements, or ways to enhance your experience.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-2">
                  <Bug className="h-8 w-8 text-red-600" />
                </div>
                <CardTitle className="text-lg">Technical Issues</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Report bugs, loading problems, or any technical difficulties you've encountered.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="flex justify-center mb-2">
                  <Heart className="h-8 w-8 text-pink-600" />
                </div>
                <CardTitle className="text-lg">General Feedback</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Tell us what you love, what could be better, or share your overall experience.
                </CardDescription>
              </CardContent>
            </Card>
          </div>

          {/* Main Feedback Form */}
          <div className="max-w-4xl mx-auto">
            <UserFeedbackForm />
          </div>

          {/* Additional Information */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5" />
                  <span>Community Driven</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Our platform grows stronger with every piece of feedback. Your insights help us build 
                  features that matter most to our community of investors and financial enthusiasts.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Star className="h-5 w-5" />
                  <span>Response Commitment</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We aim to respond to all feedback within 48 hours. For technical issues, 
                  we'll keep you updated on our progress and resolution timeline.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* FAQ Section */}
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h2>
            <div className="max-w-3xl mx-auto space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">How is my feedback used?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Your feedback directly influences our product roadmap. We analyze all suggestions 
                    and prioritize improvements based on user needs and technical feasibility.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Is my information kept private?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    Yes, your personal information is kept confidential and only used to respond to your 
                    feedback. We never share your details with third parties without your consent.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Can I submit anonymous feedback?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    While we require an email address to respond to your feedback, you can use a 
                    temporary email if you prefer. However, providing your real contact information 
                    helps us follow up with clarifying questions if needed.
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">What happens after I submit feedback?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">
                    You'll receive an automatic confirmation, and our team will review your message. 
                    We'll respond with updates, questions, or solutions based on the nature of your feedback.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Feedback;
