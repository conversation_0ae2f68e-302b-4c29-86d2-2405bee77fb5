import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useIsAdmin } from '@/hooks/useContent';
import { useAuth } from '@/contexts/AuthContext';
import { useSupabaseQuery } from '@/hooks/useSupabaseQuery';
import { supabase } from '@/integrations/supabase/client';
import { Shield, ArrowLeft, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import QuarterlyOutlookForm from '@/components/admin/QuarterlyOutlookForm';

const EditQuarterlyOutlook: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isAdmin = useIsAdmin();

  // Fetch the quarterly outlook data
  const { data: outlookData, isLoading, error } = useSupabaseQuery(
    ['quarterly_outlook', id],
    async () => {
      if (!id) throw new Error('No ID provided');
      return await supabase
        .from('quarterly_outlook')
        .select('*')
        .eq('id', id)
        .single();
    },
    {
      enabled: !!id && !!user && isAdmin,
    }
  );

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
          <p className="text-muted-foreground">Please sign in to access this page.</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4 text-red-600">Access Denied</h1>
          <p className="text-muted-foreground">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">Loading...</h1>
          <p className="text-muted-foreground">Loading quarterly outlook data...</p>
        </div>
      </div>
    );
  }

  if (error || !outlookData) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Button variant="ghost" asChild className="mb-4">
          <Link to="/admin?tab=quarterly-outlook">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Quarterly Outlook
          </Link>
        </Button>
        
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              {error ? 'Failed to load quarterly outlook data.' : 'Quarterly outlook not found.'}
            </p>
            <Button asChild className="mt-4">
              <Link to="/admin?tab=quarterly-outlook">
                Back to Quarterly Outlook
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleSuccess = () => {
    navigate('/admin?tab=quarterly-outlook');
  };

  const handleCancel = () => {
    navigate('/admin?tab=quarterly-outlook');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link to="/admin?tab=quarterly-outlook">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Quarterly Outlook
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Edit Quarterly Outlook</h1>
        <p className="text-muted-foreground mt-2">
          Edit the quarterly market outlook and forecast.
        </p>
      </div>

      <QuarterlyOutlookForm
        mode="edit"
        initialData={outlookData}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default EditQuarterlyOutlook;
