import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useIsAdmin } from '@/hooks/useContent';
import { useAuth } from '@/contexts/AuthContext';
import { Shield, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import QuarterlyOutlookForm from '@/components/admin/QuarterlyOutlookForm';

const CreateQuarterlyOutlook: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const isAdmin = useIsAdmin();

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
          <p className="text-muted-foreground">Please sign in to access this page.</p>
        </div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4 text-red-600">Access Denied</h1>
          <p className="text-muted-foreground">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  const handleSuccess = () => {
    navigate('/admin?tab=quarterly-outlook');
  };

  const handleCancel = () => {
    navigate('/admin?tab=quarterly-outlook');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild className="mb-4">
          <Link to="/admin?tab=quarterly-outlook">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Quarterly Outlook
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">Create Quarterly Outlook</h1>
        <p className="text-muted-foreground mt-2">
          Create a new quarterly market outlook and forecast.
        </p>
      </div>

      <QuarterlyOutlookForm
        mode="create"
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  );
};

export default CreateQuarterlyOutlook;
