import React from 'react';
import { useQuery, useQueries } from '@tanstack/react-query';
import {
  getStockQuote,
  getCompanyProfile,
  getStockCandles,
  getBatchQuotes,
  searchSymbols,
  getMarketNews
} from '@/integrations/finnhub/client';
import {
  StockQuote,
  CompanyProfile,
  StockCandle,
  StockData,
  BatchQuoteResult,
  SymbolSearch,
  MarketNews,
  ChartTimeframe
} from '@/integrations/finnhub/types';
import {
  useIndianStockQuote,
  useBatchIndianStockQuotes,
  useIndianStockSearch
} from '@/hooks/useIndianStockData';
import {
  isIndianStock,
  isUSStock,
  processMixedSymbols,
  normalizeYahooQuote,
  normalizeFinnhubQuote,
  NormalizedQuote
} from '@/utils/stockUtils';
import {
  getCacheConfig,
  getMarketAwareCacheConfig,
  getRetryConfig,
  trackAPIPerformance
} from '@/utils/stockCaching';
import {
  deduplicatedStockQuote,
  deduplicatedCompanyProfile,
  deduplicatedStockCandles,
  deduplicatedMarketNews
} from '@/utils/requestDeduplication';

// Hook for single stock quote with optimized caching
export const useStockQuote = (symbol: string, enabled: boolean = true) => {
  const cacheConfig = getMarketAwareCacheConfig(symbol, 'quote');
  const retryConfig = getRetryConfig(symbol);

  return useQuery<StockQuote>({
    queryKey: ['stock-quote', symbol],
    queryFn: async () => {
      const startTime = Date.now();
      try {
        const result = await deduplicatedStockQuote(symbol, () => getStockQuote(symbol));
        trackAPIPerformance('finnhub', 'quote', Date.now() - startTime, true);
        return result;
      } catch (error) {
        trackAPIPerformance('finnhub', 'quote', Date.now() - startTime, false);
        throw error;
      }
    },
    enabled: enabled && !!symbol,
    ...cacheConfig,
    ...retryConfig,
  });
};

// Hook for company profile - Optimized for non-real-time data with extended caching
export const useCompanyProfile = (symbol: string, enabled: boolean = true) => {
  const query = useQuery<CompanyProfile>({
    queryKey: ['company-profile', symbol],
    queryFn: () => deduplicatedCompanyProfile(symbol, () => getCompanyProfile(symbol)),
    enabled: enabled && !!symbol,
    staleTime: 3600000, // 1 hour (60 * 60 * 1000 ms) - Data stays fresh for 1 hour
    gcTime: 5400000, // 90 minutes (90 * 60 * 1000 ms) - Cache kept for 90 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000), // Exponential backoff
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    refetch: query.refetch,
  };
};

// Hook for stock candles/chart data
export const useStockCandles = (
  symbol: string, 
  timeframe: ChartTimeframe = '1D',
  enabled: boolean = true
) => {
  const getTimeRange = (timeframe: ChartTimeframe) => {
    const now = Math.floor(Date.now() / 1000);
    const ranges = {
      '1D': { resolution: '5', from: now - 86400, to: now },
      '1W': { resolution: '15', from: now - 604800, to: now },
      '1M': { resolution: '60', from: now - 2592000, to: now },
      '3M': { resolution: '240', from: now - 7776000, to: now },
      '6M': { resolution: 'D', from: now - 15552000, to: now },
      '1Y': { resolution: 'D', from: now - 31536000, to: now },
      '5Y': { resolution: 'W', from: now - 157680000, to: now },
    };
    return ranges[timeframe];
  };

  const { resolution, from, to } = getTimeRange(timeframe);

  return useQuery<StockCandle>({
    queryKey: ['stock-candles', symbol, timeframe],
    queryFn: () => getStockCandles(symbol, resolution, from, to),
    enabled: enabled && !!symbol,
    staleTime: 60000, // 1 minute
    retry: 2,
  });
};

// Hook for multiple stock quotes with optimized caching
export const useBatchStockQuotes = (symbols: string[], enabled: boolean = true) => {
  const cacheConfig = getCacheConfig(symbols[0] || '', 'quote');

  return useQuery<BatchQuoteResult[]>({
    queryKey: ['batch-quotes', symbols.sort().join(',')], // Sort for consistent caching
    queryFn: async () => {
      const startTime = Date.now();
      try {
        const result = await getBatchQuotes(symbols);
        trackAPIPerformance('finnhub', 'batch-quotes', Date.now() - startTime, true);
        return result;
      } catch (error) {
        trackAPIPerformance('finnhub', 'batch-quotes', Date.now() - startTime, false);
        throw error;
      }
    },
    enabled: enabled && symbols.length > 0,
    ...cacheConfig,
    retry: 1,
    retryDelay: 2000,
  });
};

// Hook for symbol search - Enhanced caching for search results
export const useSymbolSearch = (query: string, enabled: boolean = true) => {
  // Search Indian stocks via Yahoo Finance
  const { data: indianResults, isLoading: indianLoading, error: indianError } = useIndianStockSearch(
    query,
    enabled && query.length > 2
  );

  // Search US stocks via Finnhub
  const finnhubQuery = useQuery<SymbolSearch>({
    queryKey: ['finnhub-search', query],
    queryFn: () => searchSymbols(query),
    enabled: enabled && query.length > 2,
    staleTime: 1800000, // 30 minutes
    gcTime: 3600000, // 1 hour
    retry: 1,
    retryDelay: 1000,
  });

  // Combine search results
  const combinedResults = React.useMemo(() => {
    const results: any = {
      count: 0,
      result: [],
    };

    // Add Indian stock results
    if (indianResults && indianResults.quotes) {
      const indianQuotes = indianResults.quotes.slice(0, 5).map((quote: any) => ({
        description: quote.longname || quote.shortname || quote.symbol,
        displaySymbol: quote.symbol,
        symbol: quote.symbol,
        type: 'Common Stock',
        currency: 'INR',
        exchange: quote.exchange || (quote.symbol.endsWith('.NS') ? 'NSE' : 'BSE'),
      }));
      results.result.push(...indianQuotes);
      results.count += indianQuotes.length;
    }

    // Add US stock results
    if (finnhubQuery.data && finnhubQuery.data.result) {
      const usQuotes = finnhubQuery.data.result.slice(0, 5);
      results.result.push(...usQuotes);
      results.count += usQuotes.length;
    }

    return results.count > 0 ? results : null;
  }, [indianResults, finnhubQuery.data]);

  return {
    data: combinedResults,
    isLoading: indianLoading || finnhubQuery.isLoading,
    isError: indianError || finnhubQuery.isError,
    error: indianError || finnhubQuery.error,
    isSuccess: !indianError && !finnhubQuery.isError && !!combinedResults,
    isFetching: finnhubQuery.isFetching,
    refetch: () => {
      finnhubQuery.refetch();
    },
  };
};

// Hook for market news - Balanced caching for news data
export const useMarketNews = (category: string = 'general', enabled: boolean = true) => {
  const query = useQuery<MarketNews[]>({
    queryKey: ['market-news', category],
    queryFn: () => deduplicatedMarketNews(category, () => getMarketNews(category)),
    enabled: enabled,
    staleTime: 900000, // 15 minutes - News updates moderately frequently
    gcTime: 1800000, // 30 minutes - Keep news cached for reasonable time
    refetchInterval: 900000, // Auto-refresh every 15 minutes
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return {
    data: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    refetch: query.refetch,
  };
};

// Hook for watchlist stocks
export const useWatchlistQuotes = (symbols: string[]) => {
  return useQueries({
    queries: symbols.map(symbol => ({
      queryKey: ['watchlist-quote', symbol],
      queryFn: () => getStockQuote(symbol),
      staleTime: 30000,
      refetchInterval: 30000,
      retry: 1,
    })),
  });
};

// Utility hook to transform raw quote data to StockData format
export const useFormattedStockData = (symbol: string, name?: string) => {
  const { data: quote, ...queryResult } = useStockQuote(symbol);
  const { data: profile } = useCompanyProfile(symbol);

  const formattedData: StockData | undefined = quote ? {
    symbol,
    name: name || profile?.name || symbol,
    price: quote.c,
    change: quote.d,
    changePercent: quote.dp,
    high: quote.h,
    low: quote.l,
    open: quote.o,
    previousClose: quote.pc,
    timestamp: quote.t,
    currency: profile?.currency,
    exchange: profile?.exchange,
  } : undefined;

  return {
    data: formattedData,
    ...queryResult,
  };
};

// Hook for market indices - Using ETFs as proxies since free tier doesn't support indices
export const useMarketIndices = () => {
  const indices = [
    { symbol: 'SPY', name: 'S&P 500 ETF' },
    { symbol: 'QQQ', name: 'NASDAQ ETF' },
    { symbol: 'DIA', name: 'DOW JONES ETF' },
    { symbol: 'VTI', name: 'Total Stock Market ETF' },
    { symbol: 'IWM', name: 'Russell 2000 ETF' },
  ];

  return useQueries({
    queries: indices.map(index => ({
      queryKey: ['market-index', index.symbol],
      queryFn: () => getStockQuote(index.symbol),
      staleTime: 30000,
      refetchInterval: 30000,
      retry: 2,
      select: (data: StockQuote | null) => {
        if (!data) return null;

        return {
          symbol: index.symbol,
          name: index.name,
          value: data.c || 0,
          change: data.d || 0,
          changePercent: data.dp || 0,
          isPositive: (data.d || 0) >= 0,
          high: data.h || 0,
          low: data.l || 0,
          open: data.o || 0,
          previousClose: data.pc || 0,
        };
      },
    })),
  });
};

// Hook for popular stocks (Indian + US) - properly separated by API
export const usePopularStocks = () => {
  const indianSymbols = [
    'RELIANCE.NS',
    'TCS.NS',
    'HDFCBANK.NS',
    'INFY.NS',
    'HINDUNILVR.NS',
  ];

  const usSymbols = [
    'AAPL',
    'GOOGL',
    'MSFT',
    'AMZN',
    'TSLA',
  ];

  // Fetch Indian stocks from Yahoo Finance
  const { data: indianData, isLoading: indianLoading, error: indianError } = useBatchIndianStockQuotes(indianSymbols);

  // Fetch US stocks from Finnhub
  const { data: usData, isLoading: usLoading, error: usError } = useBatchStockQuotes(usSymbols);

  // Combine and normalize the data
  const combinedData = React.useMemo(() => {
    const results: BatchQuoteResult[] = [];

    // Add Indian stock results (normalized)
    if (indianData) {
      indianData.forEach(item => {
        if (item.data) {
          results.push({
            symbol: item.symbol,
            data: normalizeYahooQuote(item.data) as any,
            error: item.error,
          });
        } else {
          results.push({
            symbol: item.symbol,
            data: null,
            error: item.error || new Error('No data available'),
          });
        }
      });
    }

    // Add US stock results
    if (usData) {
      results.push(...usData);
    }

    return results;
  }, [indianData, usData]);

  return {
    data: combinedData,
    isLoading: indianLoading || usLoading,
    error: indianError || usError,
    isSuccess: !indianError && !usError && combinedData.length > 0,
  };
};

// Custom hook for real-time price updates
export const useRealTimePrice = (symbol: string) => {
  const { data, isLoading, error, refetch } = useStockQuote(symbol);

  // You can extend this to use WebSocket for real-time updates
  // For now, it uses polling every 30 seconds

  return {
    price: data?.c,
    change: data?.d,
    changePercent: data?.dp,
    isLoading,
    error,
    refresh: refetch,
  };
};

// Enhanced hook for company fundamentals with maximum caching
// This is ideal for data that rarely changes like company basic info, sector, etc.
export const useCompanyFundamentals = (symbol: string, enabled: boolean = true) => {
  const query = useQuery<CompanyProfile>({
    queryKey: ['company-fundamentals', symbol],
    queryFn: () => getCompanyProfile(symbol),
    enabled: enabled && !!symbol,
    staleTime: 3600000, // 1 hour - Company fundamentals change very rarely
    gcTime: 5400000, // 90 minutes - Keep in cache for extended period
    retry: 3, // More retries for important fundamental data
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    refetchOnWindowFocus: false, // Don't refetch on window focus for static data
    refetchOnReconnect: false, // Don't refetch on reconnect for static data
  });

  return {
    // Core data
    data: query.data,

    // Loading states
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isSuccess: query.isSuccess,

    // Error states
    isError: query.isError,
    error: query.error,

    // Actions
    refetch: query.refetch,

    // Convenience getters for common company info
    companyName: query.data?.name,
    sector: query.data?.finnhubIndustry,
    country: query.data?.country,
    exchange: query.data?.exchange,
    currency: query.data?.currency,
    marketCap: query.data?.marketCapitalization,
    website: query.data?.weburl,
    logo: query.data?.logo,
  };
};
