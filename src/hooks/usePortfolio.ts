import { useSupabaseQuery, useSupabaseMutation } from './useSupabaseQuery';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';

// Types for portfolio data
export interface Portfolio {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  total_value: number;
  created_at: string;
  updated_at: string;
}

export interface PortfolioHolding {
  id: string;
  portfolio_id: string;
  symbol: string;
  quantity: number;
  average_cost: number;
  current_price: number;
  created_at: string;
  updated_at: string;
}

// Hook to get user's portfolios
export const usePortfolios = () => {
  const { user } = useAuth();
  
  return useSupabaseQuery(
    ['portfolios', user?.id],
    async () => {
      return await supabase
        .from('portfolios')
        .select('*')
        .eq('user_id', user?.id)
        .order('created_at', { ascending: false });
    },
    {
      enabled: !!user,
    }
  );
};

// Hook to get a specific portfolio
export const usePortfolio = (portfolioId: string) => {
  return useSupabaseQuery(
    ['portfolio', portfolioId],
    async () => {
      return await supabase
        .from('portfolios')
        .select('*')
        .eq('id', portfolioId)
        .single();
    },
    {
      enabled: !!portfolioId,
    }
  );
};

// Hook to get portfolio holdings
export const usePortfolioHoldings = (portfolioId: string) => {
  return useSupabaseQuery(
    ['portfolio-holdings', portfolioId],
    async () => {
      return await supabase
        .from('portfolio_holdings')
        .select('*')
        .eq('portfolio_id', portfolioId)
        .order('created_at', { ascending: false });
    },
    {
      enabled: !!portfolioId,
    }
  );
};

// Hook to create a new portfolio
export const useCreatePortfolio = () => {
  const { user } = useAuth();
  
  return useSupabaseMutation(
    async (portfolio: Omit<Portfolio, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
      return await supabase
        .from('portfolios')
        .insert([
          {
            ...portfolio,
            user_id: user?.id,
          },
        ])
        .select()
        .single();
    },
    {
      invalidateQueries: [['portfolios', user?.id]],
    }
  );
};

// Hook to update a portfolio
export const useUpdatePortfolio = () => {
  const { user } = useAuth();
  
  return useSupabaseMutation(
    async ({ id, ...updates }: Partial<Portfolio> & { id: string }) => {
      return await supabase
        .from('portfolios')
        .update(updates)
        .eq('id', id)
        .eq('user_id', user?.id)
        .select()
        .single();
    },
    {
      invalidateQueries: [['portfolios', user?.id]],
    }
  );
};

// Hook to delete a portfolio
export const useDeletePortfolio = () => {
  const { user } = useAuth();
  
  return useSupabaseMutation(
    async (portfolioId: string) => {
      return await supabase
        .from('portfolios')
        .delete()
        .eq('id', portfolioId)
        .eq('user_id', user?.id);
    },
    {
      invalidateQueries: [['portfolios', user?.id]],
    }
  );
};

// Hook to add a holding to a portfolio
export const useAddHolding = () => {
  return useSupabaseMutation(
    async (holding: Omit<PortfolioHolding, 'id' | 'created_at' | 'updated_at'>) => {
      return await supabase
        .from('portfolio_holdings')
        .insert([holding])
        .select()
        .single();
    },
    {
      invalidateQueries: [['portfolio-holdings']],
    }
  );
};

// Hook to update a holding
export const useUpdateHolding = () => {
  return useSupabaseMutation(
    async ({ id, ...updates }: Partial<PortfolioHolding> & { id: string }) => {
      return await supabase
        .from('portfolio_holdings')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
    },
    {
      invalidateQueries: [['portfolio-holdings']],
    }
  );
};

// Hook to delete a holding
export const useDeleteHolding = () => {
  return useSupabaseMutation(
    async (holdingId: string) => {
      return await supabase
        .from('portfolio_holdings')
        .delete()
        .eq('id', holdingId);
    },
    {
      invalidateQueries: [['portfolio-holdings']],
    }
  );
};
