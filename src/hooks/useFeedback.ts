import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

type FeedbackRow = Database['public']['Tables']['user_feedback']['Row'];
type FeedbackInsert = Database['public']['Tables']['user_feedback']['Insert'];
type FeedbackUpdate = Database['public']['Tables']['user_feedback']['Update'];

export interface FeedbackSubmission {
  name: string;
  email: string;
  subject: string;
  category: 'general' | 'content' | 'technical' | 'suggestion' | 'complaint' | 'compliment';
  message: string;
  rating?: number | null;
}

// Helper function to get user agent and IP (for analytics)
const getBrowserInfo = () => {
  return {
    user_agent: typeof window !== 'undefined' ? window.navigator.userAgent : null,
    // Note: IP address will be handled server-side for privacy
    ip_address: null,
  };
};

// Submit feedback
export const useSubmitFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (feedbackData: FeedbackSubmission): Promise<FeedbackRow> => {
      const browserInfo = getBrowserInfo();
      
      const insertData: FeedbackInsert = {
        name: feedbackData.name.trim(),
        email: feedbackData.email.trim().toLowerCase(),
        subject: feedbackData.subject.trim(),
        category: feedbackData.category,
        message: feedbackData.message.trim(),
        rating: feedbackData.rating,
        ...browserInfo,
        status: 'new',
      };

      const { data, error } = await supabase
        .from('user_feedback')
        .insert(insertData)
        .select()
        .single();

      if (error) {
        console.error('Error submitting feedback:', error);
        throw new Error(error.message || 'Failed to submit feedback');
      }

      return data;
    },
    onSuccess: () => {
      // Invalidate admin feedback queries if they exist
      queryClient.invalidateQueries({ queryKey: ['admin-feedback'] });
      queryClient.invalidateQueries({ queryKey: ['feedback-stats'] });
    },
  });
};

// Admin hooks for managing feedback
export const useAdminFeedback = (options?: {
  status?: FeedbackRow['status'];
  category?: FeedbackRow['category'];
  limit?: number;
  offset?: number;
}) => {
  return useQuery({
    queryKey: ['admin-feedback', options],
    queryFn: async (): Promise<FeedbackRow[]> => {
      let query = supabase
        .from('user_feedback')
        .select('*')
        .order('created_at', { ascending: false });

      if (options?.status) {
        query = query.eq('status', options.status);
      }

      if (options?.category) {
        query = query.eq('category', options.category);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching feedback:', error);
        throw new Error(error.message || 'Failed to fetch feedback');
      }

      return data || [];
    },
    // Only enable for authenticated users (admin check will be handled by RLS)
    enabled: true,
  });
};

// Update feedback status (admin only)
export const useUpdateFeedbackStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      id,
      status,
      adminNotes,
      resolvedBy,
    }: {
      id: string;
      status: FeedbackRow['status'];
      adminNotes?: string;
      resolvedBy?: string;
    }): Promise<FeedbackRow> => {
      const updateData: FeedbackUpdate = {
        status,
        admin_notes: adminNotes,
        resolved_by: resolvedBy,
        resolved_at: status === 'resolved' || status === 'closed' ? new Date().toISOString() : null,
      };

      const { data, error } = await supabase
        .from('user_feedback')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error('Error updating feedback:', error);
        throw new Error(error.message || 'Failed to update feedback');
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-feedback'] });
      queryClient.invalidateQueries({ queryKey: ['feedback-stats'] });
    },
  });
};

// Get feedback statistics (admin only)
export const useFeedbackStats = () => {
  return useQuery({
    queryKey: ['feedback-stats'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_feedback_stats');

      if (error) {
        console.error('Error fetching feedback stats:', error);
        throw new Error(error.message || 'Failed to fetch feedback statistics');
      }

      return data;
    },
  });
};

// Get single feedback item (admin only)
export const useFeedbackItem = (id: string) => {
  return useQuery({
    queryKey: ['feedback-item', id],
    queryFn: async (): Promise<FeedbackRow> => {
      const { data, error } = await supabase
        .from('user_feedback')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching feedback item:', error);
        throw new Error(error.message || 'Failed to fetch feedback item');
      }

      return data;
    },
    enabled: !!id,
  });
};

// Delete feedback (admin only)
export const useDeleteFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const { error } = await supabase
        .from('user_feedback')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting feedback:', error);
        throw new Error(error.message || 'Failed to delete feedback');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-feedback'] });
      queryClient.invalidateQueries({ queryKey: ['feedback-stats'] });
    },
  });
};

// Utility function to format feedback for display
export const formatFeedbackForDisplay = (feedback: FeedbackRow) => {
  return {
    ...feedback,
    formattedDate: new Date(feedback.created_at).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }),
    categoryLabel: getCategoryLabel(feedback.category),
    statusLabel: getStatusLabel(feedback.status),
    ratingStars: feedback.rating ? '★'.repeat(feedback.rating) + '☆'.repeat(5 - feedback.rating) : null,
  };
};

// Helper functions for labels
const getCategoryLabel = (category: FeedbackRow['category']): string => {
  const labels = {
    general: 'General Inquiry',
    content: 'Content Feedback',
    technical: 'Technical Issue',
    suggestion: 'Suggestion',
    complaint: 'Complaint',
    compliment: 'Compliment',
  };
  return labels[category] || category;
};

const getStatusLabel = (status: FeedbackRow['status']): string => {
  const labels = {
    new: 'New',
    in_progress: 'In Progress',
    resolved: 'Resolved',
    closed: 'Closed',
  };
  return labels[status] || status;
};

// Export types for use in components
export type { FeedbackRow, FeedbackInsert, FeedbackUpdate };
