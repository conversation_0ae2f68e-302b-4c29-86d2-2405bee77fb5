import { useQuery } from '@tanstack/react-query';
import { 
  getIndianStockQuote, 
  getBatchIndianStockQuotes, 
  getIndianMarketIndices,
  searchIndianStocks,
  getIndianStockHistory,
  areIndianMarketsOpen
} from '@/integrations/yahoo-finance/api';
import { 
  IndianStockQuote, 
  IndianMarketIndex, 
  BatchIndianQuoteResult,
  ChartTimeframe 
} from '@/integrations/yahoo-finance/types';
import {
  fallbackIndianStocks,
  fallbackIndianIndices,
  getFallbackStockData,
  getFallbackStocksData
} from '@/integrations/yahoo-finance/fallback';
import {
  getCacheConfig,
  getMarketAwareCacheConfig,
  getRetryConfig,
  trackAPIPerformance
} from '@/utils/stockCaching';
import {
  deduplicatedStockQuote,
  deduplicatedStockCandles
} from '@/utils/requestDeduplication';

// Hook for single Indian stock quote with optimized caching and fallback
export const useIndianStockQuote = (symbol: string, enabled: boolean = true) => {
  const cacheConfig = getMarketAwareCacheConfig(symbol, 'quote');
  const retryConfig = getRetryConfig(symbol);

  return useQuery<IndianStockQuote>({
    queryKey: ['indian-stock-quote', symbol],
    queryFn: async () => {
      const startTime = Date.now();
      try {
        const result = await deduplicatedStockQuote(symbol, () => getIndianStockQuote(symbol));
        trackAPIPerformance('yahoo', 'quote', Date.now() - startTime, true);
        return result;
      } catch (error) {
        trackAPIPerformance('yahoo', 'quote', Date.now() - startTime, false);
        throw error;
      }
    },
    enabled: enabled && !!symbol,
    ...cacheConfig,
    ...retryConfig,
    placeholderData: getFallbackStockData(symbol) || undefined,
    meta: {
      errorMessage: `Failed to fetch quote for ${symbol}`,
    },
  });
};

// Hook for single Indian stock quote with guaranteed fallback
export const useIndianStockQuoteWithFallback = (symbol: string, enabled: boolean = true) => {
  const query = useIndianStockQuote(symbol, enabled);
  
  return {
    ...query,
    data: query.data || getFallbackStockData(symbol),
    isUsingFallback: !query.data && !query.isLoading,
    hasRealData: !!query.data,
  };
};

// Hook for multiple Indian stock quotes with error handling
export const useBatchIndianStockQuotes = (symbols: string[], enabled: boolean = true) => {
  const isMarketOpen = areIndianMarketsOpen();
  
  return useQuery<BatchIndianQuoteResult[]>({
    queryKey: ['batch-indian-quotes', symbols.sort().join(',')],
    queryFn: () => getBatchIndianStockQuotes(symbols),
    enabled: enabled && symbols.length > 0,
    staleTime: isMarketOpen ? 30000 : 300000,
    refetchInterval: isMarketOpen ? 30000 : 300000,
    retry: 1,
    retryDelay: 2000,
    placeholderData: symbols.map(symbol => ({
      symbol,
      data: getFallbackStockData(symbol),
      error: null,
    })),
    meta: {
      errorMessage: 'Failed to fetch batch stock quotes',
    },
  });
};

// Hook for batch quotes with guaranteed data (using fallback when needed)
export const useBatchIndianStockQuotesWithFallback = (symbols: string[], enabled: boolean = true) => {
  const query = useBatchIndianStockQuotes(symbols, enabled);
  
  const processedData = React.useMemo(() => {
    if (!query.data) {
      return symbols.map(symbol => ({
        symbol,
        data: getFallbackStockData(symbol),
        error: null,
        isUsingFallback: true,
      }));
    }

    return query.data.map(result => ({
      ...result,
      data: result.data || getFallbackStockData(result.symbol),
      isUsingFallback: !result.data,
    }));
  }, [query.data, symbols]);

  return {
    ...query,
    data: processedData,
    successfulQuotes: processedData.filter(item => item.data && !item.isUsingFallback),
    fallbackQuotes: processedData.filter(item => item.isUsingFallback),
  };
};

// Hook for Indian market indices with fallback
export const useIndianMarketIndices = () => {
  const isMarketOpen = areIndianMarketsOpen();
  
  return useQuery<IndianMarketIndex[]>({
    queryKey: ['indian-market-indices'],
    queryFn: getIndianMarketIndices,
    staleTime: isMarketOpen ? 60000 : 300000, // 1 minute if market open, 5 minutes if closed
    refetchInterval: isMarketOpen ? 60000 : 300000,
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    placeholderData: fallbackIndianIndices,
    meta: {
      errorMessage: 'Failed to fetch Indian market indices',
    },
  });
};

// Hook for Indian market indices with guaranteed fallback
export const useIndianMarketIndicesWithFallback = () => {
  const query = useIndianMarketIndices();
  
  return {
    ...query,
    data: query.data && query.data.length > 0 ? query.data : fallbackIndianIndices,
    isUsingFallback: !query.data || query.data.length === 0,
    hasRealData: query.data && query.data.length > 0,
  };
};

// Hook for searching Indian stocks
export const useIndianStockSearch = (query: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: ['indian-stock-search', query.toLowerCase().trim()],
    queryFn: () => searchIndianStocks(query),
    enabled: enabled && query.length > 2,
    staleTime: 300000, // 5 minutes
    retry: 1,
    retryDelay: 2000,
    meta: {
      errorMessage: `Failed to search for "${query}"`,
    },
  });
};

// Hook for Indian stock historical data
export const useIndianStockHistory = (
  symbol: string, 
  period: ChartTimeframe = '1mo',
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: ['indian-stock-history', symbol, period],
    queryFn: () => deduplicatedStockCandles(symbol, period, () => getIndianStockHistory(symbol, period)),
    enabled: enabled && !!symbol,
    staleTime: 300000, // 5 minutes
    retry: 1,
    retryDelay: 2000,
    meta: {
      errorMessage: `Failed to fetch historical data for ${symbol}`,
    },
  });
};

// Hook for popular Indian stocks (top 10)
export const usePopularIndianStocks = () => {
  const symbols = [
    'RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'HINDUNILVR.NS',
    'ICICIBANK.NS', 'BHARTIARTL.NS', 'ITC.NS', 'KOTAKBANK.NS', 'LT.NS'
  ];
  
  return useBatchIndianStockQuotesWithFallback(symbols);
};

// Hook for market status
export const useIndianMarketStatus = () => {
  return useQuery({
    queryKey: ['indian-market-status'],
    queryFn: () => ({
      isOpen: areIndianMarketsOpen(),
      timestamp: Date.now(),
    }),
    staleTime: 60000, // 1 minute
    refetchInterval: 60000, // Check every minute
  });
};

// Custom hook for real-time updates (when market is open)
export const useRealTimeIndianStocks = (symbols: string[]) => {
  const isMarketOpen = areIndianMarketsOpen();
  
  return useBatchIndianStockQuotesWithFallback(symbols, true);
};

// Hook for getting top gainers/losers (using fallback data for now)
export const useIndianStockMovers = () => {
  const { data: stocksData } = usePopularIndianStocks();
  
  return React.useMemo(() => {
    if (!stocksData) return { gainers: [], losers: [] };
    
    const validStocks = stocksData
      .filter(item => item.data)
      .map(item => item.data!)
      .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent));
    
    const gainers = validStocks
      .filter(stock => stock.changePercent > 0)
      .slice(0, 5);
    
    const losers = validStocks
      .filter(stock => stock.changePercent < 0)
      .slice(0, 5);
    
    return { gainers, losers };
  }, [stocksData]);
};

// React import for useMemo
import React from 'react';
