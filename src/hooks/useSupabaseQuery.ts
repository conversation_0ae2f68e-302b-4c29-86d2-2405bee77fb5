import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

// Generic hook for Supabase queries
export const useSupabaseQuery = <T>(
  queryKey: string[],
  queryFn: () => Promise<{ data: T | null; error: any }>,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    refetchOnWindowFocus?: boolean;
  }
) => {
  return useQuery({
    queryKey,
    queryFn: async () => {
      const { data, error } = await queryFn();
      if (error) {
        toast({
          title: "Error fetching data",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data;
    },
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: options?.refetchOnWindowFocus ?? false,
    enabled: options?.enabled ?? true,
  });
};

// Generic hook for Supabase mutations
export const useSupabaseMutation = <T, V>(
  mutationFn: (variables: V) => Promise<{ data: T | null; error: any }>,
  options?: {
    onSuccess?: (data: T | null) => void;
    onError?: (error: any) => void;
    invalidateQueries?: string[][];
  }
) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (variables: V) => {
      const { data, error } = await mutationFn(variables);
      if (error) {
        toast({
          title: "Operation failed",
          description: error.message,
          variant: "destructive",
        });
        throw error;
      }
      return data;
    },
    onSuccess: (data) => {
      if (options?.invalidateQueries) {
        options.invalidateQueries.forEach((queryKey) => {
          queryClient.invalidateQueries({ queryKey });
        });
      }
      options?.onSuccess?.(data);
    },
    onError: (error) => {
      options?.onError?.(error);
    },
  });
};

// Hook for real-time subscriptions
export const useSupabaseSubscription = <T>(
  table: string,
  callback: (payload: any) => void,
  filter?: string
) => {
  const queryClient = useQueryClient();

  const subscription = supabase
    .channel(`${table}_changes`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table,
        filter,
      },
      (payload) => {
        callback(payload);
        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: [table] });
      }
    )
    .subscribe();

  return () => {
    subscription.unsubscribe();
  };
};
