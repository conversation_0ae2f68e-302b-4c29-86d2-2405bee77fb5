import { useState, useEffect } from 'react';

interface UseSplashScreenOptions {
  duration?: number;
}

export const useSplashScreen = (options: UseSplashScreenOptions = {}) => {
  const { duration = 6000 } = options;
  const [showSplash, setShowSplash] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSplash(false);
    }, duration);

    return () => clearTimeout(timer);
  }, [duration]);

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  return {
    showSplash,
    handleSplashComplete,
    isAppReady: !showSplash
  };
};
