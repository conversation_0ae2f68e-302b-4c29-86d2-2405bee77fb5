import { useSupabaseQuery, useSupabaseMutation } from './useSupabaseQuery';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Database } from '@/integrations/supabase/types';

type ContentType = 'insights' | 'news' | 'education' | 'case_studies';
type ContentRow<T extends ContentType> = Database['public']['Tables'][T]['Row'];
type ContentInsert<T extends ContentType> = Database['public']['Tables'][T]['Insert'];
type ContentUpdate<T extends ContentType> = Database['public']['Tables'][T]['Update'];

// Generic hook for fetching published content
export const usePublishedContent = <T extends ContentType>(
  contentType: T,
  options?: {
    category?: string;
    tags?: string[];
    limit?: number;
    search?: string;
  }
) => {
  return useSupabaseQuery(
    [contentType, 'published', options?.category, options?.tags?.join(','), options?.limit?.toString(), options?.search],
    async () => {
      let query = supabase
        .from(contentType)
        .select('*')
        .eq('status', 'published')
        .order('published_at', { ascending: false });

      if (options?.category) {
        query = query.eq('category', options.category);
      }

      if (options?.tags && options.tags.length > 0) {
        query = query.overlaps('tags', options.tags);
      }

      if (options?.search) {
        query = query.or(`title.ilike.%${options.search}%,content.ilike.%${options.search}%`);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      return await query;
    }
  );
};

// Hook for fetching a single content item (published only)
export const useContentItem = <T extends ContentType>(
  contentType: T,
  id: string
) => {
  return useSupabaseQuery(
    [contentType, id],
    async () => {
      return await supabase
        .from(contentType)
        .select('*')
        .eq('id', id)
        .eq('status', 'published')
        .single();
    },
    {
      enabled: !!id,
    }
  );
};

// Hook for fetching a single content item for admin (any status)
export const useAdminContentItem = <T extends ContentType>(
  contentType: T,
  id: string
) => {
  return useSupabaseQuery(
    [contentType, 'admin', id],
    async () => {
      return await supabase
        .from(contentType)
        .select('*')
        .eq('id', id)
        .single();
    },
    {
      enabled: !!id,
    }
  );
};

// Admin hook for fetching all content (including drafts)
export const useAdminContent = <T extends ContentType>(
  contentType: T,
  options?: {
    status?: 'draft' | 'published';
    limit?: number;
  }
) => {
  const { user } = useAuth();
  
  return useSupabaseQuery(
    [contentType, 'admin', options?.status, options?.limit?.toString()],
    async () => {
      let query = supabase
        .from(contentType)
        .select('*')
        .order('created_at', { ascending: false });

      if (options?.status) {
        query = query.eq('status', options.status);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      return await query;
    },
    {
      enabled: !!user,
    }
  );
};

// Hook for creating content
export const useCreateContent = <T extends ContentType>(contentType: T) => {
  const { user } = useAuth();
  
  return useSupabaseMutation(
    async (content: Omit<ContentInsert<T>, 'author_id'>) => {
      return await supabase
        .from(contentType)
        .insert([
          {
            ...content,
            author_id: user?.id,
          } as ContentInsert<T>,
        ])
        .select()
        .single();
    },
    {
      invalidateQueries: [[contentType]],
    }
  );
};

// Hook for updating content
export const useUpdateContent = <T extends ContentType>(contentType: T) => {
  return useSupabaseMutation(
    async ({ id, ...updates }: ContentUpdate<T> & { id: string }) => {
      return await supabase
        .from(contentType)
        .update(updates)
        .eq('id', id)
        .select()
        .single();
    },
    {
      invalidateQueries: [[contentType]],
    }
  );
};

// Hook for deleting content
export const useDeleteContent = <T extends ContentType>(contentType: T) => {
  return useSupabaseMutation(
    async (id: string) => {
      return await supabase
        .from(contentType)
        .delete()
        .eq('id', id);
    },
    {
      invalidateQueries: [[contentType]],
    }
  );
};

// Hook for fetching content categories
export const useContentCategories = <T extends ContentType>(contentType: T) => {
  return useSupabaseQuery(
    [contentType, 'categories'],
    async () => {
      const { data } = await supabase
        .from(contentType)
        .select('category')
        .not('category', 'is', null)
        .eq('status', 'published');
      
      const categories = [...new Set(data?.map(item => item.category).filter(Boolean))];
      return { data: categories, error: null };
    }
  );
};

// Hook for user profile and role management
export const useUserProfile = (userId?: string) => {
  return useSupabaseQuery(
    ['user_profile', userId],
    async () => {
      return await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();
    },
    {
      enabled: !!userId,
    }
  );
};

// Hook for checking if user is admin
export const useIsAdmin = () => {
  const { user } = useAuth();
  const { data: profile } = useUserProfile(user?.id);
  
  return profile?.role === 'admin' || profile?.role === 'editor';
};

// Hook for pages content
export const usePage = (slug: string) => {
  return useSupabaseQuery(
    ['pages', slug],
    async () => {
      // Return the full Supabase response format that useSupabaseQuery expects
      return await supabase
        .from('pages')
        .select('*')
        .eq('slug', slug)
        .eq('status', 'published')
        .maybeSingle(); // Use maybeSingle() instead of single() to handle no results gracefully
    },
    {
      enabled: !!slug,
    }
  );
};

// Hook for creating/updating pages
export const useCreatePage = () => {
  const { user } = useAuth();
  
  return useSupabaseMutation(
    async (page: Omit<Database['public']['Tables']['pages']['Insert'], 'author_id'>) => {
      return await supabase
        .from('pages')
        .insert([
          {
            ...page,
            author_id: user?.id,
          },
        ])
        .select()
        .single();
    },
    {
      invalidateQueries: [['pages']],
    }
  );
};

export const useUpdatePage = () => {
  return useSupabaseMutation(
    async ({ id, ...updates }: Database['public']['Tables']['pages']['Update'] & { id: string }) => {
      return await supabase
        .from('pages')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
    },
    {
      invalidateQueries: [['pages']],
    }
  );
};

// Hook for admin to fetch all pages (including drafts)
export const useAdminPages = () => {
  const { user } = useAuth();

  return useSupabaseQuery(
    ['pages', 'admin'],
    async () => {
      return await supabase
        .from('pages')
        .select('*')
        .order('created_at', { ascending: false });
    },
    {
      enabled: !!user,
    }
  );
};

// Hook for admin to fetch a single page (any status)
export const useAdminPage = (id: string) => {
  return useSupabaseQuery(
    ['pages', 'admin', id],
    async () => {
      return await supabase
        .from('pages')
        .select('*')
        .eq('id', id)
        .single();
    },
    {
      enabled: !!id,
    }
  );
};

// Hook for deleting pages
export const useDeletePage = () => {
  return useSupabaseMutation(
    async (id: string) => {
      return await supabase
        .from('pages')
        .delete()
        .eq('id', id);
    },
    {
      invalidateQueries: [['pages']],
    }
  );
};

// Hook for fetching quarterly outlook data
export const useQuarterlyOutlook = (options?: {
  market?: 'us' | 'india';
  year?: number;
  quarter?: 'Q1' | 'Q2' | 'Q3' | 'Q4';
  limit?: number;
}) => {
  return useSupabaseQuery(
    ['quarterly_outlook', 'published', options?.market, options?.year?.toString(), options?.quarter, options?.limit?.toString()],
    async () => {
      let query = supabase
        .from('quarterly_outlook')
        .select('*')
        .eq('status', 'published')
        .order('year', { ascending: false })
        .order('quarter', { ascending: false });

      if (options?.market) {
        query = query.eq('market', options.market);
      }

      if (options?.year) {
        query = query.eq('year', options.year);
      }

      if (options?.quarter) {
        query = query.eq('quarter', options.quarter);
      }

      if (options?.limit) {
        query = query.limit(options.limit);
      }

      return await query;
    }
  );
};

// Hook for admin to fetch all quarterly outlook data
export const useAdminQuarterlyOutlook = () => {
  return useSupabaseQuery(
    ['quarterly_outlook', 'admin'],
    async () => {
      return await supabase
        .from('quarterly_outlook')
        .select('*')
        .order('year', { ascending: false })
        .order('quarter', { ascending: false });
    }
  );
};

// Hook for creating quarterly outlook
export const useCreateQuarterlyOutlook = () => {
  const { user } = useAuth();

  return useSupabaseMutation(
    async (outlook: Omit<Database['public']['Tables']['quarterly_outlook']['Insert'], 'author_id'>) => {
      return await supabase
        .from('quarterly_outlook')
        .insert([
          {
            ...outlook,
            author_id: user?.id,
          },
        ])
        .select()
        .single();
    },
    {
      invalidateQueries: [['quarterly_outlook']],
    }
  );
};

// Hook for updating quarterly outlook
export const useUpdateQuarterlyOutlook = () => {
  return useSupabaseMutation(
    async ({ id, ...updates }: Database['public']['Tables']['quarterly_outlook']['Update'] & { id: string }) => {
      return await supabase
        .from('quarterly_outlook')
        .update(updates)
        .eq('id', id)
        .select()
        .single();
    },
    {
      invalidateQueries: [['quarterly_outlook']],
    }
  );
};

// Hook for deleting quarterly outlook
export const useDeleteQuarterlyOutlook = () => {
  return useSupabaseMutation(
    async (id: string) => {
      return await supabase
        .from('quarterly_outlook')
        .delete()
        .eq('id', id);
    },
    {
      invalidateQueries: [['quarterly_outlook']],
    }
  );
};
