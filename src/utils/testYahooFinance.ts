import {
  getIndianStockQuote,
  getBatchIndianStockQuotes,
  getIndianMarketIndices,
  searchIndianStocks,
  areIndianMarketsOpen,
  getNextMarketOpenTime
} from '@/integrations/yahoo-finance/api';
import { TEST_INDIAN_STOCKS, TEST_INDIAN_INDICES } from '@/integrations/yahoo-finance/types';

// Test individual stock quote
export const testSingleStock = async (symbol: string = 'RELIANCE.NS') => {
  try {
    console.log(`🧪 Testing single stock quote for ${symbol}...`);
    const startTime = Date.now();
    
    const quote = await getIndianStockQuote(symbol);
    const endTime = Date.now();
    
    console.log(`✅ ${symbol} Quote (${endTime - startTime}ms):`, {
      symbol: quote.symbol,
      name: quote.name,
      price: quote.price,
      change: quote.change,
      changePercent: quote.changePercent,
      marketState: quote.marketState,
      exchange: quote.exchange,
      currency: quote.currency,
    });
    
    return quote;
  } catch (error) {
    console.error(`❌ Single stock test failed for ${symbol}:`, error);
    throw error;
  }
};

// Test batch quotes
export const testBatchQuotes = async (symbols: string[] = TEST_INDIAN_STOCKS.slice(0, 5)) => {
  try {
    console.log(`🧪 Testing batch quotes for ${symbols.length} symbols...`);
    const startTime = Date.now();
    
    const quotes = await getBatchIndianStockQuotes(symbols);
    const endTime = Date.now();
    
    const successful = quotes.filter(q => q.data && !q.error);
    const failed = quotes.filter(q => q.error);
    
    console.log(`✅ Batch Quotes (${endTime - startTime}ms):`, {
      total: quotes.length,
      successful: successful.length,
      failed: failed.length,
      successRate: `${((successful.length / quotes.length) * 100).toFixed(1)}%`,
    });
    
    successful.forEach(quote => {
      if (quote.data) {
        console.log(`  📈 ${quote.symbol}: ₹${quote.data.price} (${quote.data.changePercent.toFixed(2)}%)`);
      }
    });
    
    if (failed.length > 0) {
      console.warn('❌ Failed quotes:', failed.map(f => f.symbol));
    }
    
    return quotes;
  } catch (error) {
    console.error('❌ Batch quotes test failed:', error);
    throw error;
  }
};

// Test market indices
export const testMarketIndices = async () => {
  try {
    console.log('🧪 Testing market indices...');
    const startTime = Date.now();
    
    const indices = await getIndianMarketIndices();
    const endTime = Date.now();
    
    console.log(`✅ Market Indices (${endTime - startTime}ms):`, {
      count: indices.length,
      indices: indices.map(index => ({
        name: index.name,
        value: index.value,
        change: index.change,
        changePercent: index.changePercent,
        isPositive: index.isPositive,
      })),
    });
    
    return indices;
  } catch (error) {
    console.error('❌ Market indices test failed:', error);
    throw error;
  }
};

// Test search functionality
export const testStockSearch = async (query: string = 'Reliance') => {
  try {
    console.log(`🧪 Testing stock search for "${query}"...`);
    const startTime = Date.now();
    
    const results = await searchIndianStocks(query);
    const endTime = Date.now();
    
    console.log(`✅ Search Results (${endTime - startTime}ms):`, {
      query,
      count: results.count,
      quotes: results.quotes.slice(0, 5).map(quote => ({
        symbol: quote.symbol,
        shortname: quote.shortname,
        exchange: quote.exchange,
        quoteType: quote.quoteType,
      })),
    });
    
    return results;
  } catch (error) {
    console.error(`❌ Stock search test failed for "${query}":`, error);
    throw error;
  }
};



// Test market status
export const testMarketStatus = () => {
  try {
    console.log('🧪 Testing market status...');
    
    const isOpen = areIndianMarketsOpen();
    const nextOpen = getNextMarketOpenTime();
    
    console.log('✅ Market Status:', {
      isOpen,
      status: isOpen ? 'OPEN' : 'CLOSED',
      nextOpen: nextOpen.toLocaleString('en-IN', {
        timeZone: 'Asia/Kolkata',
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      }),
    });
    
    return { isOpen, nextOpen };
  } catch (error) {
    console.error('❌ Market status test failed:', error);
    throw error;
  }
};

// Performance test
export const testPerformance = async () => {
  try {
    console.log('🧪 Running performance tests...');
    
    const tests = [
      { name: 'Single Stock', fn: () => testSingleStock('RELIANCE.NS') },
      { name: 'Batch Quotes (3)', fn: () => testBatchQuotes(['RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS']) },
      { name: 'Market Indices', fn: () => testMarketIndices() },
      { name: 'Search', fn: () => testStockSearch('TCS') },
      { name: 'Historical Data', fn: () => testHistoricalData('INFY.NS', '1d') },
    ];
    
    const results = [];
    
    for (const test of tests) {
      const startTime = Date.now();
      try {
        await test.fn();
        const endTime = Date.now();
        results.push({
          name: test.name,
          duration: endTime - startTime,
          status: 'SUCCESS',
        });
      } catch (error) {
        const endTime = Date.now();
        results.push({
          name: test.name,
          duration: endTime - startTime,
          status: 'FAILED',
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }
    
    console.log('📊 Performance Test Results:');
    results.forEach(result => {
      const status = result.status === 'SUCCESS' ? '✅' : '❌';
      console.log(`  ${status} ${result.name}: ${result.duration}ms`);
      if (result.error) {
        console.log(`    Error: ${result.error}`);
      }
    });
    
    const avgDuration = results.reduce((sum, r) => sum + r.duration, 0) / results.length;
    const successRate = (results.filter(r => r.status === 'SUCCESS').length / results.length) * 100;
    
    console.log(`📈 Summary: ${successRate.toFixed(1)}% success rate, ${avgDuration.toFixed(0)}ms average`);
    
    return results;
  } catch (error) {
    console.error('❌ Performance test failed:', error);
    throw error;
  }
};

// Run all tests
export const runAllTests = async () => {
  console.log('🚀 Starting Yahoo Finance API Tests...');
  console.log('=' .repeat(50));
  
  const testResults = {
    singleStock: null as any,
    batchQuotes: null as any,
    marketIndices: null as any,
    stockSearch: null as any,
    historicalData: null as any,
    marketStatus: null as any,
    performance: null as any,
  };
  
  try {
    // Test market status first
    testResults.marketStatus = testMarketStatus();
    console.log('');
    
    // Test single stock
    testResults.singleStock = await testSingleStock();
    console.log('');
    
    // Test batch quotes
    testResults.batchQuotes = await testBatchQuotes();
    console.log('');
    
    // Test market indices
    testResults.marketIndices = await testMarketIndices();
    console.log('');
    
    // Test search
    testResults.stockSearch = await testStockSearch();
    console.log('');
    
    // Test historical data
    testResults.historicalData = await testHistoricalData();
    console.log('');
    
    // Run performance tests
    testResults.performance = await testPerformance();
    console.log('');
    
    console.log('🎉 All tests completed successfully!');
    console.log('=' .repeat(50));
    
    return testResults;
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    console.log('=' .repeat(50));
    throw error;
  }
};

// Quick test for development
export const quickTest = async () => {
  console.log('⚡ Running quick Yahoo Finance test...');
  
  try {
    const quote = await getIndianStockQuote('RELIANCE.NS');
    console.log('✅ Quick test passed:', {
      symbol: quote.symbol,
      price: quote.price,
      change: quote.changePercent.toFixed(2) + '%',
      marketState: quote.marketState,
    });
    return true;
  } catch (error) {
    console.error('❌ Quick test failed:', error);
    return false;
  }
};
