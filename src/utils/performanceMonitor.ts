/**
 * Simple performance monitoring for API calls
 */

interface PerformanceMetric {
  operation: string;
  duration: number;
  success: boolean;
  timestamp: number;
  details?: any;
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 100; // Keep last 100 metrics

  startTimer(operation: string) {
    return {
      operation,
      startTime: Date.now(),
      end: (success: boolean = true, details?: any) => {
        const duration = Date.now() - Date.now();
        this.recordMetric({
          operation,
          duration: Date.now() - Date.now(),
          success,
          timestamp: Date.now(),
          details
        });
        return duration;
      }
    };
  }

  recordMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log slow operations
    if (metric.duration > 5000) {
      console.warn(`🐌 Slow operation: ${metric.operation} took ${metric.duration}ms`);
    } else if (metric.duration < 1000) {
      console.log(`⚡ Fast operation: ${metric.operation} took ${metric.duration}ms`);
    }
  }

  getStats() {
    const recent = this.metrics.slice(-20); // Last 20 operations
    const successful = recent.filter(m => m.success);
    const failed = recent.filter(m => !m.success);
    
    const avgDuration = successful.length > 0 
      ? successful.reduce((sum, m) => sum + m.duration, 0) / successful.length 
      : 0;

    return {
      totalOperations: recent.length,
      successRate: recent.length > 0 ? (successful.length / recent.length) * 100 : 0,
      averageDuration: Math.round(avgDuration),
      fastOperations: successful.filter(m => m.duration < 2000).length,
      slowOperations: successful.filter(m => m.duration > 5000).length,
      recentMetrics: recent.slice(-5) // Last 5 for debugging
    };
  }

  logStats() {
    const stats = this.getStats();
    console.log('📊 Performance Stats:', stats);
    return stats;
  }
}

export const performanceMonitor = new PerformanceMonitor();

// Helper function for timing API calls
export const withPerformanceTracking = async <T>(
  operation: string,
  fn: () => Promise<T>,
  details?: any
): Promise<T> => {
  const startTime = Date.now();
  
  try {
    const result = await fn();
    const duration = Date.now() - startTime;
    
    performanceMonitor.recordMetric({
      operation,
      duration,
      success: true,
      timestamp: Date.now(),
      details
    });
    
    return result;
  } catch (error) {
    const duration = Date.now() - startTime;
    
    performanceMonitor.recordMetric({
      operation,
      duration,
      success: false,
      timestamp: Date.now(),
      details: { ...details, error: error.message }
    });
    
    throw error;
  }
};

// Make it available in dev console
if (import.meta.env.DEV) {
  (window as any).performanceMonitor = performanceMonitor;
}
