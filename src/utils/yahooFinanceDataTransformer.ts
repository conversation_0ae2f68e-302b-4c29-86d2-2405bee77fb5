/**
 * Yahoo Finance API Data Transformer
 * Transforms Yahoo Finance API response into chart-ready format
 */

export interface YahooFinanceResponse {
  result: Array<{
    meta: {
      currency: string;
      symbol: string;
      exchangeName: string;
      regularMarketPrice: number;
      previousClose: number;
      regularMarketDayHigh: number;
      regularMarketDayLow: number;
      regularMarketVolume: number;
      fiftyTwoWeekHigh: number;
      fiftyTwoWeekLow: number;
      longName: string;
      shortName: string;
    };
    timestamp: number[];
    indicators: {
      quote: Array<{
        open: number[];
        high: number[];
        low: number[];
        close: number[];
        volume: number[];
      }>;
    };
    events?: {
      dividends?: Record<string, { amount: number; date: number }>;
    };
  }>;
  error: null | string;
}

export interface ChartDataPoint {
  timestamp: number;
  date: string;
  time: string;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  change: number;
  changePercent: number;
}

export interface TransformedChartData {
  data: ChartDataPoint[];
  meta: {
    symbol: string;
    currency: string;
    currentPrice: number;
    previousClose: number;
    dayHigh: number;
    dayLow: number;
    volume: number;
    fiftyTwoWeekHigh: number;
    fiftyTwoWeekLow: number;
    companyName: string;
  };
  summary: {
    totalPoints: number;
    timeRange: string;
    firstTimestamp: number;
    lastTimestamp: number;
  };
}

/**
 * Transform Yahoo Finance API response to chart-ready format
 */
export function transformYahooFinanceData(
  response: YahooFinanceResponse
): TransformedChartData | null {
  try {
    // Validate response structure
    if (!response?.result?.[0]) {
      console.error('Invalid Yahoo Finance response: missing result data');
      return null;
    }

    const result = response.result[0];
    const { meta, timestamp, indicators } = result;

    // Validate required data arrays
    if (!timestamp?.length || !indicators?.quote?.[0]) {
      console.error('Invalid Yahoo Finance response: missing timestamp or quote data');
      return null;
    }

    const quote = indicators.quote[0];
    const { open, high, low, close, volume } = quote;

    // Validate all OHLCV arrays have the same length
    const expectedLength = timestamp.length;
    if (
      !open?.length || open.length !== expectedLength ||
      !high?.length || high.length !== expectedLength ||
      !low?.length || low.length !== expectedLength ||
      !close?.length || close.length !== expectedLength ||
      !volume?.length || volume.length !== expectedLength
    ) {
      console.error('Invalid Yahoo Finance response: OHLCV arrays length mismatch');
      return null;
    }

    // Transform data points
    const data: ChartDataPoint[] = timestamp.map((ts, index) => {
      const currentClose = close[index];
      const previousClose = index > 0 ? close[index - 1] : meta.previousClose;
      const change = currentClose - previousClose;
      const changePercent = (change / previousClose) * 100;

      return {
        timestamp: ts,
        date: new Date(ts * 1000).toLocaleDateString(),
        time: new Date(ts * 1000).toLocaleTimeString(),
        open: parseFloat(open[index].toFixed(2)),
        high: parseFloat(high[index].toFixed(2)),
        low: parseFloat(low[index].toFixed(2)),
        close: parseFloat(currentClose.toFixed(2)),
        volume: volume[index],
        change: parseFloat(change.toFixed(2)),
        changePercent: parseFloat(changePercent.toFixed(2))
      };
    });

    // Filter out invalid data points (where any OHLC value is null/undefined)
    const validData = data.filter(point => 
      point.open > 0 && point.high > 0 && point.low > 0 && point.close > 0
    );

    return {
      data: validData,
      meta: {
        symbol: meta.symbol,
        currency: meta.currency,
        currentPrice: meta.regularMarketPrice,
        previousClose: meta.previousClose,
        dayHigh: meta.regularMarketDayHigh,
        dayLow: meta.regularMarketDayLow,
        volume: meta.regularMarketVolume,
        fiftyTwoWeekHigh: meta.fiftyTwoWeekHigh,
        fiftyTwoWeekLow: meta.fiftyTwoWeekLow,
        companyName: meta.longName || meta.shortName
      },
      summary: {
        totalPoints: validData.length,
        timeRange: `${validData[0]?.date} - ${validData[validData.length - 1]?.date}`,
        firstTimestamp: validData[0]?.timestamp || 0,
        lastTimestamp: validData[validData.length - 1]?.timestamp || 0
      }
    };

  } catch (error) {
    console.error('Error transforming Yahoo Finance data:', error);
    return null;
  }
}

/**
 * Yahoo Finance API configuration for different time ranges
 */
export const YAHOO_FINANCE_RANGES = {
  '1d': { range: '1d', interval: '1m', label: '1 Day' },
  '5d': { range: '5d', interval: '5m', label: '5 Days' },
  '1mo': { range: '1mo', interval: '30m', label: '1 Month' },
  '3mo': { range: '3mo', interval: '1h', label: '3 Months' },
  '6mo': { range: '6mo', interval: '1d', label: '6 Months' },
  '1y': { range: '1y', interval: '1d', label: '1 Year' },
  '2y': { range: '2y', interval: '1d', label: '2 Years' },
  '5y': { range: '5y', interval: '1wk', label: '5 Years' },
  '10y': { range: '10y', interval: '1wk', label: '10 Years' },
  'ytd': { range: 'ytd', interval: '1d', label: 'Year to Date' },
  'max': { range: 'max', interval: '1mo', label: 'Maximum' }
} as const;

/**
 * Generate Yahoo Finance API URL for different time ranges
 */
export function generateYahooFinanceUrl(
  symbol: string,
  range: keyof typeof YAHOO_FINANCE_RANGES = '1d'
): string {
  const config = YAHOO_FINANCE_RANGES[range];
  const baseUrl = 'https://query1.finance.yahoo.com/v8/finance/chart';
  return `${baseUrl}/${symbol}?range=${config.range}&interval=${config.interval}`;
}

/**
 * Create sample data for testing when API is unavailable
 */
export function createSampleChartData(range: keyof typeof YAHOO_FINANCE_RANGES = '1d'): TransformedChartData {
  const now = Date.now();
  const data: ChartDataPoint[] = [];

  // Determine data points and time intervals based on range
  const rangeConfig = {
    '1d': { points: 390, intervalMs: 60000 }, // 1 minute intervals
    '5d': { points: 390, intervalMs: 300000 }, // 5 minute intervals
    '1mo': { points: 48, intervalMs: 1800000 }, // 30 minute intervals
    '3mo': { points: 90, intervalMs: 3600000 }, // 1 hour intervals
    '6mo': { points: 180, intervalMs: 86400000 }, // 1 day intervals
    '1y': { points: 365, intervalMs: 86400000 }, // 1 day intervals
    '2y': { points: 104, intervalMs: 604800000 }, // 1 week intervals
    '5y': { points: 260, intervalMs: 604800000 }, // 1 week intervals (5 years)
    '10y': { points: 520, intervalMs: 604800000 }, // 1 week intervals (10 years)
    'ytd': { points: 200, intervalMs: 86400000 }, // 1 day intervals
    'max': { points: 600, intervalMs: 2592000000 } // 1 month intervals
  };

  const config = rangeConfig[range];

  for (let i = 0; i < config.points; i++) {
    const timestamp = Math.floor((now - (config.points - i) * config.intervalMs) / 1000);

    // Create more realistic long-term price trends for 5y data
    const timeProgress = i / config.points;
    const longTermTrend = range === '5y' || range === '10y' || range === 'max'
      ? 1500 + (timeProgress * 400) + Math.sin(timeProgress * 8) * 100 // Long-term growth with cycles
      : 1900 + Math.sin(i * 0.1) * 50; // Short-term fluctuations

    const volatility = Math.random() * 10 - 5;

    data.push({
      timestamp,
      date: new Date(timestamp * 1000).toLocaleDateString(),
      time: new Date(timestamp * 1000).toLocaleTimeString(),
      open: parseFloat((longTermTrend + volatility).toFixed(2)),
      high: parseFloat((longTermTrend + volatility + Math.random() * 5).toFixed(2)),
      low: parseFloat((longTermTrend + volatility - Math.random() * 5).toFixed(2)),
      close: parseFloat((longTermTrend + volatility + Math.random() * 2 - 1).toFixed(2)),
      volume: Math.floor(Math.random() * 50000),
      change: parseFloat((Math.random() * 4 - 2).toFixed(2)),
      changePercent: parseFloat((Math.random() * 0.2 - 0.1).toFixed(2))
    });
  }

  return {
    data,
    meta: {
      symbol: 'SAMPLE.NS',
      currency: 'INR',
      currentPrice: 1900,
      previousClose: 1895,
      dayHigh: 1920,
      dayLow: 1880,
      volume: 1000000,
      fiftyTwoWeekHigh: 2000,
      fiftyTwoWeekLow: 1500,
      companyName: 'Sample Company Limited'
    },
    summary: {
      totalPoints: data.length,
      timeRange: `${data[0].date} - ${data[data.length - 1].date}`,
      firstTimestamp: data[0].timestamp,
      lastTimestamp: data[data.length - 1].timestamp
    }
  };
}

/**
 * Validate if Yahoo Finance response has required chart data
 */
export function validateYahooFinanceResponse(response: any): boolean {
  return !!(
    response?.result?.[0]?.timestamp?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.open?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.high?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.low?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.close?.length &&
    response?.result?.[0]?.indicators?.quote?.[0]?.volume?.length
  );
}
