/**
 * Simple Yahoo Finance Indian Stock Data Test
 * Tests if Yahoo Finance API is working for Indian stocks via our proxy
 */

import { getIndianStockQuote, getIndianMarketIndices } from '@/integrations/yahoo-finance/api';
import { formatINR } from '@/integrations/yahoo-finance/types';

// Test configuration
const TEST_STOCKS = [
  { symbol: 'RELIANCE.NS', name: 'Reliance Industries (NSE)' },
  { symbol: 'TCS.NS', name: 'Tata Consultancy Services (NSE)' },
  { symbol: 'HDFCBANK.NS', name: 'HDFC Bank (NSE)' },
  { symbol: 'INFY.NS', name: 'Infosys (NSE)' },
  { symbol: 'RELIANCE.BO', name: 'Reliance Industries (BSE)' },
];

const TEST_INDICES = [
  { symbol: '^NSEI', name: 'NIFTY 50' },
  { symbol: '^BSESN', name: 'BSE SENSEX' },
];

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  duration: number;
}

interface TestSummary {
  totalTests: number;
  passed: number;
  failed: number;
  successRate: number;
  avgDuration: number;
  results: Array<{
    test: string;
    status: 'PASS' | 'FAIL';
    duration: number;
    error?: string;
  }>;
}

/**
 * Test single Indian stock quote
 */
export const testSingleIndianStock = async (symbol: string, name: string): Promise<TestResult> => {
  const startTime = Date.now();
  
  try {
    console.log(`🧪 Testing ${name} (${symbol})...`);
    
    const quote = await getIndianStockQuote(symbol);
    const duration = Date.now() - startTime;
    
    // Validate response structure
    if (!quote || typeof quote.price !== 'number' || !quote.symbol) {
      throw new Error('Invalid response structure');
    }
    
    // Log success details
    console.log(`✅ ${name} - SUCCESS (${duration}ms)`);
    console.log(`   Price: ${formatINR(quote.price)}`);
    console.log(`   Change: ${quote.change >= 0 ? '+' : ''}${formatINR(quote.change)} (${quote.changePercent.toFixed(2)}%)`);
    console.log(`   Market State: ${quote.marketState}`);
    console.log(`   Exchange: ${quote.exchange}`);
    
    return {
      success: true,
      data: quote,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    console.error(`❌ ${name} - FAILED (${duration}ms)`);
    console.error(`   Error: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
      duration
    };
  }
};

/**
 * Test Indian market indices
 */
export const testIndianMarketIndices = async (): Promise<TestResult> => {
  const startTime = Date.now();
  
  try {
    console.log(`🧪 Testing Indian Market Indices...`);
    
    const indices = await getIndianMarketIndices();
    const duration = Date.now() - startTime;
    
    if (!indices || indices.length === 0) {
      throw new Error('No indices data received');
    }
    
    console.log(`✅ Indian Market Indices - SUCCESS (${duration}ms)`);
    indices.forEach(index => {
      console.log(`   ${index.name}: ${index.value.toLocaleString()} (${index.changePercent >= 0 ? '+' : ''}${index.changePercent.toFixed(2)}%)`);
    });
    
    return {
      success: true,
      data: indices,
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    console.error(`❌ Indian Market Indices - FAILED (${duration}ms)`);
    console.error(`   Error: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
      duration
    };
  }
};

/**
 * Test data quality and consistency
 */
export const testDataQuality = async (symbol: string): Promise<TestResult> => {
  const startTime = Date.now();
  
  try {
    console.log(`🧪 Testing data quality for ${symbol}...`);
    
    const quote = await getIndianStockQuote(symbol);
    const duration = Date.now() - startTime;
    
    // Data quality checks
    const checks = {
      pricePositive: quote.price > 0,
      validChange: typeof quote.change === 'number',
      validPercentage: typeof quote.changePercent === 'number',
      validVolume: quote.volume >= 0,
      validTimestamp: quote.timestamp > 0,
      validCurrency: quote.currency === 'INR',
      recentData: (Date.now() / 1000 - quote.timestamp) < (7 * 24 * 60 * 60), // Within 7 days
    };
    
    const failedChecks = Object.entries(checks)
      .filter(([_, passed]) => !passed)
      .map(([check, _]) => check);
    
    if (failedChecks.length > 0) {
      throw new Error(`Data quality issues: ${failedChecks.join(', ')}`);
    }
    
    console.log(`✅ Data Quality - PASSED (${duration}ms)`);
    console.log(`   All quality checks passed for ${symbol}`);
    
    return {
      success: true,
      data: { symbol, checks },
      duration
    };
    
  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    console.error(`❌ Data Quality - FAILED (${duration}ms)`);
    console.error(`   Error: ${errorMessage}`);
    
    return {
      success: false,
      error: errorMessage,
      duration
    };
  }
};

/**
 * Run comprehensive Yahoo Finance Indian stock test
 */
export const runYahooFinanceIndianTest = async (): Promise<TestSummary> => {
  console.log('🚀 Starting Yahoo Finance Indian Stock Data Test');
  console.log('=' .repeat(60));
  
  const results: TestSummary['results'] = [];
  let totalDuration = 0;
  
  try {
    // Test individual stocks
    for (const stock of TEST_STOCKS) {
      const result = await testSingleIndianStock(stock.symbol, stock.name);
      results.push({
        test: `${stock.name} (${stock.symbol})`,
        status: result.success ? 'PASS' : 'FAIL',
        duration: result.duration,
        error: result.error
      });
      totalDuration += result.duration;
      
      // Small delay between requests to be respectful
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Test market indices
    const indicesResult = await testIndianMarketIndices();
    results.push({
      test: 'Indian Market Indices',
      status: indicesResult.success ? 'PASS' : 'FAIL',
      duration: indicesResult.duration,
      error: indicesResult.error
    });
    totalDuration += indicesResult.duration;
    
    // Test data quality on one stock
    await new Promise(resolve => setTimeout(resolve, 1000));
    const qualityResult = await testDataQuality('RELIANCE.NS');
    results.push({
      test: 'Data Quality Check',
      status: qualityResult.success ? 'PASS' : 'FAIL',
      duration: qualityResult.duration,
      error: qualityResult.error
    });
    totalDuration += qualityResult.duration;
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
  
  // Calculate summary
  const passed = results.filter(r => r.status === 'PASS').length;
  const failed = results.filter(r => r.status === 'FAIL').length;
  const successRate = (passed / results.length) * 100;
  const avgDuration = totalDuration / results.length;
  
  // Print summary
  console.log('');
  console.log('📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  console.log(`Total Tests: ${results.length}`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Success Rate: ${successRate.toFixed(1)}%`);
  console.log(`Average Duration: ${avgDuration.toFixed(0)}ms`);
  console.log('');
  
  // Print detailed results
  console.log('📋 DETAILED RESULTS');
  console.log('-'.repeat(60));
  results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${status} ${result.test} (${result.duration}ms)`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  console.log('');
  console.log('🎯 VERDICT');
  console.log('-'.repeat(60));
  if (successRate >= 80) {
    console.log('✅ Yahoo Finance Indian stock data is WORKING WELL');
    console.log('   Recommendation: Keep current setup');
  } else if (successRate >= 50) {
    console.log('⚠️  Yahoo Finance Indian stock data has SOME ISSUES');
    console.log('   Recommendation: Monitor closely, consider alternatives');
  } else {
    console.log('❌ Yahoo Finance Indian stock data is FAILING');
    console.log('   Recommendation: Find alternative data source urgently');
  }
  
  return {
    totalTests: results.length,
    passed,
    failed,
    successRate,
    avgDuration,
    results
  };
};

/**
 * Quick test function for immediate validation
 */
export const quickYahooFinanceTest = async (): Promise<boolean> => {
  console.log('⚡ Quick Yahoo Finance Test...');
  
  try {
    const quote = await getIndianStockQuote('RELIANCE.NS');
    
    if (quote && quote.price > 0 && quote.symbol) {
      console.log(`✅ Quick test PASSED: RELIANCE.NS = ${formatINR(quote.price)}`);
      return true;
    } else {
      console.log('❌ Quick test FAILED: Invalid data structure');
      return false;
    }
  } catch (error) {
    console.log(`❌ Quick test FAILED: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return false;
  }
};

// Export for use in components or console testing
export default {
  runYahooFinanceIndianTest,
  quickYahooFinanceTest,
  testSingleIndianStock,
  testIndianMarketIndices,
  testDataQuality
};
