/**
 * Request deduplication utility to prevent multiple identical API calls
 * This works alongside React Query's built-in deduplication for additional protection
 */

interface PendingRequest<T> {
  promise: Promise<T>;
  timestamp: number;
}

class RequestDeduplicator {
  private pendingRequests = new Map<string, PendingRequest<any>>();
  private readonly TIMEOUT = 30000; // 30 seconds timeout

  /**
   * Deduplicate requests by key. If a request with the same key is already pending,
   * return the existing promise instead of making a new request.
   */
  async deduplicate<T>(key: string, requestFn: () => Promise<T>): Promise<T> {
    // Clean up expired requests
    this.cleanup();

    const existing = this.pendingRequests.get(key);
    
    // If there's an existing request that's not expired, return it
    if (existing && Date.now() - existing.timestamp < this.TIMEOUT) {
      console.log(`[RequestDeduplicator] Reusing existing request for key: ${key}`);
      return existing.promise;
    }

    // Create new request
    console.log(`[RequestDeduplicator] Creating new request for key: ${key}`);
    const promise = requestFn().finally(() => {
      // Clean up after request completes
      this.pendingRequests.delete(key);
    });

    this.pendingRequests.set(key, {
      promise,
      timestamp: Date.now(),
    });

    return promise;
  }

  /**
   * Clean up expired requests
   */
  private cleanup() {
    const now = Date.now();
    for (const [key, request] of this.pendingRequests.entries()) {
      if (now - request.timestamp > this.TIMEOUT) {
        this.pendingRequests.delete(key);
      }
    }
  }

  /**
   * Clear all pending requests (useful for testing or manual cleanup)
   */
  clear() {
    this.pendingRequests.clear();
  }

  /**
   * Get stats about pending requests
   */
  getStats() {
    return {
      pendingCount: this.pendingRequests.size,
      keys: Array.from(this.pendingRequests.keys()),
    };
  }
}

// Global instance
export const requestDeduplicator = new RequestDeduplicator();

/**
 * Helper function to create a deduplication key for stock-related requests
 */
export const createStockRequestKey = (
  type: 'quote' | 'profile' | 'candles' | 'history' | 'news',
  symbol?: string,
  params?: Record<string, any>
): string => {
  const baseKey = symbol ? `${type}-${symbol}` : type;
  if (params) {
    const paramString = Object.entries(params)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}:${value}`)
      .join('|');
    return `${baseKey}-${paramString}`;
  }
  return baseKey;
};

/**
 * Wrapper for stock quote requests with deduplication
 */
export const deduplicatedStockQuote = async <T>(
  symbol: string,
  requestFn: () => Promise<T>
): Promise<T> => {
  const key = createStockRequestKey('quote', symbol);
  return requestDeduplicator.deduplicate(key, requestFn);
};

/**
 * Wrapper for company profile requests with deduplication
 */
export const deduplicatedCompanyProfile = async <T>(
  symbol: string,
  requestFn: () => Promise<T>
): Promise<T> => {
  const key = createStockRequestKey('profile', symbol);
  return requestDeduplicator.deduplicate(key, requestFn);
};



/**
 * Wrapper for market news requests with deduplication
 */
export const deduplicatedMarketNews = async <T>(
  category: string,
  requestFn: () => Promise<T>
): Promise<T> => {
  const key = createStockRequestKey('news', undefined, { category });
  return requestDeduplicator.deduplicate(key, requestFn);
};

// Development helper to monitor deduplication
if (process.env.NODE_ENV === 'development') {
  // Log stats every 30 seconds
  setInterval(() => {
    const stats = requestDeduplicator.getStats();
    if (stats.pendingCount > 0) {
      console.log('[RequestDeduplicator] Stats:', stats);
    }
  }, 30000);
}
