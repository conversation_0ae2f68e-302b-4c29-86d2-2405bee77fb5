/**
 * Stock data caching utilities for optimized performance
 * Handles different caching strategies for Yahoo Finance and Finnhub APIs
 */

import { isIndianStock, getMarketRegion } from './stockUtils';

// Cache configuration based on data type and market
export const getCacheConfig = (symbol: string, dataType: 'quote' | 'profile' | 'candles' | 'search' | 'news') => {
  const isIndian = isIndianStock(symbol);
  const marketRegion = getMarketRegion(symbol);
  
  // Base configurations
  const configs = {
    quote: {
      staleTime: isIndian ? 30000 : 30000, // 30 seconds for both
      gcTime: isIndian ? 300000 : 300000, // 5 minutes for both
      refetchInterval: isIndian ? 30000 : 30000, // 30 seconds for both
    },
    profile: {
      staleTime: 3600000, // 1 hour - company profiles don't change often
      gcTime: 5400000, // 90 minutes
      refetchInterval: false, // No auto-refresh for profiles
    },
    candles: {
      staleTime: 300000, // 5 minutes
      gcTime: 900000, // 15 minutes
      refetchInterval: false, // No auto-refresh for historical data
    },
    search: {
      staleTime: 1800000, // 30 minutes - search results are relatively stable
      gcTime: 3600000, // 1 hour
      refetchInterval: false,
    },
    news: {
      staleTime: 300000, // 5 minutes
      gcTime: 900000, // 15 minutes
      refetchInterval: 600000, // 10 minutes
    },
  };

  return configs[dataType];
};

// Market-aware caching - different strategies for market hours
export const getMarketAwareCacheConfig = (symbol: string, dataType: 'quote' | 'indices') => {
  const isIndian = isIndianStock(symbol);
  const isMarketOpen = isIndian ? areIndianMarketsOpen() : areUSMarketsOpen();
  
  if (dataType === 'quote') {
    return {
      staleTime: isMarketOpen ? 30000 : 300000, // 30s when open, 5min when closed
      gcTime: isMarketOpen ? 300000 : 1800000, // 5min when open, 30min when closed
      refetchInterval: isMarketOpen ? 30000 : 300000, // More frequent when market is open
    };
  }
  
  if (dataType === 'indices') {
    return {
      staleTime: isMarketOpen ? 60000 : 600000, // 1min when open, 10min when closed
      gcTime: isMarketOpen ? 600000 : 3600000, // 10min when open, 1hr when closed
      refetchInterval: isMarketOpen ? 60000 : 600000,
    };
  }
  
  return getCacheConfig(symbol, 'quote');
};

// Simple market hours detection (you can enhance this with actual market hours)
const areIndianMarketsOpen = (): boolean => {
  const now = new Date();
  const istTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Kolkata"}));
  const hour = istTime.getHours();
  const day = istTime.getDay();
  
  // Monday to Friday, 9:15 AM to 3:30 PM IST (simplified)
  return day >= 1 && day <= 5 && hour >= 9 && hour < 16;
};

const areUSMarketsOpen = (): boolean => {
  const now = new Date();
  const estTime = new Date(now.toLocaleString("en-US", {timeZone: "America/New_York"}));
  const hour = estTime.getHours();
  const day = estTime.getDay();
  
  // Monday to Friday, 9:30 AM to 4:00 PM EST (simplified)
  return day >= 1 && day <= 5 && hour >= 9 && hour < 16;
};

// Cache key generation for consistent caching
export const generateCacheKey = (
  type: string,
  symbol?: string,
  params?: Record<string, any>
): string[] => {
  const baseKey = [type];
  
  if (symbol) {
    baseKey.push(symbol);
  }
  
  if (params) {
    // Sort params for consistent key generation
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}:${params[key]}`)
      .join(',');
    baseKey.push(sortedParams);
  }
  
  return baseKey;
};

// Batch cache invalidation
export const invalidateStockCache = (queryClient: any, symbol: string) => {
  const isIndian = isIndianStock(symbol);
  const prefix = isIndian ? 'indian-stock' : 'stock';
  
  // Invalidate all related queries for the symbol
  queryClient.invalidateQueries({ queryKey: [`${prefix}-quote`, symbol] });
  queryClient.invalidateQueries({ queryKey: [`${prefix}-candles`, symbol] });
  queryClient.invalidateQueries({ queryKey: [`${prefix}-profile`, symbol] });
  
  // Invalidate batch queries that might include this symbol
  queryClient.invalidateQueries({ queryKey: ['batch-quotes'] });
  queryClient.invalidateQueries({ queryKey: ['batch-indian-quotes'] });
};

// Prefetch strategy for commonly accessed stocks
export const prefetchPopularStocks = async (queryClient: any) => {
  const popularIndianStocks = [
    'RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'HINDUNILVR.NS'
  ];
  
  const popularUSStocks = [
    'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA'
  ];
  
  // Prefetch Indian stocks
  popularIndianStocks.forEach(symbol => {
    queryClient.prefetchQuery({
      queryKey: ['indian-stock-quote', symbol],
      staleTime: 30000,
    });
  });
  
  // Prefetch US stocks
  popularUSStocks.forEach(symbol => {
    queryClient.prefetchQuery({
      queryKey: ['stock-quote', symbol],
      staleTime: 30000,
    });
  });
};

// Memory optimization - remove old cache entries
export const optimizeCache = (queryClient: any) => {
  const now = Date.now();
  const oneHourAgo = now - 3600000; // 1 hour
  
  // Remove old cache entries
  queryClient.getQueryCache().getAll().forEach((query: any) => {
    if (query.state.dataUpdatedAt < oneHourAgo) {
      queryClient.removeQueries({ queryKey: query.queryKey });
    }
  });
};

// Performance monitoring
export const trackAPIPerformance = (
  apiName: 'yahoo' | 'finnhub',
  endpoint: string,
  duration: number,
  success: boolean
) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`API Performance: ${apiName}/${endpoint} - ${duration}ms - ${success ? 'SUCCESS' : 'FAILED'}`);
  }
  
  // You can extend this to send metrics to monitoring service
};

// Retry configuration based on API source
export const getRetryConfig = (symbol: string) => {
  const isIndian = isIndianStock(symbol);
  
  return {
    retry: isIndian ? 2 : 2, // Same retry count for both
    retryDelay: (attemptIndex: number) => {
      // Exponential backoff with jitter
      const baseDelay = isIndian ? 2000 : 1000; // Yahoo Finance gets longer delay
      const exponentialDelay = Math.min(baseDelay * Math.pow(2, attemptIndex), 30000);
      const jitter = Math.random() * 1000; // Add randomness to prevent thundering herd
      return exponentialDelay + jitter;
    },
  };
};

// Cache warming strategy
export const warmCache = async (queryClient: any, symbols: string[]) => {
  const { indianStocks, usStocks } = symbols.reduce((acc, symbol) => {
    if (isIndianStock(symbol)) {
      acc.indianStocks.push(symbol);
    } else {
      acc.usStocks.push(symbol);
    }
    return acc;
  }, { indianStocks: [] as string[], usStocks: [] as string[] });
  
  // Warm Indian stock cache
  if (indianStocks.length > 0) {
    queryClient.prefetchQuery({
      queryKey: ['batch-indian-quotes', indianStocks.sort().join(',')],
      staleTime: 30000,
    });
  }
  
  // Warm US stock cache
  if (usStocks.length > 0) {
    queryClient.prefetchQuery({
      queryKey: ['batch-quotes', usStocks.sort().join(',')],
      staleTime: 30000,
    });
  }
};
