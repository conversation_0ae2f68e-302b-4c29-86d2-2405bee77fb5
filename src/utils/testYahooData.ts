/**
 * Test script to validate Yahoo Finance data transformation
 * Run this to test the actual response.txt data
 */

import { transformYahooFinanceData, validateYahooFinanceResponse } from './yahooFinanceDataTransformer';

// Sample response data structure (from your response.txt)
const sampleResponse = {
  "result": [
    {
      "meta": {
        "currency": "INR",
        "symbol": "BHARTIARTL.NS",
        "exchangeName": "NSI",
        "fullExchangeName": "NSE",
        "instrumentType": "EQUITY",
        "firstTradeDate": 1025495100,
        "regularMarketTime": 1752832800,
        "hasPrePostMarketData": false,
        "gmtoffset": 19800,
        "timezone": "IST",
        "exchangeTimezoneName": "Asia/Kolkata",
        "regularMarketPrice": 1901,
        "fiftyTwoWeekHigh": 2045.8,
        "fiftyTwoWeekLow": 1422.6,
        "regularMarketDayHigh": 1916,
        "regularMarketDayLow": 1891.8,
        "regularMarketVolume": 4643834,
        "longName": "Bharti Airtel Limited",
        "shortName": "BHARTI AIRTEL LIMITED",
        "chartPreviousClose": 1929.9,
        "previousClose": 1929.9,
        "scale": 3,
        "priceHint": 2
      },
      "timestamp": [1752810300, 1752810360, 1752810420], // Sample timestamps
      "indicators": {
        "quote": [
          {
            "close": [1911.699951171875, 1910.300048828125, 1907.4000244140625],
            "high": [1914.5, 1912.9000244140625, 1909.9000244140625],
            "volume": [0, 37426, 26526],
            "low": [1909, 1908.9000244140625, 1907],
            "open": [1912.800048828125, 1912, 1909.5]
          }
        ]
      }
    }
  ],
  "error": null
};

export function testYahooDataTransformation() {
  console.log('🧪 Testing Yahoo Finance Data Transformation');
  console.log('='.repeat(50));

  // Test 1: Validate response structure
  console.log('\n1. Validating response structure...');
  const isValid = validateYahooFinanceResponse(sampleResponse);
  console.log(`✅ Response validation: ${isValid ? 'PASSED' : 'FAILED'}`);

  if (!isValid) {
    console.log('❌ Response structure is invalid');
    return;
  }

  // Test 2: Transform data
  console.log('\n2. Transforming data...');
  const transformed = transformYahooFinanceData(sampleResponse);
  
  if (!transformed) {
    console.log('❌ Data transformation failed');
    return;
  }

  console.log('✅ Data transformation: PASSED');
  console.log(`📊 Transformed ${transformed.data.length} data points`);

  // Test 3: Validate transformed data structure
  console.log('\n3. Validating transformed data...');
  const firstPoint = transformed.data[0];
  
  const requiredFields = ['timestamp', 'date', 'time', 'open', 'high', 'low', 'close', 'volume'];
  const hasAllFields = requiredFields.every(field => firstPoint.hasOwnProperty(field));
  
  console.log(`✅ Required fields check: ${hasAllFields ? 'PASSED' : 'FAILED'}`);
  
  if (hasAllFields) {
    console.log('\n📈 Sample data point:');
    console.log(JSON.stringify(firstPoint, null, 2));
  }

  // Test 4: Validate metadata
  console.log('\n4. Validating metadata...');
  console.log(`📋 Company: ${transformed.meta.companyName}`);
  console.log(`💱 Symbol: ${transformed.meta.symbol}`);
  console.log(`💰 Currency: ${transformed.meta.currency}`);
  console.log(`📊 Current Price: ₹${transformed.meta.currentPrice}`);
  console.log(`📈 Day High: ₹${transformed.meta.dayHigh}`);
  console.log(`📉 Day Low: ₹${transformed.meta.dayLow}`);
  console.log(`📊 Volume: ${transformed.meta.volume.toLocaleString()}`);

  // Test 5: Chart compatibility check
  console.log('\n5. Chart compatibility check...');
  const chartCompatible = transformed.data.every(point => 
    typeof point.close === 'number' && 
    typeof point.timestamp === 'number' &&
    point.close > 0
  );
  
  console.log(`✅ Chart compatibility: ${chartCompatible ? 'PASSED' : 'FAILED'}`);

  console.log('\n🎉 All tests completed!');
  console.log('='.repeat(50));

  return transformed;
}

// Common issues and solutions
export const troubleshootingGuide = {
  'Charts not rendering': [
    'Check if data array is not empty',
    'Verify all required fields (timestamp, close, open, high, low) are present',
    'Ensure numeric values are not null or undefined',
    'Check if ResponsiveContainer has proper height'
  ],
  
  'Data transformation fails': [
    'Verify Yahoo Finance API response structure',
    'Check if indicators.quote[0] exists',
    'Ensure timestamp array length matches OHLCV arrays',
    'Validate that all arrays have the same length'
  ],
  
  'Missing chart data': [
    'Check if API response includes historical data (not just current price)',
    'Verify the API endpoint includes range parameter (1d, 5d, 1mo, etc.)',
    'Ensure the interval parameter is set correctly (1m, 5m, 1h, 1d)',
    'Check if the stock symbol is correct (.NS for NSE, .BO for BSE)'
  ],
  
  'Performance issues': [
    'Limit data points for intraday charts (use sampling)',
    'Use memo for expensive chart calculations',
    'Implement data virtualization for large datasets',
    'Consider using WebWorkers for data processing'
  ]
};

// Export for use in components
export { sampleResponse };
