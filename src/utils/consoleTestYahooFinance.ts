/**
 * Console Test for Yahoo Finance Indian Stock Data
 * Run this in browser console to quickly test Yahoo Finance functionality
 */

import { getIndianStockQuote, getIndianMarketIndices } from '@/integrations/yahoo-finance/api';
import { formatINR } from '@/integrations/yahoo-finance/types';

// Make functions available globally for console testing
declare global {
  interface Window {
    testYahooFinance: {
      quickTest: () => Promise<void>;
      testStock: (symbol: string) => Promise<void>;
      testIndices: () => Promise<void>;
      testMultipleStocks: () => Promise<void>;
    };
  }
}

/**
 * Quick test - single stock
 */
const quickTest = async () => {
  console.log('🚀 Yahoo Finance Quick Test - RELIANCE.NS');
  console.log('=' .repeat(50));
  
  try {
    const startTime = Date.now();
    const quote = await getIndianStockQuote('RELIANCE.NS');
    const duration = Date.now() - startTime;
    
    console.log('✅ SUCCESS!');
    console.log(`📊 RELIANCE.NS Data (${duration}ms):`);
    console.log(`   Price: ${formatINR(quote.price)}`);
    console.log(`   Change: ${quote.change >= 0 ? '+' : ''}${formatINR(quote.change)} (${quote.changePercent.toFixed(2)}%)`);
    console.log(`   High: ${formatINR(quote.high)} | Low: ${formatINR(quote.low)}`);
    console.log(`   Volume: ${quote.volume.toLocaleString()}`);
    console.log(`   Exchange: ${quote.exchange}`);
    console.log(`   Market State: ${quote.marketState}`);
    console.log(`   Currency: ${quote.currency}`);
    console.log(`   Timestamp: ${new Date(quote.timestamp * 1000).toLocaleString()}`);
    
    return quote;
  } catch (error) {
    console.error('❌ FAILED!');
    console.error('Error:', error);
    throw error;
  }
};

/**
 * Test specific stock symbol
 */
const testStock = async (symbol: string) => {
  console.log(`🧪 Testing ${symbol}`);
  console.log('-'.repeat(30));
  
  try {
    const startTime = Date.now();
    const quote = await getIndianStockQuote(symbol);
    const duration = Date.now() - startTime;
    
    console.log(`✅ ${symbol} - SUCCESS (${duration}ms)`);
    console.log(`   ${formatINR(quote.price)} (${quote.changePercent >= 0 ? '+' : ''}${quote.changePercent.toFixed(2)}%)`);
    
    return quote;
  } catch (error) {
    console.error(`❌ ${symbol} - FAILED`);
    console.error('   Error:', error instanceof Error ? error.message : error);
    throw error;
  }
};

/**
 * Test Indian market indices
 */
const testIndices = async () => {
  console.log('🏛️ Testing Indian Market Indices');
  console.log('=' .repeat(40));
  
  try {
    const startTime = Date.now();
    const indices = await getIndianMarketIndices();
    const duration = Date.now() - startTime;
    
    console.log(`✅ Indices SUCCESS (${duration}ms)`);
    console.log(`📈 Found ${indices.length} indices:`);
    
    indices.forEach(index => {
      const changeSymbol = index.changePercent >= 0 ? '📈' : '📉';
      console.log(`   ${changeSymbol} ${index.name}: ${index.value.toLocaleString()} (${index.changePercent >= 0 ? '+' : ''}${index.changePercent.toFixed(2)}%)`);
    });
    
    return indices;
  } catch (error) {
    console.error('❌ Indices FAILED');
    console.error('Error:', error);
    throw error;
  }
};

/**
 * Test multiple popular Indian stocks
 */
const testMultipleStocks = async () => {
  console.log('📊 Testing Multiple Indian Stocks');
  console.log('=' .repeat(50));
  
  const stocks = [
    'RELIANCE.NS',
    'TCS.NS', 
    'HDFCBANK.NS',
    'INFY.NS',
    'HINDUNILVR.NS'
  ];
  
  const results = [];
  
  for (const symbol of stocks) {
    try {
      const quote = await testStock(symbol);
      results.push({ symbol, success: true, data: quote });
      
      // Small delay between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    } catch (error) {
      results.push({ symbol, success: false, error });
    }
  }
  
  // Summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log('\n📋 SUMMARY');
  console.log('-'.repeat(30));
  console.log(`✅ Successful: ${successful}/${stocks.length}`);
  console.log(`❌ Failed: ${failed}/${stocks.length}`);
  console.log(`📊 Success Rate: ${((successful / stocks.length) * 100).toFixed(1)}%`);
  
  if (successful >= 4) {
    console.log('🎉 Yahoo Finance is working well for Indian stocks!');
  } else if (successful >= 2) {
    console.log('⚠️  Yahoo Finance has some issues with Indian stocks');
  } else {
    console.log('🚨 Yahoo Finance is failing for Indian stocks');
  }
  
  return results;
};

// Export test functions
const yahooFinanceTests = {
  quickTest,
  testStock,
  testIndices,
  testMultipleStocks
};

// Make available globally for console access
if (typeof window !== 'undefined') {
  window.testYahooFinance = yahooFinanceTests;
}

export default yahooFinanceTests;

// Console instructions
console.log(`
🧪 Yahoo Finance Console Tests Available!

Run these commands in your browser console:

1. Quick Test (single stock):
   await window.testYahooFinance.quickTest()

2. Test specific stock:
   await window.testYahooFinance.testStock('RELIANCE.NS')
   await window.testYahooFinance.testStock('TCS.NS')

3. Test market indices:
   await window.testYahooFinance.testIndices()

4. Test multiple stocks:
   await window.testYahooFinance.testMultipleStocks()

Examples:
- await window.testYahooFinance.quickTest()
- await window.testYahooFinance.testStock('HDFCBANK.NS')
- await window.testYahooFinance.testIndices()
`);
