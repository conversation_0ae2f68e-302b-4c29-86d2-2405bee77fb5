/**
 * Feature flags for gradual migration to microservice
 */

// Environment-based feature flags
const FEATURE_FLAGS = {
  USE_MICROSERVICE: import.meta.env.VITE_USE_MICROSERVICE === 'true',
  MICROSERVICE_ROLLOUT_PERCENTAGE: parseInt(import.meta.env.VITE_MICROSERVICE_ROLLOUT || '0'),
  ENABLE_PERFORMANCE_LOGGING: import.meta.env.VITE_ENABLE_PERF_LOGGING === 'true',
};

// User-based rollout (deterministic based on user session)
const getUserRolloutGroup = (): number => {
  // Use a consistent hash based on session/user ID
  const sessionId = sessionStorage.getItem('sessionId') || 
    Math.random().toString(36).substring(7);
  
  if (!sessionStorage.getItem('sessionId')) {
    sessionStorage.setItem('sessionId', sessionId);
  }
  
  // Simple hash to get consistent percentage
  let hash = 0;
  for (let i = 0; i < sessionId.length; i++) {
    const char = sessionId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash) % 100;
};

export const shouldUseMicroservice = (): boolean => {
  // Force enable if environment variable is set
  if (FEATURE_FLAGS.USE_MICROSERVICE) {
    return true;
  }
  
  // Gradual rollout based on percentage
  const rolloutPercentage = FEATURE_FLAGS.MICROSERVICE_ROLLOUT_PERCENTAGE;
  if (rolloutPercentage > 0) {
    const userGroup = getUserRolloutGroup();
    return userGroup < rolloutPercentage;
  }
  
  return false;
};

export const logPerformance = (
  operation: string, 
  duration: number, 
  source: 'yahoo' | 'microservice',
  success: boolean = true
) => {
  if (!FEATURE_FLAGS.ENABLE_PERFORMANCE_LOGGING) return;
  
  const logData = {
    operation,
    duration,
    source,
    success,
    timestamp: new Date().toISOString(),
    userAgent: navigator.userAgent,
  };
  
  console.log(`📊 Performance Log:`, logData);
  
  // In production, you might want to send this to analytics
  // analytics.track('api_performance', logData);
};

export const getFeatureFlags = () => FEATURE_FLAGS;

// Development helpers
if (import.meta.env.DEV) {
  // Make feature flags available in console for testing
  (window as any).featureFlags = {
    shouldUseMicroservice,
    getFeatureFlags,
    getUserRolloutGroup,
    logPerformance,
  };
  
  console.log('🚩 Feature Flags:', {
    useMicroservice: shouldUseMicroservice(),
    rolloutPercentage: FEATURE_FLAGS.MICROSERVICE_ROLLOUT_PERCENTAGE,
    userGroup: getUserRolloutGroup(),
  });
}
