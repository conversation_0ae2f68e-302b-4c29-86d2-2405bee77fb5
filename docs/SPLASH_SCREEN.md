# Splash Screen Implementation

## Overview

The Syed's Investments website now features a beautiful, customizable splash screen that displays when users first visit the site. The splash screen creates an attractive first impression with smooth animations, gradient backgrounds, and professional branding.

## Features

### ✨ Visual Features
- **Gradient Background**: Beautiful multi-color gradient backgrounds
- **Animated Logo**: Company logo with glow effects and smooth animations
- **Staggered Text Animation**: Company name and taglines appear with smooth transitions
- **Loading Indicator**: Animated dots showing loading progress
- **Responsive Design**: Works perfectly on desktop and mobile devices
- **Smooth Transitions**: Fade-in and fade-out effects for seamless user experience

### ⚙️ Technical Features
- **Configurable Duration**: Customizable display time (default: 3 seconds)
- **Session Management**: Skip splash screen on revisit within the same session
- **Minimum Display Time**: Ensures splash screen shows for at least 2 seconds
- **Theme Support**: Multiple pre-built themes (default, light, premium)
- **Custom Configuration**: Extensive customization options

## Implementation

### Core Components

1. **SplashScreen Component** (`src/components/SplashScreen.tsx`)
   - Main splash screen component with animations and styling
   - Accepts configuration props for customization

2. **useSplashScreen Hook** (`src/hooks/useSplashScreen.ts`)
   - Manages splash screen state and timing
   - Handles session storage for skip-on-revisit functionality

3. **Configuration System** (`src/config/splashConfig.ts`)
   - Centralized configuration management
   - Pre-built theme configurations
   - Type-safe configuration options

### Integration

The splash screen is integrated into the main App component:

```typescript
const App = () => {
  const { showSplash, handleSplashComplete, isAppReady } = useSplashScreen({
    duration: 3000,
    minDisplayTime: 2000,
    skipOnRevisit: true
  });

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {showSplash && (
          <SplashScreen onComplete={handleSplashComplete} duration={3000} />
        )}
        
        {isAppReady && (
          <BrowserRouter>
            {/* Main app routes */}
          </BrowserRouter>
        )}
      </AuthProvider>
    </QueryClientProvider>
  );
};
```

## Configuration Options

### Basic Configuration

```typescript
interface SplashScreenConfig {
  // Display settings
  duration: number;              // Total display time (ms)
  minDisplayTime: number;        // Minimum display time (ms)
  skipOnRevisit: boolean;        // Skip on same session revisit
  
  // Animation timings
  logoDelay: number;             // Logo animation delay (ms)
  textDelay: number;             // Text animation delay (ms)
  fadeOutOffset: number;         // Fade out start offset (ms)
  
  // Content
  companyName: string;           // Company name text
  primaryTagline: string;        // Main tagline
  secondaryTagline: string;      // Secondary tagline
  loadingText: string;           // Loading indicator text
  
  // Styling
  gradientColors: {
    background: string[];        // Background gradient classes
    text: string[];              // Text gradient classes
    accent: string[];            // Accent gradient classes
  };
  
  // Logo settings
  logoSize: {
    width: string;               // Logo width class
    height: string;              // Logo height class
  };
}
```

### Pre-built Themes

#### Default Theme (Dark Finance)
- Dark blue gradient background
- Blue to cyan text gradients
- Professional finance-focused styling
- Duration: 3 seconds

#### Light Theme
- Light blue gradient background
- Darker blue text gradients
- Clean, minimal appearance
- Duration: 3 seconds

#### Premium Theme
- Dark purple gradient background
- Gold to amber text gradients
- Elegant, premium feel
- Duration: 4 seconds

### Custom Configuration Example

```typescript
const customConfig = {
  duration: 4000,
  companyName: "Custom Finance Co.",
  primaryTagline: "Innovation in Investment",
  secondaryTagline: "Your Success, Our Mission",
  loadingText: "Preparing your dashboard...",
  gradientColors: {
    background: ["from-emerald-900", "via-teal-900", "to-cyan-900"],
    text: ["from-emerald-400", "via-teal-300", "to-cyan-300"],
    accent: ["from-emerald-500", "via-teal-500", "to-cyan-500"]
  }
};

<SplashScreen 
  onComplete={handleComplete} 
  duration={4000}
  config={customConfig} 
/>
```

## Demo Page

Visit `/splash-demo` to see all available themes and configurations in action. The demo page includes:

- Preview buttons for each theme
- Configuration details
- Customization examples
- Technical documentation

## Customization Guide

### Changing Colors

1. **Background Gradient**: Modify `gradientColors.background` array
2. **Text Gradient**: Modify `gradientColors.text` array  
3. **Accent Colors**: Modify `gradientColors.accent` array

### Adjusting Timing

1. **Total Duration**: Set `duration` prop
2. **Animation Delays**: Modify `logoDelay` and `textDelay`
3. **Fade Out Timing**: Adjust `fadeOutOffset`

### Content Updates

1. **Company Name**: Update `companyName` in config
2. **Taglines**: Modify `primaryTagline` and `secondaryTagline`
3. **Loading Text**: Change `loadingText`

### Logo Customization

1. **Size**: Modify `logoSize.width` and `logoSize.height`
2. **Image**: Replace `/assets/logo.svg` with your logo
3. **Effects**: Customize glow and shadow effects in component

## Browser Compatibility

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)

## Performance Considerations

- Splash screen uses CSS transforms and opacity for smooth animations
- Session storage prevents unnecessary re-displays
- Minimal JavaScript execution during display
- Optimized for fast loading and smooth transitions

## Accessibility

- Respects `prefers-reduced-motion` for users with motion sensitivity
- Proper ARIA labels and semantic HTML
- Keyboard navigation support
- Screen reader compatible

## Future Enhancements

- [ ] Sound effects support
- [ ] Video background options
- [ ] Progress bar for actual loading states
- [ ] A/B testing integration
- [ ] Analytics tracking
- [ ] Dark/light mode auto-detection
