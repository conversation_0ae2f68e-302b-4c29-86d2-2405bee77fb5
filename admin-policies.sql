-- Admin policies for content management
-- This script adds admin-specific RLS policies to allow admin users to manage all content

-- Admin policies for insights table
CREATE POLICY "Ad<PERSON> can view all insights" ON insights
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Ad<PERSON> can update all insights" ON insights
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Ad<PERSON> can delete all insights" ON insights
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

-- Admin policies for news table
CREATE POLICY "Ad<PERSON> can view all news" ON news
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Ad<PERSON> can update all news" ON news
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can delete all news" ON news
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

-- Admin policies for education table
CREATE POLICY "Admins can view all education" ON education
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can update all education" ON education
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can delete all education" ON education
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

-- Admin policies for case_studies table
CREATE POLICY "Admins can view all case_studies" ON case_studies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can update all case_studies" ON case_studies
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can delete all case_studies" ON case_studies
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

-- Admin policies for pages table
CREATE POLICY "Admins can view all pages" ON pages
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can update all pages" ON pages
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can delete all pages" ON pages
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

-- Admin policies for quarterly_outlook table (if not already present)
CREATE POLICY "Admins can view all quarterly_outlook" ON quarterly_outlook
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can update all quarterly_outlook" ON quarterly_outlook
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );

CREATE POLICY "Admins can delete all quarterly_outlook" ON quarterly_outlook
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM user_profiles 
            WHERE user_profiles.user_id = auth.uid() 
            AND user_profiles.role IN ('admin', 'editor')
        )
    );
