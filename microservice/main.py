"""
Fast Finance Data Microservice
High-performance API for Indian and US stock data
"""

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import yfinance as yf
import redis
import json
import asyncio
import time
from datetime import datetime, timedelta
import logging
from concurrent.futures import ThreadPoolExecutor
import os

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI
app = FastAPI(
    title="Finance Data API",
    description="High-performance stock data API for Indian and US markets",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Redis connection
try:
    redis_client = redis.Redis(
        host=os.getenv('REDIS_HOST', 'localhost'),
        port=int(os.getenv('REDIS_PORT', 6379)),
        decode_responses=True
    )
    redis_client.ping()
    logger.info("✅ Redis connected successfully")
except Exception as e:
    logger.warning(f"⚠️ Redis not available: {e}")
    redis_client = None

# Thread pool for parallel processing
executor = ThreadPoolExecutor(max_workers=10)

# Pydantic models
class StockQuote(BaseModel):
    symbol: str
    name: str
    price: float
    change: float
    changePercent: float
    high: float
    low: float
    open: float
    previousClose: float
    volume: int
    currency: str
    exchange: str
    timestamp: int
    marketState: str

class BatchQuoteRequest(BaseModel):
    symbols: List[str]

class BatchQuoteResponse(BaseModel):
    symbol: str
    data: Optional[StockQuote]
    error: Optional[str]

# Cache configuration
CACHE_DURATION = {
    'quote': 30,  # 30 seconds for quotes
    'profile': 3600,  # 1 hour for company profiles
    'indices': 60,  # 1 minute for indices
}

def get_cache_key(data_type: str, symbol: str = None, **kwargs) -> str:
    """Generate cache key"""
    if symbol:
        return f"{data_type}:{symbol}"
    return f"{data_type}:{':'.join(f'{k}={v}' for k, v in kwargs.items())}"

def get_from_cache(key: str) -> Optional[Dict]:
    """Get data from Redis cache"""
    if not redis_client:
        return None
    
    try:
        data = redis_client.get(key)
        if data:
            return json.loads(data)
    except Exception as e:
        logger.error(f"Cache read error: {e}")
    return None

def set_cache(key: str, data: Dict, ttl: int):
    """Set data in Redis cache"""
    if not redis_client:
        return
    
    try:
        redis_client.setex(key, ttl, json.dumps(data))
    except Exception as e:
        logger.error(f"Cache write error: {e}")

def fetch_stock_quote(symbol: str) -> Dict:
    """Fetch single stock quote using yfinance"""
    try:
        ticker = yf.Ticker(symbol)
        info = ticker.info
        
        # Get current price and market data
        current_price = info.get('currentPrice') or info.get('regularMarketPrice', 0)
        previous_close = info.get('previousClose', current_price)
        change = current_price - previous_close
        change_percent = (change / previous_close * 100) if previous_close else 0
        
        return {
            'symbol': symbol,
            'name': info.get('shortName', symbol),
            'price': current_price,
            'change': change,
            'changePercent': change_percent,
            'high': info.get('dayHigh', current_price),
            'low': info.get('dayLow', current_price),
            'open': info.get('open', current_price),
            'previousClose': previous_close,
            'volume': info.get('volume', 0),
            'currency': info.get('currency', 'USD'),
            'exchange': info.get('exchange', ''),
            'timestamp': int(time.time()),
            'marketState': info.get('marketState', 'REGULAR')
        }
    except Exception as e:
        logger.error(f"Error fetching {symbol}: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch data for {symbol}")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Finance Data API",
        "timestamp": datetime.now().isoformat(),
        "cache_available": redis_client is not None
    }

@app.get("/quote/{symbol}", response_model=StockQuote)
async def get_quote(symbol: str):
    """Get single stock quote with caching"""
    cache_key = get_cache_key('quote', symbol)
    
    # Try cache first
    cached_data = get_from_cache(cache_key)
    if cached_data:
        logger.info(f"📦 Cache hit for {symbol}")
        return StockQuote(**cached_data)
    
    # Fetch from API
    logger.info(f"🌐 Fetching {symbol} from API")
    loop = asyncio.get_event_loop()
    quote_data = await loop.run_in_executor(executor, fetch_stock_quote, symbol)
    
    # Cache the result
    set_cache(cache_key, quote_data, CACHE_DURATION['quote'])
    
    return StockQuote(**quote_data)

@app.post("/quotes/batch", response_model=List[BatchQuoteResponse])
async def get_batch_quotes(request: BatchQuoteRequest):
    """Get multiple stock quotes in parallel"""
    symbols = request.symbols
    logger.info(f"🔄 Batch request for {len(symbols)} symbols")
    
    async def fetch_single_quote(symbol: str) -> BatchQuoteResponse:
        try:
            quote = await get_quote(symbol)
            return BatchQuoteResponse(symbol=symbol, data=quote, error=None)
        except Exception as e:
            logger.error(f"Error fetching {symbol}: {e}")
            return BatchQuoteResponse(symbol=symbol, data=None, error=str(e))
    
    # Process all symbols in parallel
    tasks = [fetch_single_quote(symbol) for symbol in symbols]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Handle any exceptions
    final_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            final_results.append(
                BatchQuoteResponse(symbol=symbols[i], data=None, error=str(result))
            )
        else:
            final_results.append(result)
    
    return final_results

@app.get("/indices/indian")
async def get_indian_indices():
    """Get Indian market indices"""
    cache_key = get_cache_key('indices', region='indian')
    
    # Try cache first
    cached_data = get_from_cache(cache_key)
    if cached_data:
        return cached_data
    
    indices_symbols = ['^NSEI', '^BSESN', '^NSEBANK', '^NSEIT']
    
    async def fetch_index(symbol: str):
        try:
            quote = await get_quote(symbol)
            return quote.dict()
        except:
            return None
    
    tasks = [fetch_index(symbol) for symbol in indices_symbols]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Filter out failed requests
    indices_data = [r for r in results if r and not isinstance(r, Exception)]
    
    # Cache the result
    set_cache(cache_key, indices_data, CACHE_DURATION['indices'])
    
    return indices_data

@app.get("/health")
async def health_check():
    """Detailed health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "cache": {
            "available": redis_client is not None,
            "keys_count": redis_client.dbsize() if redis_client else 0
        },
        "performance": {
            "thread_pool_size": executor._max_workers,
            "active_threads": len(executor._threads) if hasattr(executor, '_threads') else 0
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
