# Supabase Setup Guide

This document provides instructions for setting up your Supabase database to work with the Finance Compass application.

## Database Schema

To use all features of the Finance Compass application, you'll need to create the following tables in your Supabase database:

### 1. Portfolios Table

```sql
-- Create portfolios table
CREATE TABLE portfolios (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    total_value DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS (Row Level Security)
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own portfolios" ON portfolios
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own portfolios" ON portfolios
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own portfolios" ON portfolios
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own portfolios" ON portfolios
    FOR DELETE USING (auth.uid() = user_id);
```

### 2. Portfolio Holdings Table

```sql
-- Create portfolio_holdings table
CREATE TABLE portfolio_holdings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
    symbol TEXT NOT NULL,
    quantity DECIMAL(15,8) NOT NULL,
    average_cost DECIMAL(15,2) NOT NULL,
    current_price DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE portfolio_holdings ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view holdings of their portfolios" ON portfolio_holdings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM portfolios 
            WHERE portfolios.id = portfolio_holdings.portfolio_id 
            AND portfolios.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert holdings to their portfolios" ON portfolio_holdings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM portfolios 
            WHERE portfolios.id = portfolio_holdings.portfolio_id 
            AND portfolios.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update holdings of their portfolios" ON portfolio_holdings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM portfolios 
            WHERE portfolios.id = portfolio_holdings.portfolio_id 
            AND portfolios.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete holdings of their portfolios" ON portfolio_holdings
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM portfolios 
            WHERE portfolios.id = portfolio_holdings.portfolio_id 
            AND portfolios.user_id = auth.uid()
        )
    );
```

### 3. Update Triggers (Optional)

```sql
-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers
CREATE TRIGGER update_portfolios_updated_at 
    BEFORE UPDATE ON portfolios 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_portfolio_holdings_updated_at
    BEFORE UPDATE ON portfolio_holdings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 3. User Profiles Table

```sql
-- Create user_profiles table for role management
CREATE TABLE user_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    role TEXT DEFAULT 'user' CHECK (role IN ('user', 'admin', 'editor')),
    full_name TEXT,
    bio TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create trigger
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 4. Content Tables

```sql
-- Create insights table
CREATE TABLE insights (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    tags TEXT[],
    featured_image TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create news table
CREATE TABLE news (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    tags TEXT[],
    featured_image TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create education table
CREATE TABLE education (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    tags TEXT[],
    featured_image TEXT,
    difficulty_level TEXT DEFAULT 'beginner' CHECK (difficulty_level IN ('beginner', 'intermediate', 'advanced')),
    estimated_read_time INTEGER,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create case_studies table
CREATE TABLE case_studies (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    tags TEXT[],
    featured_image TEXT,
    investment_amount DECIMAL(15,2),
    return_percentage DECIMAL(5,2),
    time_period TEXT,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create pages table for static content
CREATE TABLE pages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    slug TEXT UNIQUE NOT NULL,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    meta_description TEXT,
    author_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'published')),
    published_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 5. Content Table Policies

```sql
-- Insights policies
ALTER TABLE insights ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view published insights" ON insights
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own insights" ON insights
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create insights" ON insights
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own insights" ON insights
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own insights" ON insights
    FOR DELETE USING (auth.uid() = author_id);

-- News policies (same pattern)
ALTER TABLE news ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view published news" ON news
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own news" ON news
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create news" ON news
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own news" ON news
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own news" ON news
    FOR DELETE USING (auth.uid() = author_id);

-- Education policies (same pattern)
ALTER TABLE education ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view published education" ON education
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own education" ON education
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create education" ON education
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own education" ON education
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own education" ON education
    FOR DELETE USING (auth.uid() = author_id);

-- Case studies policies (same pattern)
ALTER TABLE case_studies ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view published case_studies" ON case_studies
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own case_studies" ON case_studies
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create case_studies" ON case_studies
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own case_studies" ON case_studies
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own case_studies" ON case_studies
    FOR DELETE USING (auth.uid() = author_id);

-- Pages policies (same pattern)
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Anyone can view published pages" ON pages
    FOR SELECT USING (status = 'published');

CREATE POLICY "Authors can view their own pages" ON pages
    FOR SELECT USING (auth.uid() = author_id);

CREATE POLICY "Authors can create pages" ON pages
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Authors can update their own pages" ON pages
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Authors can delete their own pages" ON pages
    FOR DELETE USING (auth.uid() = author_id);
```

### 6. Content Table Triggers

```sql
-- Create triggers for all content tables
CREATE TRIGGER update_insights_updated_at
    BEFORE UPDATE ON insights
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_news_updated_at
    BEFORE UPDATE ON news
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_education_updated_at
    BEFORE UPDATE ON education
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_case_studies_updated_at
    BEFORE UPDATE ON case_studies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pages_updated_at
    BEFORE UPDATE ON pages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 7. Create Admin User

```sql
-- Insert a user profile with admin role (replace with your user ID)
INSERT INTO user_profiles (user_id, role, full_name)
VALUES ('your-user-id-here', 'admin', 'Admin User');
```

## Environment Variables

Make sure your `.env.local` file contains the correct Supabase credentials:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## Authentication Setup

1. In your Supabase dashboard, go to Authentication > Settings
2. Configure your site URL (e.g., `http://localhost:5173` for development)
3. Enable email authentication or configure other providers as needed

## Testing the Integration

1. Start your development server: `npm run dev`
2. Navigate to your application
3. Sign up for a new account or sign in
4. Go to the Dashboard to test portfolio creation
5. Create a new portfolio to verify the database integration is working

## Features Implemented

### Core Features
- ✅ User authentication with Supabase Auth
- ✅ Protected routes and user sessions
- ✅ Portfolio CRUD operations
- ✅ Real-time data with React Query
- ✅ Row Level Security (RLS) for data protection
- ✅ TypeScript integration with Supabase types
- ✅ Error handling and loading states
- ✅ Toast notifications for user feedback

### Content Management System
- ✅ Dynamic content pages (Insights, News, Education, Case Studies)
- ✅ Admin dashboard with CRUD operations
- ✅ Role-based access control (User, Admin, Editor)
- ✅ Content filtering and search functionality
- ✅ Draft and published status management
- ✅ Category and tag organization
- ✅ Featured images and rich content support
- ✅ SEO-friendly page structure

### Admin Features
- ✅ Admin-only dashboard access
- ✅ Content statistics and analytics
- ✅ Bulk content management
- ✅ User role management
- ✅ Content publishing workflow

## Next Steps

You can extend this setup by:

1. Adding more financial data tables (transactions, watchlists, etc.)
2. Implementing real-time subscriptions for live data updates
3. Adding file upload capabilities for documents
4. Integrating with external financial APIs
5. Adding more sophisticated portfolio analytics

## Troubleshooting

If you encounter issues:

1. Check that your Supabase project is active
2. Verify your environment variables are correct
3. Ensure RLS policies are properly configured
4. Check the browser console for any JavaScript errors
5. Review the Supabase logs in your dashboard
