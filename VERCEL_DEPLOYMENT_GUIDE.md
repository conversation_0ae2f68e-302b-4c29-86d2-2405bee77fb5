# 🚀 Vercel Python API Deployment Guide

## 📁 **File Structure**
Your project now has this structure:
```
your-project/
├── api/                    # Python serverless functions
│   ├── quote.py           # Stock quotes endpoint
│   ├── indices.py         # Market indices endpoint
│   └── health.py          # Health check endpoint
├── src/                   # Your React app
├── requirements.txt       # Python dependencies
├── vercel.json           # Vercel configuration
└── package.json          # Node.js dependencies
```

## 🔧 **Setup Steps**

### 1. **Environment Variables (Optional)**
Add to your Vercel dashboard or `.env.local`:
```bash
# Optional: Enable microservice for all users
VITE_USE_MICROSERVICE=true

# Optional: Gradual rollout (0-100%)
VITE_MICROSERVICE_ROLLOUT=50

# Optional: Performance logging
VITE_ENABLE_PERF_LOGGING=true
```

### 2. **Deploy to Vercel**
```bash
# If you haven't already
npm install -g vercel

# Deploy (from your project root)
vercel --prod
```

### 3. **Test Your API Endpoints**
After deployment, test these endpoints:

```bash
# Health check
curl https://your-app.vercel.app/api/health

# Single stock quote
curl https://your-app.vercel.app/api/quote/RELIANCE.NS

# Indian market indices
curl https://your-app.vercel.app/api/indices

# Batch quotes (POST request)
curl -X POST https://your-app.vercel.app/api/quote \
  -H "Content-Type: application/json" \
  -d '{"symbols": ["RELIANCE.NS", "TCS.NS", "HDFCBANK.NS"]}'
```

## 📊 **Expected Performance**

| Endpoint | Expected Response Time | Cache Duration |
|----------|----------------------|----------------|
| Single Quote | 1-3 seconds | None (live data) |
| Batch Quotes | 2-5 seconds | None (live data) |
| Market Indices | 2-4 seconds | None (live data) |
| Health Check | 100-200ms | N/A |

## 🔄 **Gradual Migration Strategy**

### Phase 1: Test with 10% of users
```bash
# Set in Vercel environment variables
VITE_MICROSERVICE_ROLLOUT=10
```

### Phase 2: Increase to 50% if successful
```bash
VITE_MICROSERVICE_ROLLOUT=50
```

### Phase 3: Full rollout
```bash
VITE_USE_MICROSERVICE=true
```

## 🐛 **Troubleshooting**

### **API Not Working?**
1. Check Vercel function logs in dashboard
2. Verify `requirements.txt` is in project root
3. Ensure `vercel.json` has Python runtime config

### **CORS Issues?**
- CORS headers are already configured in the Python functions
- If issues persist, check Vercel dashboard settings

### **Slow Performance?**
- Vercel cold starts can take 1-2 seconds
- Consider upgrading to Vercel Pro for better performance
- Monitor function execution time in Vercel dashboard

### **Python Dependencies Issues?**
```bash
# Test locally first
pip install -r requirements.txt
python api/health.py  # Won't work locally, but checks imports
```

## 🎯 **Next Steps**

1. **Deploy and test** the API endpoints
2. **Enable gradual rollout** with `VITE_MICROSERVICE_ROLLOUT=10`
3. **Monitor performance** in browser dev tools
4. **Scale up** rollout percentage based on results
5. **Consider Vercel Pro** if you need better performance

## 💡 **Pro Tips**

- **Monitor Vercel Dashboard**: Check function execution times and errors
- **Use Browser Dev Tools**: Compare network timing between old and new APIs
- **Test Edge Cases**: Try invalid symbols, network failures, etc.
- **Consider Caching**: For production, consider adding Redis via Upstash

## 🔗 **Useful Links**

- [Vercel Python Functions Docs](https://vercel.com/docs/functions/serverless-functions/runtimes/python)
- [yfinance Documentation](https://pypi.org/project/yfinance/)
- [Vercel Environment Variables](https://vercel.com/docs/concepts/projects/environment-variables)
