# 📊 Free Chart Data Alternatives for Stock Charts

## 🎯 Current Solution: Mock Data Fallback

✅ **Implemented**: Charts now show demo data when Finnhub free tier doesn't provide historical data
- Realistic price movements based on current stock price
- Volume data simulation
- Clear "Demo Data" indicator
- All chart types work (line, area, candlestick)

## 🆓 Free Alternatives for Real Historical Data

### 1. **Alpha Vantage** (Recommended)
- **Free Tier**: 25 requests/day, 5 requests/minute
- **Historical Data**: ✅ Yes (daily, weekly, monthly)
- **Intraday Data**: ✅ Yes (1min, 5min, 15min, 30min, 60min)
- **API**: REST API with JSON responses
- **Setup**: Get free API key at https://www.alphavantage.co/support/#api-key

```javascript
// Example API call
const API_KEY = 'your_alpha_vantage_key';
const symbol = 'AAPL';
const url = `https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol=${symbol}&apikey=${API_KEY}`;
```

### 2. **Yahoo Finance (via yfinance-like APIs)**
- **Free Tier**: Unlimited (unofficial)
- **Historical Data**: ✅ Yes
- **Intraday Data**: ✅ Yes
- **API**: Various third-party wrappers
- **Note**: Unofficial, may have rate limits

### 3. **IEX Cloud**
- **Free Tier**: 50,000 requests/month
- **Historical Data**: ✅ Yes
- **Intraday Data**: ✅ Yes (limited in free tier)
- **API**: Professional REST API
- **Setup**: https://iexcloud.io/

### 4. **Polygon.io**
- **Free Tier**: 5 requests/minute
- **Historical Data**: ✅ Yes (limited to 2 years)
- **Intraday Data**: ✅ Yes
- **API**: REST and WebSocket
- **Setup**: https://polygon.io/

### 5. **Quandl (now part of Nasdaq)**
- **Free Tier**: 50 requests/day
- **Historical Data**: ✅ Yes
- **Focus**: Economic and financial data
- **API**: REST API

## 🔧 Implementation Guide

### Option A: Alpha Vantage Integration (Recommended)

1. **Get API Key**: Register at https://www.alphavantage.co/support/#api-key
2. **Add to .env.local**:
   ```env
   VITE_ALPHA_VANTAGE_API_KEY=your_api_key_here
   ```
3. **Create Alpha Vantage client**:
   ```typescript
   // src/integrations/alphavantage/client.ts
   const ALPHA_VANTAGE_BASE_URL = 'https://www.alphavantage.co/query';
   const API_KEY = import.meta.env.VITE_ALPHA_VANTAGE_API_KEY;
   
   export const getStockCandles = async (symbol: string, interval: string) => {
     const response = await fetch(
       `${ALPHA_VANTAGE_BASE_URL}?function=TIME_SERIES_INTRADAY&symbol=${symbol}&interval=${interval}&apikey=${API_KEY}`
     );
     return response.json();
   };
   ```

### Option B: Hybrid Approach (Best of Both Worlds)

1. **Use Finnhub** for real-time quotes, company profiles, news
2. **Use Alpha Vantage** for historical chart data
3. **Keep mock data** as ultimate fallback

```typescript
// Enhanced hook with multiple data sources
export const useStockCandles = (symbol: string, timeframe: ChartTimeframe) => {
  // Try Alpha Vantage first
  const alphaVantageQuery = useQuery({
    queryKey: ['alpha-vantage-candles', symbol, timeframe],
    queryFn: () => getAlphaVantageCandles(symbol, timeframe),
    enabled: !!symbol,
    retry: 1,
  });
  
  // Fallback to Finnhub
  const finnhubQuery = useQuery({
    queryKey: ['finnhub-candles', symbol, timeframe],
    queryFn: () => getFinnhubCandles(symbol, timeframe),
    enabled: !!symbol && !alphaVantageQuery.data,
    retry: 1,
  });
  
  // Return first successful result or mock data
  return {
    data: alphaVantageQuery.data || finnhubQuery.data || generateMockData(symbol),
    isLoading: alphaVantageQuery.isLoading || finnhubQuery.isLoading,
    error: alphaVantageQuery.error || finnhubQuery.error,
  };
};
```

## 🚀 Quick Implementation Steps

1. **Choose your preferred API** (Alpha Vantage recommended)
2. **Get API key** from the provider
3. **Add API key** to your `.env.local`
4. **Create new client** in `src/integrations/[provider]/client.ts`
5. **Update useStockCandles hook** to use new provider
6. **Test with real data**

## 💡 Pro Tips

- **Rate Limiting**: Implement proper rate limiting for free tiers
- **Caching**: Cache historical data aggressively (it doesn't change)
- **Error Handling**: Always have fallbacks (mock data is perfect for this)
- **User Experience**: Show loading states and data source indicators
- **Cost Management**: Monitor API usage to stay within free limits

## 🎨 Current Mock Data Features

- ✅ Realistic price movements
- ✅ Volume simulation
- ✅ Multiple timeframes (1D, 1W, 1M, 3M)
- ✅ All chart types supported
- ✅ Based on current stock price
- ✅ Clear "Demo Data" indicator
- ✅ Smooth user experience

The mock data provides an excellent user experience while you decide on a real data provider!
