const express = require('express');
const cors = require('cors');
const axios = require('axios');
require('dotenv').config({ path: '.env.local' });

const app = express();
const PORT = process.env.PROXY_PORT || 3001;

// Enable CORS for all routes
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000', 'http://127.0.0.1:5173'],
  credentials: true
}));

app.use(express.json());

// Finnhub API proxy routes
const FINNHUB_BASE_URL = 'https://finnhub.io/api/v1';
const API_KEY = process.env.VITE_FINNHUB_API_KEY;

if (!API_KEY) {
  console.error('❌ VITE_FINNHUB_API_KEY not found in environment variables');
  process.exit(1);
}

// Rate limiting
let requestQueue = [];
let isProcessing = false;
const RATE_LIMIT_DELAY = 1000; // 1 second between requests

const processQueue = () => {
  if (isProcessing || requestQueue.length === 0) return;
  
  isProcessing = true;
  const { resolve, request } = requestQueue.shift();
  
  request()
    .then(resolve)
    .catch(resolve)
    .finally(() => {
      setTimeout(() => {
        isProcessing = false;
        processQueue();
      }, RATE_LIMIT_DELAY);
    });
};

const rateLimitedRequest = (requestFn) => {
  return new Promise((resolve) => {
    requestQueue.push({ resolve, request: requestFn });
    processQueue();
  });
};

// Specific Finnhub proxy endpoints
app.get('/api/finnhub/quote', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying quote request for symbol: ${req.query.symbol}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${FINNHUB_BASE_URL}/quote`, {
        params: queryParams,
        timeout: 10000
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error('❌ Quote proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

app.get('/api/finnhub/stock/profile2', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying profile request for symbol: ${req.query.symbol}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${FINNHUB_BASE_URL}/stock/profile2`, {
        params: queryParams,
        timeout: 10000
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error('❌ Profile proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

app.get('/api/finnhub/stock/candle', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying candle request for symbol: ${req.query.symbol}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${FINNHUB_BASE_URL}/stock/candle`, {
        params: queryParams,
        timeout: 10000
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error('❌ Candle proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

app.get('/api/finnhub/search', async (req, res) => {
  try {
    const queryParams = { ...req.query, token: API_KEY };
    console.log(`📡 Proxying search request for query: ${req.query.q}`);

    const response = await rateLimitedRequest(() =>
      axios.get(`${FINNHUB_BASE_URL}/search`, {
        params: queryParams,
        timeout: 10000
      })
    );

    res.json(response.data);
  } catch (error) {
    console.error('❌ Search proxy error:', error.message);
    res.status(500).json({ error: 'Proxy Error', message: error.message });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    apiKey: API_KEY ? 'Present' : 'Missing'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Finnhub Proxy Server running on http://localhost:${PORT}`);
  console.log(`🔑 API Key: ${API_KEY ? 'Loaded' : 'Missing'}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
});

module.exports = app;
